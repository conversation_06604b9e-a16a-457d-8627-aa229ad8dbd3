import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../shared/router.dart';

class Loginpage extends StatefulWidget {
  const Loginpage({super.key});

  @override
  State<Loginpage> createState() => _LoginpageState();
}

class _LoginpageState extends State<Loginpage> {
  @override
  Widget build(BuildContext context) {
    final providers = [EmailAuthProvider()];
    return Scaffold(
      backgroundColor: const Color.fromRGBO(255, 255, 255, 1),
      body: SignInScreen(
        providers: providers,
        showAuthActionSwitch: false,
        email: '<EMAIL>',
        actions: [
          AuthStateChangeAction<SignedIn>((context, state) {
            if (state.user != null) {
              context.go(homeRoute);
              setState(() {});
            }
            // else {
            //   const snackBar = SnackBar(content: Text("Wrong Credentials!!!"));
            //   ScaffoldMessenger.of(context).showSnackBar(snackBar);
            // }
          }),
        ],
      ),
    );
  }
}
