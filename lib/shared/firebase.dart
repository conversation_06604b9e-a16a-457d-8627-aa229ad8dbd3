import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:foodcorp_admin/shared/const.dart';

class FBAuth {
  static final FirebaseAuth auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final FirebaseFirestore fb = FirebaseFirestore.instance;

  // Core collections
  static final settings =
      fb.collection(testMode ? 'testSettings' : 'settings').doc('sets');
  static final users = fb.collection(testMode ? 'testUsers' : 'Users');
  static final newuserapplication =
      fb.collection(testMode ? 'testNewUserApplication' : 'newuserapplication');
  static final districtoffice =
      fb.collection(testMode ? 'testDistrictOffice' : 'DistrictOffice');

  // Financial collections
  static final records = fb.collection(testMode ? 'testRecords' : 'Records');
  static final expense = fb.collection(testMode ? 'testExpense' : 'Expense');
  static final recovery = fb.collection(testMode ? 'testRecovery' : 'Recovery');
  static final recoverymonthly =
      fb.collection(testMode ? 'testRecoveryMonthly' : 'RecoveryMonthly');
  static final usermonthly =
      fb.collection(testMode ? 'testUserMonthly' : 'UserMonthly');
  static final loan = fb.collection(testMode ? 'testLoan' : 'Loan');
  static final receipt = fb.collection(testMode ? 'testReceipt' : 'Receipt');
  static final societyYearly =
      fb.collection(testMode ? 'testSocietyYearly' : 'SocietyYearly');
  static final cash = fb.collection(testMode ? 'testCash' : 'Cash');

  // Reports and analytics
  static final transactions =
      fb.collection(testMode ? 'testTransactions' : 'transactions');
  static final subsidiaryledger =
      fb.collection(testMode ? 'testSubsidiaryLedger' : 'subsidiaryledger');
  static final userloandetails =
      fb.collection(testMode ? 'testUserLoanDetails' : 'userloandetails');
  static final profitandloss =
      fb.collection(testMode ? 'testProfitAndLoss' : 'profitandloss');
  static final trialBalance =
      fb.collection(testMode ? 'testTrialBalance' : 'trialBalance');
  static final tbEntries =
      fb.collection(testMode ? 'testTbEntries' : 'tbEntries');
  static final balancesheet =
      fb.collection(testMode ? 'testBalanceSheet' : 'balancesheet');
  static final profitDistribution =
      fb.collection(testMode ? 'testProfitDistribution' : 'profitDistribution');

  // Notifications
  static final notifications =
      fb.collection(testMode ? 'testNotifications' : 'notifications');
  static final customnotifications = fb
      .collection(testMode ? 'testCustomNotifications' : 'customnotifications');
  static final requests = fb.collection(testMode ? 'testRequests' : 'requests');
  static final polls = fb.collection(testMode ? 'testPolls' : 'polls');
  static final feedback = fb.collection(testMode ? 'testFeedback' : 'feedback');
}

class FBStorage {
  static final FirebaseStorage fbstore = FirebaseStorage.instance;
}

class FBFunctions {
  static final FirebaseFunctions ff = FirebaseFunctions.instance;
}
