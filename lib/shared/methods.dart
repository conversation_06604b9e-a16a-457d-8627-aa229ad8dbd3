// ignore_for_file: must_be_immutable, library_private_types_in_public_api, dead_code

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/shared/firebase.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();

final GlobalKey<ScaffoldMessengerState> snackbar2Key =
    GlobalKey<ScaffoldMessengerState>();
// showAppSnackBar(String message,
//         {SnackBarAction? action,
//         Duration duration = const Duration(milliseconds: 1500)}) =>
//     snackbar2Key.currentState?.showSnackBar(SnackBar(
//       content: Text(
//         message,
//         style: const TextStyle(color: Colors.white),
//       ),
//       action: action,
//       duration: duration,
//     ));

String getMonthName(int monthNumber) {
  const monthNames = [
    '', // index 0 is unused to align months with index (1 = Jan)
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (monthNumber >= 1 && monthNumber <= 12)
      ? monthNames[monthNumber]
      : 'Invalid Month';
}

String monthShortName(int month) {
  const names = [
    '',
    'JAN',
    'FEB',
    'MAR',
    'APR',
    'MAY',
    'JUN',
    'JUL',
    'AUG',
    'SEP',
    'OCT',
    'NOV',
    'DEC'
  ];
  return names[month];
}

dynamic showCtcAppSnackBar(BuildContext context, String message,
        {SnackBarAction? action,
        Duration duration = const Duration(milliseconds: 1500)}) =>
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(
        message,
        style: const TextStyle(color: Colors.white),
      ),
      action: action,
      duration: duration,
    ));

bool isValidEmail(String email) {
  String pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  RegExp regex = RegExp(pattern);

  return regex.hasMatch(email);
}

class HeaderTxt extends StatelessWidget {
  const HeaderTxt({super.key, required this.txt, this.overflow, this.twice});
  final String txt;
  final TextOverflow? overflow;
  final bool? twice;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: (twice ?? false) ? 2 : 1,
      child: Text(
        txt,
        style: TextStyle(fontWeight: FontWeight.w600, overflow: overflow),
      ),
    );
  }
}

class RecoveryHeaderTxt extends StatelessWidget {
  const RecoveryHeaderTxt({
    super.key,
    required this.txt,
    this.overflow,
    this.flex,
  });
  final String txt;
  final TextOverflow? overflow;
  final int? flex;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex ?? 1,
      child: Text(
        txt,
        style: TextStyle(fontWeight: FontWeight.w600, overflow: overflow),
      ),
    );
  }
}

class CustomTextfield extends StatelessWidget {
  final String text;
  final TextEditingController? controller;
  final Function(String)? onFieldSubmitted;
  final bool enabled;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validatorrr;
  final bool? filled;
  final Color? fillColor;

  const CustomTextfield(
      {super.key,
      this.validatorrr,
      required this.text,
      this.controller,
      this.filled,
      this.fillColor,
      this.onFieldSubmitted,
      required this.enabled,
      this.keyboardType});
  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: Axis.horizontal,
      children: [
        Expanded(
          // flex: 5,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                text,
                style: const TextStyle(fontSize: 15),
              ),
              TextFormField(
                validator: validatorrr,
                keyboardType: keyboardType,
                enabled: enabled,
                decoration: InputDecoration(
                    filled: filled,
                    fillColor: fillColor,
                    border: OutlineInputBorder(),
                    hintText: 'Enter text here',
                    enabled: false),
                controller: controller,
                // onSubmitted: onFieldSubmitted,
                onFieldSubmitted: onFieldSubmitted,
              ),
              const SizedBox(height: 20)
            ],
          ),
        ),
      ],
    );
  }
}

class CustomRecoverytextfield extends StatelessWidget {
  const CustomRecoverytextfield({
    super.key,
    // required this.controller,
    this.onchange,
    this.enabled,
    this.keyboardType,
    this.flex,
    this.flexw,
  });

  // final TextEditingController controller;
  final Function(String)? onchange;
  final bool? enabled;
  final TextInputType? keyboardType;
  final int? flex;
  final String? flexw;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex ?? 1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
                // border: Border.all(color: Colors.black)
                ),
            padding: const EdgeInsets.only(right: 15),
            height: 30,
            child: TextFormField(
              cursorColor: Colors.black,
              cursorHeight: 18,
              keyboardType: keyboardType,
              enabled: enabled,
              decoration: const InputDecoration(
                  contentPadding:
                      EdgeInsets.symmetric(vertical: 7, horizontal: 8),
                  border: OutlineInputBorder(
                      borderSide: BorderSide(
                    color: Colors.black,
                  )),
                  enabled: false),
              initialValue: flexw,
              // controller: controller,
              onChanged: onchange,
            ),
          ),
        ],
      ),
    );
  }
}

String generateRandomId(int length) {
  const chars =
      'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  Random rand = Random();

  String randomId =
      List.generate(length, (_) => chars[rand.nextInt(chars.length)]).join();

  return randomId;
}

String getName(String keyName) {
  switch (keyName) {
    case 'aadharName':
      return 'Aadhar Card';
    case 'bankacName':
      return 'Bank Account Details with IFSC';
    case 'officeidentityName':
      return 'Office Identity Card';
    case 'salarySlipName':
      return 'Latest Salary Slip';
      break;
    default:
      return '';
  }
}

class UserCustomTextField extends StatefulWidget {
  final String initialvalue;
  final String labeltext;
  // final TextEditingController? controller;
  final Function(String)? onFieldSubmitted;
  final bool enabled;
  final TextInputType? keyboardType;
  final FormFieldValidator<String>? validatorrr;

  const UserCustomTextField(
      {super.key,
      this.validatorrr,
      required this.initialvalue,
      // this.controller,
      this.onFieldSubmitted,
      required this.enabled,
      this.keyboardType,
      required this.labeltext});

  @override
  State<UserCustomTextField> createState() => _UserCustomTextFieldState();
}

class _UserCustomTextFieldState extends State<UserCustomTextField> {
  // TextEditingController ctrler = TextEditingController();
  // @override
  // void initState() {
  //   super.initState();
  //   ctrler.text = widget.initialvalue;
  // }

  @override
  Widget build(BuildContext context) {
    return Flex(
      direction: Axis.horizontal,
      children: [
        SizedBox(
          width: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                readOnly: true,
                // initialValue: widget.initialvalue,
                controller: TextEditingController(text: widget.initialvalue),
                style: TextStyle(color: Colors.black),
                maxLines: 1,
                validator: widget.validatorrr,
                keyboardType: widget.keyboardType,
                enabled: widget.enabled,
                decoration: InputDecoration(
                  alignLabelWithHint: true,
                  label: Text(widget.labeltext),
                  border: UnderlineInputBorder(),
                  // hintText: hinttext,
                  // hintStyle: TextStyle(color: Colors.black87),
                  enabled: false,
                ),
                onFieldSubmitted: widget.onFieldSubmitted,
              ),
              const SizedBox(height: 20)
            ],
          ),
        ),
      ],
    );
  }
}

class CustomCard extends StatefulWidget {
  const CustomCard({
    super.key,
    required this.dataTexxt,
    this.color,
  });

  final String dataTexxt;
  final Color? color;

  @override
  State<CustomCard> createState() => _CustomCardState();
}

class _CustomCardState extends State<CustomCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 170,
        width: 150,
        child: Card(
            color: Color.fromARGB(255, 167, 243, 203),
            elevation: 0,
            shape: ContinuousRectangleBorder(
                borderRadius: BorderRadius.circular(28)),
            child: Padding(
                padding: EdgeInsets.symmetric(vertical: 40),
                child: Column(
                  children: [
                    Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          margin: EdgeInsets.only(right: 8),
                          child: Icon(
                            Icons.circle,
                            color: widget.color,
                            size: 10,
                          ),
                        ),
                        Text(widget.dataTexxt,
                            style: TextStyle(
                                fontWeight: FontWeight.w700, fontSize: 16)),
                      ],
                    ),
                  ],
                ))));
  }
}

class UserCustomCard extends StatefulWidget {
  UserCustomCard(
      {super.key,
      this.texxt,
      required this.dataTexxt,
      this.color,
      this.extraTexxt});

  final String? texxt;
  final String dataTexxt;
  final Color? color;
  String? extraTexxt;

  @override
  State<UserCustomCard> createState() => _UserCustomCardState();
}

class _UserCustomCardState extends State<UserCustomCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 170,
      width: 200,
      child: Card(
        color: const Color.fromARGB(255, 167, 243, 203),
        elevation: 0,
        shape:
            ContinuousRectangleBorder(borderRadius: BorderRadius.circular(28)),
        child: Padding(
          padding: const EdgeInsets.all(13.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              const SizedBox(width: 5),
              Align(
                alignment: Alignment.topLeft,
                child: Row(
                  children: [
                    Text(
                      widget.texxt ?? "",
                      style: const TextStyle(
                          fontSize: 13, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 6),
              Center(
                  child: Text(
                widget.dataTexxt,
                style: const TextStyle(fontSize: 18),
              )),
              const SizedBox(height: 12),
              Text(widget.extraTexxt ?? "")
            ],
          ),
        ),
      ),
    );
  }
}

class DbCustomCard extends StatefulWidget {
  DbCustomCard(
      {super.key,
      required this.iconn,
      required this.texxt,
      required this.dataTexxt,
      required this.color,
      this.extraTexxt});

  final IconData? iconn;
  final String texxt;
  final String dataTexxt;
  final Color color;
  String? extraTexxt;

  @override
  _DbCustomCard createState() => _DbCustomCard();
}

class _DbCustomCard extends State<DbCustomCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 170,
      width: 150,
      child: Card(
        color: widget.color,
        elevation: 0,
        shape:
            ContinuousRectangleBorder(borderRadius: BorderRadius.circular(28)),
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            children: [
              const SizedBox(width: 5),
              Align(
                alignment: Alignment.topLeft,
                child: Row(
                  children: [
                    Icon(widget.iconn),
                    const SizedBox(width: 5),
                    Text(
                      widget.texxt,
                      // "Total Attendance :",
                      style: const TextStyle(
                          fontSize: 13, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),
              Center(
                  child: Text(
                widget.dataTexxt,
                // "${ctrl.record?.totalAttendance ?? 0}",
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 32),
              )),
              const SizedBox(height: 12),
              Text(widget.extraTexxt ?? "")
            ],
          ),
        ),
      ),
    );
  }
}

List<int> getDynamicMonthNumbers() {
  final currentMonth = DateTime.now().month;
  final endMonth =
      (currentMonth + 1) > 12 ? (currentMonth + 1) - 12 : (currentMonth + 1);
  const startMonth = 4; // April

  if (startMonth <= endMonth) {
    return List.generate(endMonth - startMonth,
        (i) => startMonth + i); //increase or decrease month from here
  } else {
    return [
      ...List.generate(12 - startMonth + 1, (i) => startMonth + i),
      ...List.generate(endMonth, (i) => i + 1),
    ];
  }
}
