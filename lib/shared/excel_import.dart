// ignore_for_file: avoid_print

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/recovery_monthly_model.dart';
import 'package:foodcorp_admin/models/society_yearlyrecord_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/models/user_monthlyrecord_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:get/get.dart';

Future<void> importExcelData(
    {required List<List<dynamic>> cleanedRows,
    required int customYear,
    required int customMonth}) async {
  print("started excell import");

  try {
    final skippedUsers = <Map<String, dynamic>>{};
    final Map<String, SocietyYearlyRecordModel> societyYearlyAggregates = {};
    WriteBatch batch = FirebaseFirestore.instance.batch();
    int opCount = 0;

    print('📥 Starting import for ${cleanedRows.length} rows '
        '(Year: $customYear, Month: $customMonth)');

    final settingsSnap = await FBFireStore.settings.get();
    num currentAppNo = (settingsSnap.data()?['applicationNo'] ?? 0);

    Map<String, num> userTotalSubsCache = {};

    for (int i = 0; i < cleanedRows.length; i++) {
      final row = cleanedRows[i];

      print('\n➡️ Processing row ${i + 1}/${cleanedRows.length}');

      final rawCpf = row.length > 3 ? row[3] : null;
      final num? cpfNoParsed =
          rawCpf is num ? rawCpf : num.tryParse(rawCpf.toString() ?? '');
      if (cpfNoParsed == null) {
        skippedUsers.add({
          'reason': 'Invalid CPF No',
          'employeeNo': row[1],
          'cpfNo': row[3],
          'email': row[8],
        });
        continue;
      }

      final baseUser = UserModel.fromExcelRow(row, cpfNoParsed.toInt());
      print('   👤 User: ${baseUser.name} (CPF: ${baseUser.cpfNo})');

      if (baseUser.email == null ||
          baseUser.email!.trim().isEmpty ||
          baseUser.email == 'RESIGNED') {
        print('   ⚠️ Skipping: Missing/Resigned email (${baseUser.email})');
        skippedUsers.add({
          'reason': 'Missing or Resigned Email',
          'employeeNo': baseUser.employeeNo,
          'cpfNo': baseUser.cpfNo,
          'email': baseUser.email!.toLowerCase(),
        });
        continue;
      }

      final officeName =
          baseUser.districtoffice.toString().trim().toUpperCase();
      print('   🏢 Looking up DO: $officeName');
      final disoffSnap = await FBFireStore.districtoffice
          .where('name', isEqualTo: officeName)
          .limit(1)
          .get();
      if (disoffSnap.docs.isEmpty) {
        skippedUsers.add({
          'reason': 'District Office Not Found',
          'employeeNo': baseUser.employeeNo,
          'cpfNo': baseUser.cpfNo,
          'email': baseUser.email!.toLowerCase(),
        });
        continue;
      }
      // else {
      //   print('   ✅ Found DO: ${disoffSnap.docs.first.id}');
      //   String distOfficeId = disoffSnap.docs.first.id;
      //   // Assign ID to user model field
      //   baseUser.districtoffice = distOfficeId;
      // }
      final disoff = disoffSnap.docs.first.id;
      print('   ✅ Found DO: $disoff');

      // --- Updated logic starts here ---

      final emailTrimmed = baseUser.email!.trim().toLowerCase();

      String? resolvedUid;
      bool hasAuthUser = false;

      try {
        // 1) Get canonical UID from Auth via callable Cloud Function
        final getUidResp = await FBFunctions.ff
            .httpsCallable('getUserUidByEmail')
            .call({'email': emailTrimmed});
        resolvedUid = getUidResp.data['uid'] as String;
        hasAuthUser = true;

        // 2) Delete any stray Firestore user docs with this email but mismatched doc IDs
        final dupSnap = await FBFireStore.users
            .where('email', isEqualTo: emailTrimmed)
            .get();

        for (final doc in dupSnap.docs) {
          if (doc.id != resolvedUid) {
            await FBFireStore.users.doc(doc.id).delete();
            print('🗑️ Deleted stray Firestore doc: ${doc.id}');
          }
        }
        print('   🔑 Found existing Auth UID: $resolvedUid');
      } catch (e) {
        // No Auth user found, fallback to CPF lookup and new user creation
        print('   ℹ️ No Auth user found, will create...');
        hasAuthUser = false;

        final cpfSnap = await FBFireStore.users
            .where('cpfNo', isEqualTo: cpfNoParsed.toInt())
            .limit(1)
            .get();
        resolvedUid = cpfSnap.docs.isEmpty
            ? FBFireStore.users.doc().id
            : cpfSnap.docs.first.id;
      }

      // If no Auth user yet, create one
      if (!hasAuthUser) {
        final funcName = testMode ? 'testCreateUser' : 'createUser';
        try {
          final resp = await FBFunctions.ff.httpsCallable(funcName).call({
            'email': baseUser.email,
            'password': baseUser.password,
            'name': baseUser.name,
            'cpfNo': baseUser.cpfNo,
            'districtoffice': disoffSnap.docs.first.id,
            'phonenumber': baseUser.phoneNo,
          });
          if (resp.data['success'] != true) {
            throw Exception('User creation failed');
          } else {
            resolvedUid = resp.data['uid'];
            hasAuthUser = true;
          }
        } catch (_) {
          print('   ❌ Failed to create Auth user');
          skippedUsers.add({
            'reason': 'Auth user creation failed',
            'employeeNo': baseUser.employeeNo,
            'cpfNo': baseUser.cpfNo,
            'email': baseUser.email,
          });
          continue;
        }
      }

      // Construct UserModel with resolved UID as docId
      final userModel = UserModel(
        docId: resolvedUid!,
        createdAt: Timestamp.now().toDate(),
        name: baseUser.name,
        cpfNo: baseUser.cpfNo,
        employeeNo: baseUser.employeeNo,
        currentAddress: baseUser.currentAddress,
        permanentAddress: baseUser.permanentAddress,
        districtoffice: disoff,
        phoneNo: baseUser.phoneNo,
        email: baseUser.email,
        bankAcName: baseUser.bankAcName,
        bankName: baseUser.bankName,
        bankAcNo: baseUser.bankAcNo,
        ifscCode: baseUser.ifscCode,
        totalStLoans: baseUser.totalStLoans,
        stLoansDue: baseUser.stLoansDue,
        totalStIntPaid: baseUser.totalStIntPaid,
        totalLtLoans: baseUser.totalLtLoans,
        ltLoansDue: baseUser.ltLoansDue,
        totalLtIntPaid: baseUser.totalLtIntPaid,
        totalSubs: baseUser.totalSubs,
        totalShares: baseUser.totalShares,
        approved: baseUser.approved,
        documents: baseUser.documents,
        password: baseUser.password,
        archived: baseUser.archived,
        subsIntPayoutStatus: baseUser.subsIntPayoutStatus,
        dividendPayoutStatus: baseUser.dividendPayoutStatus,
        settlement: baseUser.settlement,
        totalSubsInt: baseUser.totalSubsInt,
        totalDivident: baseUser.totalDivident,
        registrationDate: Timestamp.now().toDate(),
        userPrevoiusMonthlyRecord: baseUser.userPrevoiusMonthlyRecord,
        userPrevoiusYearlyRecord: baseUser.userPrevoiusYearlyRecord,
        momento: baseUser.momento,
        nomineeName: baseUser.nomineeName,
        nomineeRelation: baseUser.nomineeRelation,
        obSubs: baseUser.obSubs,
        obShares: baseUser.obShares,
        obLt: baseUser.obLt,
        obSt: baseUser.obSt,
        isActive: baseUser.isActive,
      );

      final userDocRef = FBFireStore.users.doc(resolvedUid);

      final userDocSnap = await userDocRef.get();

      final currentSubs = num.tryParse(row[28]?.toString() ?? '0') ?? 0;
      final obSubsValue = num.tryParse(row[21]?.toString() ?? '0') ?? 0;

      num updatedTotalSubs;

      if (customMonth == 4) {
        // For April, totalSubs = obSubs + subs
        updatedTotalSubs = obSubsValue + currentSubs;
        print('   📊 April: obSubs: $obSubsValue, currentSubs: $currentSubs');
      } else {
        // For other months, totalSubs = previous stored totalSubs + current subs
        if (userTotalSubsCache.containsKey(resolvedUid)) {
          updatedTotalSubs = userTotalSubsCache[resolvedUid]! + currentSubs;
          print('   📊 Cached totalSubs: ${userTotalSubsCache[resolvedUid]}');
        } else {
          final prevSnapshot = await FBFireStore.users.doc(resolvedUid).get();
          final prevTotalSubs = prevSnapshot.exists
              ? (prevSnapshot.data()?['totalSubs'] ?? 0)
              : 0;
          updatedTotalSubs = prevTotalSubs + currentSubs;
          print('   📊 Fetched totalSubs: $prevTotalSubs');
        }
      }

      userTotalSubsCache[resolvedUid] = updatedTotalSubs;
      print('   📊 Updated totalSubs: $updatedTotalSubs');

      final userJsonData = userModel.toJson()
        ..['docId'] = resolvedUid
        ..['totalSubs'] = updatedTotalSubs;

      if (userDocSnap.exists) {
        batch.set(
          userDocRef,
          userJsonData,
          SetOptions(merge: true),
        );
      } else {
        batch.set(
          userDocRef,
          userJsonData,
        );
      }

      // num existingTotalSubs = 0;

      // if (userTotalSubsCache.containsKey(resolvedUid)) {
      //   existingTotalSubs = userTotalSubsCache[resolvedUid]!;
      //   print('   📊 Cached totalSubs: $existingTotalSubs');
      // } else {
      //   final userSnapshot = await userDocRef.get();
      //   if (userSnapshot.exists) {
      //     existingTotalSubs = userSnapshot.data()?['totalSubs'] ?? 0;
      //     userTotalSubsCache[resolvedUid] = existingTotalSubs;
      //     print('   📊 Fetched totalSubs: $existingTotalSubs');
      //   }
      // }

      final currentMonthSubs = num.tryParse(row[28]?.toString() ?? '0') ?? 0;
      print('   📊 Current month subs: $currentMonthSubs');

      // final updatedTotalSubs = existingTotalSubs + currentMonthSubs;

      // userTotalSubsCache[resolvedUid] = updatedTotalSubs;
      // print('   📊 Updated totalSubs: $updatedTotalSubs');

      // Map<String, dynamic> userJsonData = userModel.toJson()
      //   ..['docId'] = resolvedUid
      //   ..['totalSubs'] = updatedTotalSubs;

      print(
          '   📄 Writing Firestore user doc... (merge: ${userDocSnap.exists})');

      // Handle March month opening balance updates for next financial year

      // if (customMonth == 3) {
      //   // March month: Current closing balances become next year's opening balances
      //   final ltClosingBalance =
      //       num.tryParse(row.length > 35 ? row[35]?.toString() ?? '0' : '0') ??
      //           0;
      //   final stClosingBalance =
      //       num.tryParse(row.length > 36 ? row[36]?.toString() ?? '0' : '0') ??
      //           0;

      //   final finalObSubs = userModel.totalSubs ??
      //       0; // Current total subs becomes next year's opening
      //   final finalObShares = userModel.totalShares ??
      //       0; // Current total shares becomes next year's opening
      //   final finalObLt =
      //       ltClosingBalance; // Current LT closing becomes next year's opening
      //   final finalObSt =
      //       stClosingBalance; // Current ST closing becomes next year's opening

      //   // Update opening balance fields for March month
      //   userJsonData['obSubs'] = finalObSubs;
      //   userJsonData['obShares'] = finalObShares;
      //   userJsonData['obLt'] = finalObLt;
      //   userJsonData['obSt'] = finalObSt;

      //   print('🔄 March Excel Import: Setting opening balances for next FY');
      //   print(
      //       '   📊 User: ${userModel.name}, obSubs: $finalObSubs, obShares: $finalObShares, obLt: $finalObLt, obSt: $finalObSt');
      // }

      // if (userDocSnap.exists) {
      //   batch.set(
      //     userDocRef,
      //     userJsonData,
      //     SetOptions(merge: true),
      //   );
      // } else {
      //   batch.set(
      //     userDocRef,
      //     userJsonData,
      //   );
      // }
      // opCount++;

      print('   💳 Checking loans for ${baseUser.name}...');

      // The rest of your original batch operations — loans, recovery, monthly, yearly aggregates —

      // final settingsSnap = await FBFireStore.settings.get();

      // num currentAppNo = (settingsSnap.data()?['applicationNo'] ?? 0);

      final num ltTotalLoanAmt = num.tryParse(row[17]?.toString() ?? '') ?? 0;
      final num stTotalLoanAmt = num.tryParse(row[13]?.toString() ?? '') ?? 0;

      final ltInstAmt = num.tryParse(
              Get.find<HomeCtrl>().settings?.defaultLtinstallAmt ?? '0') ??
          0;
      final stInstAmt = num.tryParse(
              Get.find<HomeCtrl>().settings?.defaultStinstallAmt ?? '0') ??
          0;

      final ltShare = (ltTotalLoanAmt * 10) / 100;
      final stShare = (stTotalLoanAmt * 10) / 100;

      // final startOfMonth = DateTime(customYear, customMonth, 1);
      // final startOfNextMonth = DateTime(customYear, customMonth + 1, 1);

      final ltSnap = await FBFireStore.loan
          .where('uid', isEqualTo: resolvedUid)
          .where('loanType', isEqualTo: LoanTypes.longTerm.toString())
          .where('isSettled', isEqualTo: false)
          .where('rejectionDate', isEqualTo: null)
          .where('isNew', isEqualTo: false)
          .limit(1)
          .get();

      print("ltSnap.docs.isEmpty : ${ltSnap.docs.length}");

      if (ltTotalLoanAmt > 0 && ltSnap.docs.isEmpty) {
        currentAppNo++;

        final ref = FBFireStore.loan.doc();
        batch.set(
          ref,
          Loan(
            docId: ref.id,
            uid: resolvedUid,
            createdAt: Timestamp.now().toDate(),
            totalLoanAmt: ltTotalLoanAmt,
            appliedLoanAmt: ltTotalLoanAmt,
            totalLoanPaid: num.tryParse(row[18]?.toString() ?? '') ?? 0,
            totalLoanDue: num.tryParse(row[19]?.toString() ?? '') ?? 0,
            loanType: LoanTypes.longTerm,
            isSettled: false,
            appliedOn: Timestamp.now().toDate(),
            approvedOn: Timestamp.now().toDate(),
            applicationNo: currentAppNo,
            monthlyInstallmentAmt: ltInstAmt,
            share: ltShare,
            totalInterestPaid: num.tryParse(row[20]?.toString() ?? '') ?? 0,
            bAcNo: num.tryParse(row[11]?.toString() ?? '') ?? 0,
            appliedLoanAmtinWords: null,
            bSign: null,
            designation: null,
            isNew: false,
            loanReason: null,
            pdfString: null,
            rejectionDate: null,
            rejectionReason: null,
            sAcNo1: null,
            sAcNo2: null,
            surityName1: null,
            surityName2: null,
            loanPreClosureReason: null,
            processedOn: null,
            settledOn: null,
          ).toJson(),
        );
        opCount++;
      }

      final stSnap = await FBFireStore.loan
          .where('uid', isEqualTo: resolvedUid)
          .where('loanType', isEqualTo: LoanTypes.emergencyLoan.toString())
          // .where('createdAt',
          //     isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          // .where('createdAt', isLessThan: Timestamp.fromDate(startOfNextMonth))
          .where('isSettled', isEqualTo: false)
          .where('rejectionDate', isEqualTo: null)
          .where('isNew', isEqualTo: false)
          .limit(1)
          .get();

      print("stSnap.docs.isEmpty : ${stSnap.docs.length}");

      if (stTotalLoanAmt > 0 && stSnap.docs.isEmpty) {
        currentAppNo++;

        final ref = FBFireStore.loan.doc();

        batch.set(
          ref,
          Loan(
            docId: ref.id,
            uid: resolvedUid,
            createdAt: Timestamp.now().toDate(),
            totalLoanAmt: stTotalLoanAmt,
            appliedLoanAmt: stTotalLoanAmt,
            totalLoanPaid: num.tryParse(row[14]?.toString() ?? '') ?? 0,
            totalLoanDue: num.tryParse(row[15]?.toString() ?? '') ?? 0,
            loanType: LoanTypes.emergencyLoan,
            isSettled: false,
            appliedOn: Timestamp.now().toDate(),
            approvedOn: Timestamp.now().toDate(),
            applicationNo: currentAppNo,
            monthlyInstallmentAmt: stInstAmt,
            share: stShare,
            totalInterestPaid: num.tryParse(row[16]?.toString() ?? '') ?? 0,
            bAcNo: num.tryParse(row[11]?.toString() ?? '') ?? 0,
            appliedLoanAmtinWords: null,
            bSign: null,
            designation: null,
            isNew: false,
            loanReason: null,
            pdfString: null,
            rejectionDate: null,
            rejectionReason: null,
            sAcNo1: null,
            sAcNo2: null,
            surityName1: null,
            surityName2: null,
            loanPreClosureReason: null,
            processedOn: null,
            settledOn: null,
          ).toJson(),
        );
        opCount++;
      }

      // batch.update(FBFireStore.settings, {'applicationNo': currentAppNo});

      final recSnap = await FBFireStore.recoverymonthly
          .where('doId', isEqualTo: disoff)
          .where('selectedyear', isEqualTo: customYear)
          .where('selectedmonth', isEqualTo: customMonth)
          .limit(1)
          .get();

      final recDocId = recSnap.docs.isEmpty
          ? FBFireStore.recoverymonthly.doc().id
          : recSnap.docs.first.id;

      final baseRec = RecoveryMonthlyModel.fromExcelRow(row);
      final recoveryModel = RecoveryMonthlyModel(
        docId: recDocId,
        selectedyear: customYear,
        selectedmonth: customMonth,
        doId: disoff,
        obLt: baseRec.obLt,
        obSt: baseRec.obSt,
        loanPaidLt: baseRec.loanPaidLt,
        loanPaidst: baseRec.loanPaidst,
        loanTotal: baseRec.loanTotal,
        subs: baseRec.subs,
        ltInstallment: baseRec.ltInstallment,
        stInstallment: baseRec.stInstallment,
        interest: baseRec.interest,
        total: baseRec.total,
        ltCb: baseRec.ltCb,
        stCb: baseRec.stCb,
        installmentRec: baseRec.installmentRec,
        installmentRecDate: baseRec.installmentRecDate,
        subscriptionPaid: null,
        longTermInstalmentPaid: null,
        shortTermInstalmentPaid: null,
        longTermInterestPaid: null,
        shortTermInterestPaid: null,
        totalReceived: null,
      );

      batch.set(
        FBFireStore.recoverymonthly.doc(recDocId),
        recoveryModel.toJson(),
        SetOptions(merge: true),
      );
      opCount++;

      final umSnap = await FBFireStore.usermonthly
          .where('cpfNo', isEqualTo: cpfNoParsed)
          .where('selectedyear', isEqualTo: customYear)
          .where('selectedmonth', isEqualTo: customMonth)
          .limit(1)
          .get();

      final umDocId = umSnap.docs.isEmpty
          ? FBFireStore.usermonthly.doc().id
          : umSnap.docs.first.id;

      final baseUM =
          UserMonthlyRecordModel.fromExcelRow(row, cpfNoParsed.toInt());

      final userMonthlyModel = UserMonthlyRecordModel(
        docId: umDocId,
        cpfNo: cpfNoParsed.toInt(),
        districtoffice: disoff,
        selectedmonth: customMonth,
        selectedyear: customYear,
        name: baseUM.name,
        shareValue: baseUM.shareValue,
        dues: baseUM.dues,
        penalty: baseUM.penalty,
        installmentRec: baseUM.installmentRec,
        installmentRecDate: baseUM.installmentRecDate,
        subscriptionPaid: baseUM.subscriptionPaid,
        longTermInstalmentPaid: baseUM.longTermInstalmentPaid,
        shortTermInstalmentPaid: baseUM.shortTermInstalmentPaid,
        longTermInterestPaid: baseUM.longTermInterestPaid,
        shortTermInterestPaid: baseUM.shortTermInterestPaid,
        penaltyPaid: baseUM.penaltyPaid,
        isPaid: baseUM.isPaid,
        interest: baseUM.interest,
        loanPaidLt: baseUM.loanPaidLt,
        loanPaidst: baseUM.loanPaidst,
        loanTotal: baseUM.loanTotal,
        ltCb: baseUM.ltCb,
        ltInstallment: baseUM.ltInstallment,
        obLt: baseUM.obLt,
        obSt: baseUM.obSt,
        obSubs: baseUM.obSubs,
        stCb: baseUM.stCb,
        stInstallment: baseUM.stInstallment,
        status: baseUM.status,
        subs: baseUM.subs,
        total: baseUM.total,
        societySubsPayout: baseUM.societySubsPayout,
        societySharesPayout: baseUM.societySharesPayout,
      );

      batch.set(
        FBFireStore.usermonthly.doc(umDocId),
        userMonthlyModel.toJson(),
        SetOptions(merge: true),
      );
      opCount++;

      final baseSY = SocietyYearlyRecordModel.fromExcelRow(row);
      final yearKey = '$customYear';

      if (!societyYearlyAggregates.containsKey(yearKey)) {
        societyYearlyAggregates[yearKey] = SocietyYearlyRecordModel(
          docId: FBFireStore.societyYearly.doc().id,
          selectedyear: customYear,
          createdAt: Timestamp.now().toDate(),
          updatedAt: null,
          openingBalance: baseSY.openingBalance,
          closingBalance: baseSY.closingBalance,
          totalSubscription: baseSY.totalSubscription,
          intOnSubscription: baseSY.intOnSubscription,
          subscriptionInterestRate: baseSY.subscriptionInterestRate,
          totalLoanGiven: baseSY.totalLoanGiven,
          totalLoanReceived: baseSY.totalLoanReceived,
          ltLoanReceived: baseSY.ltLoanReceived,
          stLoanReceived: baseSY.stLoanReceived,
          ltLoanGiven: baseSY.ltLoanGiven,
          stLoanGiven: baseSY.stLoanGiven,
          ltIntAmt: baseSY.ltIntAmt,
          stIntAmt: baseSY.stIntAmt,
          totalIntAmt: baseSY.totalIntAmt,
          loanIntRate: baseSY.loanIntRate,
          totalPendingLoan: baseSY.totalPendingLoan,
          ltPendingLoan: baseSY.ltPendingLoan,
          stPendingLoan: baseSY.stPendingLoan,
          totalExpenses: baseSY.totalExpenses,
          expensesIds: baseSY.expensesIds,
          totalDividend: baseSY.totalDividend,
          totalMonthlyShareGiven: baseSY.totalMonthlyShareGiven,
          totalShareGiven: baseSY.totalShareGiven,
          monthlyDividend: baseSY.monthlyDividend,
          dividendRate: baseSY.dividendRate,
          cashBalance: baseSY.cashBalance,
        );
      } else {
        societyYearlyAggregates[yearKey] =
            societyYearlyAggregates[yearKey]!.mergeWith(baseSY);
      }

      if (opCount >= 450) {
        await batch.commit();
        print('✅ Committed $opCount operations (mid-loop)');
        batch = FirebaseFirestore.instance.batch();
        opCount = 0;
        await settleAndUpdateLoans(
          cleanedRows: cleanedRows,
          customYear: customYear,
          customMonth: customMonth,
        );
      }

      print('=-=-=-=-=-=-=-=--=-=-=-=-=-=-=-=-=-=-=-=-==-');
    }

    print('📊 Writing society yearly aggregates...');
    for (final entry in societyYearlyAggregates.entries) {
      final model = entry.value;
      final snap = await FBFireStore.societyYearly
          .where('selectedyear', isEqualTo: customYear
              // model.selectedyear
              )
          .limit(1)
          .get();
      final docId = snap.docs.isEmpty ? model.docId : snap.docs.first.id;
      batch.set(
        FBFireStore.societyYearly.doc(docId),
        model.toJson(),
        SetOptions(merge: true),
      );
      opCount++;
    }

    batch.update(FBFireStore.settings, {'applicationNo': currentAppNo});

    if (opCount > 0) {
      await batch.commit();
      print('✅ Final commit: $opCount operations');
      await settleAndUpdateLoans(
        cleanedRows: cleanedRows,
        customYear: customYear,
        customMonth: customMonth,
      );
    }

    if (skippedUsers.isNotEmpty) {
      print('❌ Skipped users: ${skippedUsers.length}');
      for (final user in skippedUsers) {
        print(
            '   - ${user['reason']} | CPF: ${user['cpfNo']} | ${user['email']}');
      }
    }
    print('✅ Import complete.');
  } catch (e) {
    debugPrint('⚠️ importExcelData error: $e');
  }
}

dynamic extractCellValue(dynamic cell) {
  if (cell == null) return null;
  if (cell is Data && cell.value != null) {
    final v = cell.value;
    if (v is TextCellValue) return v.value;
    if (v is IntCellValue) return v.value;
    if (v is DoubleCellValue) return v.value;
    if (v is BoolCellValue) return v.value;
    if (v is String) return v;
    return v.toString();
  }
  return cell?.toString().trim();
}

Future<void> settleAndUpdateLoans({
  required List<List<dynamic>> cleanedRows,
  required int customYear,
  required int customMonth,
}) async {
  print('🔄 Beginning settleAndUpdateLoans for $customYear-$customMonth');
  WriteBatch batch = FirebaseFirestore.instance.batch();

  int count = 0;
  for (final row in cleanedRows) {
    final cpfRaw = row.length > 3 ? row[3] : null;
    if (cpfRaw == null) continue;
    final cpf = int.tryParse(cpfRaw.toString()) ?? 0;
    if (cpf == 0) continue;

    count++;

    // Parse loan and payment data from row
    final stTotalLoanAmt = num.tryParse(row[13]?.toString() ?? '0') ?? 0;
    final stLoanPaid = num.tryParse(row[14]?.toString() ?? '0') ?? 0;
    final stClosing = num.tryParse(row[36]?.toString() ?? '0') ?? 0;
    final stInterest = num.tryParse(row[16]?.toString() ?? '0') ?? 0;

    final ltTotalLoanAmt = num.tryParse(row[17]?.toString() ?? '0') ?? 0;
    final ltLoanPaid = num.tryParse(row[18]?.toString() ?? '0') ?? 0;
    final ltClosing = num.tryParse(row[35]?.toString() ?? '0') ?? 0;
    final ltInterest = num.tryParse(row[20]?.toString() ?? '0') ?? 0;

    final userSnap =
        await FBFireStore.users.where('cpfNo', isEqualTo: cpf).limit(1).get();

    if (userSnap.docs.isEmpty) {
      print('⚠️ User with CPF $cpf not found, skipping');
      continue;
    }
    final userDoc = userSnap.docs.first;
    final userData = userDoc.data();

    // Query existing active loans
    final loanSnap = await FBFireStore.loan
        .where('uid', isEqualTo: userDoc.id)
        .where('isSettled', isEqualTo: false)
        .get();

    for (final loanDoc in loanSnap.docs) {
      final loanData = loanDoc.data();
      final loanType = loanData['loanType'];
      bool isClosed = false;

      num loanPaid = 0;
      num closingBalance = 0;
      num interestPaid = 0;

      if (loanType == LoanTypes.longTerm) {
        loanPaid = ltLoanPaid;
        closingBalance = ltClosing;
        interestPaid = ltInterest;
      } else if (loanType == LoanTypes.emergencyLoan) {
        loanPaid = stLoanPaid;
        closingBalance = stClosing;
        interestPaid = stInterest;
      }

      Map<String, dynamic> loanUpdate = {
        'totalLoanPaid': loanPaid,
        'totalLoanDue': closingBalance,
        'totalInterestPaid': interestPaid,
      };

      if (closingBalance == 0) {
        isClosed = true;
        loanUpdate['isSettled'] = true;
        loanUpdate['settledOn'] = Timestamp.fromDate(
          DateTime(customYear, customMonth, 1)
              .add(Duration(days: 31))
              .subtract(Duration(days: 1)),
        );
        loanUpdate['totalLoanDue'] = 0; // Ensure due is zero
      }

      batch.update(loanDoc.reference, loanUpdate);

      // Update user's aggregate fields
      Map<String, dynamic> userUpdate = {};

      if (isClosed) {
        if (loanType == LoanTypes.longTerm) {
          userUpdate['ltLoansDue'] = 0;
          userUpdate['totalLtLoans'] = ((userData['totalLtLoans'] ?? 0) -
                  (loanData['totalLoanDue'] ?? 0))
              .clamp(0, double.infinity);
          userUpdate['ltInterestPaid'] = ((userData['ltInterestPaid'] ?? 0) -
                  (loanData['totalInterestPaid'] ?? 0))
              .clamp(0, double.infinity);
        } else if (loanType == LoanTypes.emergencyLoan) {
          userUpdate['stLoansDue'] = 0;
          userUpdate['totalStLoans'] = ((userData['totalStLoans'] ?? 0) -
                  (loanData['totalLoanDue'] ?? 0))
              .clamp(0, double.infinity);
          userUpdate['stInterestPaid'] = ((userData['stInterestPaid'] ?? 0) -
                  (loanData['totalInterestPaid'] ?? 0))
              .clamp(0, double.infinity);
        }
      } else {
        if (loanType == LoanTypes.longTerm) {
          userUpdate['ltLoansDue'] = closingBalance;
          userUpdate['totalLtLoans'] = userData['totalLtLoans'] ?? 0;
          userUpdate['ltInterestPaid'] = interestPaid;
        } else if (loanType == LoanTypes.emergencyLoan) {
          userUpdate['stLoansDue'] = closingBalance;
          userUpdate['totalStLoans'] = userData['totalStLoans'] ?? 0;
          userUpdate['stInterestPaid'] = interestPaid;
        }
      }

      if (userUpdate.isNotEmpty) {
        batch.update(userDoc.reference, userUpdate);
      }
    }
  }

  await batch.commit();
  print('✅ Processed $count loan settlements');
}

// Future<void> settleAndUpdateLoans({
//   required List<List<dynamic>> cleanedRows,
//   required int customYear,
//   required int customMonth,
// }) async {
//   print('🔄 Beginning settleAndUpdateLoans for $customYear-$customMonth');
//   WriteBatch batch = FirebaseFirestore.instance.batch();
//   int count = 0;
//   for (final row in cleanedRows) {
//     final cpfNoRaw = row.length > 3 ? row[3] : null;
//     if (cpfNoRaw == null) continue;
//     final cpfNo = int.tryParse(cpfNoRaw.toString()) ?? 0;
//     if (cpfNo == 0) continue;
//     count++;
//     print('   🔹 Processing loan settlement for CPF: $cpfNo (row #$count)');
//     // Extract loan values here
//     final stTotalLoanAmt = num.tryParse(row[13]?.toString() ?? '0') ?? 0;
//     final stLoanPaidTillNow = num.tryParse(row[14]?.toString() ?? '0') ?? 0;
//     final stClosingBalance = num.tryParse(row[36]?.toString() ?? '0') ?? 0;
//     final stInterestPaid = num.tryParse(row[16]?.toString() ?? '0') ?? 0;
//     final ltTotalLoanAmt = num.tryParse(row[17]?.toString() ?? '0') ?? 0;
//     final ltLoanPaidTillNow = num.tryParse(row[18]?.toString() ?? '0') ?? 0;
//     final ltClosingBalance = num.tryParse(row[35]?.toString() ?? '0') ?? 0;
//     final ltInterestPaid = num.tryParse(row[20]?.toString() ?? '0') ?? 0;
//     final userSnap =
//         await FBFireStore.users.where('cpfNo', isEqualTo: cpfNo).limit(1).get();
//     if (userSnap.docs.isEmpty) {
//       print('      ⚠️ User not found for CPF $cpfNo, skipping loan update');
//       continue;
//     }
//     final userDoc = userSnap.docs.first;
//     final userData = userDoc.data();
//     final loanSnap = await FBFireStore.loan
//         .where('uid', isEqualTo: userDoc.id)
//         .where('isSettled', isEqualTo: false)
//         .get();
//     for (final loanDoc in loanSnap.docs) {
//       final loanData = loanDoc.data();
//       final loanType = loanData['loanType'];
//       print('         Processing loan ID: ${loanDoc.id} - Type: $loanType');
//       bool isClosed = false;
//       if (loanType == LoanTypes.longTerm) {
//         print('      Updating longTerm loan ${loanDoc.id} for user $cpfNo');
//         Map<String, dynamic> updateData = {
//           'totalLoanPaid': ltLoanPaidTillNow,
//           'totalLoanDue': ltClosingBalance,
//           'totalInterestPaid': ltInterestPaid,
//         };
//         if (ltClosingBalance == 0) {
//           print('         LongTerm loan balance is zero, marking as settled');
//           updateData['isSettled'] = true;
//           updateData['settledOn'] = Timestamp.fromDate(
//             DateTime(customYear, customMonth, 1)
//                 .add(Duration(days: 31))
//                 .subtract(Duration(days: 1)),
//           );
//           updateData['totalLoanDue'] = 0;
//         }
//         batch.update(loanDoc.reference, updateData);
//       } else if (loanType == LoanTypes.emergencyLoan) {
//         print('      Updating emergencyLoan ${loanDoc.id} for user $cpfNo');
//         Map<String, dynamic> updateData = {
//           'totalLoanPaid': stLoanPaidTillNow,
//           'totalLoanDue': stClosingBalance,
//           'totalInterestPaid': stInterestPaid,
//         };
//         if (stClosingBalance == 0) {
//           print('         ShortTerm loan balance is zero, marking as settled');
//           updateData['isSettled'] = true;
//           updateData['settledOn'] = Timestamp.fromDate(
//             DateTime(customYear, customMonth, 1)
//                 .add(Duration(days: 31))
//                 .subtract(Duration(days: 1)),
//           );
//           updateData['totalLoanDue'] = 0;
//         }
//         batch.update(loanDoc.reference, updateData);
//       }
//     }
//     print('      Updating user aggregate fields for $cpfNo');
//     batch.update(userDoc.reference, {
//       'totalStLoans': stTotalLoanAmt,
//       'stLoansDue': stClosingBalance,
//       'totalStIntPaid': stInterestPaid,
//       'totalLtLoans': ltTotalLoanAmt,
//       'ltLoansDue': ltClosingBalance,
//       'totalLtIntPaid': ltInterestPaid,
//     });
//   }
//   await batch.commit();
//   print('✅ Completed settleAndUpdateLoans with $count users processed');
// }
