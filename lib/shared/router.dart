import 'dart:async';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/error_page.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/views/DividendPayout/dividend_payout_page.dart';
import 'package:foodcorp_admin/views/Do%20office/dooffice.dart';
import 'package:foodcorp_admin/views/Feedback/feedback_page.dart';
import 'package:foodcorp_admin/views/Expenses/cash_history_page.dart';
import 'package:foodcorp_admin/views/Expenses/expenses.dart';
import 'package:foodcorp_admin/views/Loan/add_loan_page.dart';
import 'package:foodcorp_admin/views/Loan/loan.dart';
import 'package:foodcorp_admin/views/Loan/loan_details_page.dart';
import 'package:foodcorp_admin/views/Loan/loan_hdetails_page.dart';
import 'package:foodcorp_admin/views/Loan/loan_history_page.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/new_loan_application.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/rejected_loan.dart';
import 'package:foodcorp_admin/views/New_User_Application/new_user_application.dart';
import 'package:foodcorp_admin/views/New_User_Application/rejected_user.dart';
import 'package:foodcorp_admin/views/Notifications/poll_page.dart';
import 'package:foodcorp_admin/views/Notifications/polls_history_page.dart';
import 'package:foodcorp_admin/views/Notifications/tab_page.dart';
import 'package:foodcorp_admin/views/Recovery/recovery.dart';
import 'package:foodcorp_admin/views/Reports/Pages/balance_sheet_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/budget_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/cashbook_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/monthly_loan_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/monthly_share_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/monthly_subs_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/pnl_switch_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/register.dart';
import 'package:foodcorp_admin/views/Reports/Pages/yearly_loan_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/yearly_share_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/yearly_subs_page.dart';
import 'package:foodcorp_admin/views/Reports/reports.dart';
import 'package:foodcorp_admin/views/Reports/Pages/subsidiary_ledger_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/trial_balance_page.dart';
import 'package:foodcorp_admin/views/Settings/pin_page.dart';
import 'package:foodcorp_admin/views/SubsIntPayout/subs_int_payout_page.dart';
import 'package:foodcorp_admin/views/Update%20Request/update_request_history.dart';
import 'package:foodcorp_admin/views/Update%20Request/update_request_page.dart';
import 'package:foodcorp_admin/views/Users/<USER>';
import 'package:foodcorp_admin/views/Users/<USER>';
import 'package:foodcorp_admin/views/Users/<USER>';
import 'package:foodcorp_admin/views/Users/<USER>';
import 'package:foodcorp_admin/views/drawer/dashboard.dart';
import 'package:foodcorp_admin/views/Dashboard/dashboardpage.dart';
import 'package:foodcorp_admin/views/Settings/settings_page.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../auth/auth.dart';
import '../views/Users/<USER>';

const homeRoute = Routes.dashboard;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.signin,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  // routeHistory.add(state.uri.path);
  // if (isLoggedIn() && state.fullPath == Routes.auth) {
  //   return routeHistory.reversed. At(1);
  //   // return Routes.home;
  // }
  if (isLoggedIn()) {
    if (state.fullPath == Routes.signin) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().onInit();
      return null;
    }
  } else {
    return Routes.signin;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    ShellRoute(
        builder: (context, state, child) {
          if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
          return DashboardScreen(child: child);
        },
        routes: [
          GoRoute(
            path: Routes.dashboard,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: DashboardPage()),
          ),
          GoRoute(
            path: Routes.loanapplication,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: NewApplicationPage()),
          ),
          GoRoute(
            path: Routes.userapplication,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: NewUserApplication()),
          ),
          GoRoute(
            path: Routes.dooffice,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: DoOfficePage()),
          ),
          GoRoute(
            path: Routes.loan,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: LoanPage()),
          ),
          GoRoute(
            path: Routes.expense,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: ExpensesPage()),
          ),
          GoRoute(
            path: Routes.reports,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: ReportsPage()),
          ),
          GoRoute(
            path: Routes.users,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: UsersPage()),
          ),
          GoRoute(
            path: Routes.settings,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: SettingsPage()),
          ),
          GoRoute(
            path: Routes.recovery,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: RecoveryPage()),
          ),
          GoRoute(
            path: Routes.rejectedUser,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: RejectedUser()),
          ),
          GoRoute(
            path: Routes.rejectedLoan,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: RejectedApplicationPage()),
          ),
          GoRoute(
            path: Routes.archivedUser,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: ArchivedUserPage()),
          ),
          GoRoute(
            path: Routes.subsidiaryledger,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: SubsidiaryLedgerPage()),
          ),
          GoRoute(
            path: Routes.profitandloss,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: PnlSwitcherPage()),
          ),
          GoRoute(
              path: "${Routes.userloandetails}/:id",
              pageBuilder: (BuildContext context, GoRouterState state) {
                final id = state.pathParameters['id'];
                return NoTransitionPage(
                    child: UserLoanDetailsPage(
                  loanId: id == 'null' ? null : id,
                ));
              }),
          GoRoute(
            path: Routes.cashHistory,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: CashHistoryPage()),
          ),
          GoRoute(
            path: Routes.loanhistory,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: LoanHistoryPage());
            },
          ),
          GoRoute(
            path: Routes.notifications,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: TabPage());
            },
          ),
          GoRoute(
            path: Routes.polls, // Add this path in Routes class
            pageBuilder: (BuildContext context, GoRouterState state) {
              return const NoTransitionPage(child: PollPage());
            },
          ),
          GoRoute(
            path: Routes.pollshistory, // Add this path in Routes class
            pageBuilder: (BuildContext context, GoRouterState state) {
              return const NoTransitionPage(child: PollsHistoryPage());
            },
          ),
          GoRoute(
            path: Routes.balancesheet,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: BalanceSheetPage());
            },
          ),
          GoRoute(
            path: Routes.cashbook,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: CashbookPage());
            },
          ),
          // GoRoute(
          //   path: Routes.cashbalance,
          //   pageBuilder: (BuildContext context, GoRouterState state) {
          //     return NoTransitionPage(child: CashbalancePage());
          //   },
          // ),
          GoRoute(
            path: Routes.trialbalance,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: TrialbalancePage());
            },
          ),
          GoRoute(
            path: Routes.budget,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: BudgetPage());
            },
          ),
          GoRoute(
            path: Routes.yearlysubs,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: YearlySubsPage());
            },
          ),
          GoRoute(
            path: Routes.yearlyloan,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: YearlyLoanPage());
            },
          ),
          GoRoute(
            path: Routes.yearlyshare,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: YearlySharePage());
            },
          ),
          GoRoute(
            path: Routes.monthlysubs,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: MonthlySubsPage());
            },
          ),
          GoRoute(
            path: Routes.monthlyloan,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: MonthlyLoanPage());
            },
          ),
          GoRoute(
            path: Routes.monthlyshare,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: MonthlySharePage());
            },
          ),
          GoRoute(
            path: Routes.pinpage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: PinPage());
            },
          ),
          GoRoute(
            path: Routes.dividendpayout,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: DividendPayoutPage());
            },
          ),
          GoRoute(
            path: Routes.subsintpayout,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: SubsIntPayoutPage());
            },
          ),
          GoRoute(
            path: Routes.addloanpage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: AddLoanPage());
            },
          ),
          GoRoute(
            path: Routes.updaterequest,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: UpdateRequestPage());
            },
          ),
          GoRoute(
            path: Routes.updaterequesthistory,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: UpdateRequestHistory());
            },
          ),
          GoRoute(
            path: Routes.register,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: RegisterPage());
            },
          ),
          GoRoute(
            path: Routes.feedback,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return NoTransitionPage(child: FeedbackPage());
            },
          ),
          GoRoute(
              path: "${Routes.userDetails}/:id",
              pageBuilder: (BuildContext context, GoRouterState state) {
                final id = state.pathParameters['id'];
                return NoTransitionPage(
                    child: UserDetailsPage(
                  userdocId: id == 'null' ? null : id,
                ));
              }),
          GoRoute(
              path: "${Routes.loanDetails}/:id",
              pageBuilder: (BuildContext context, GoRouterState state) {
                final id = state.pathParameters['id'];
                return NoTransitionPage(
                  child: LoanDetailsPage(
                    loanId: id == 'null' ? null : id,
                  ),
                );
              }),
          GoRoute(
              path: "${Routes.loanhistorydetails}/:id",
              pageBuilder: (BuildContext context, GoRouterState state) {
                final id = state.pathParameters['id'];
                // final loan = state.extra as Loan;
                return NoTransitionPage(
                  child: LoanHdetailsPage(
                    loanId: id == 'null' ? null : id,
                  ),
                );
              }),
          GoRoute(
              path: "${Routes.adduserform}/:id",
              pageBuilder: (BuildContext context, GoRouterState state) {
                final id = state.pathParameters['id'];
                return NoTransitionPage(
                    child: AddUserformPage(
                  userdocId: id == 'null' ? null : id,
                ));
              }),
        ]),
    GoRoute(
      path: Routes.signin,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(
        child: Loginpage(),
      ),
    ),
  ];
}

class Routes {
  static const signin = '/signin';
  static const loanapplication = '/loanapplication';
  static const dashboard = '/dashboard';
  static const dooffice = '/dooffice';
  static const settings = '/settings';
  static const users = '/users';
  static const loan = '/loan';
  static const recovery = '/recovery';
  static const reports = '/reports';
  static const expense = '/expense';
  static const adduserform = '/adduserform';
  static const userapplication = '/userapplication';
  static const rejectedUser = '/rejectedUser';
  static const rejectedLoan = '/rejectedLoan';
  static const archivedUser = '/archiveduser';
  static const cashHistory = '/cashhistory';
  static const userDetails = '/userdetails';
  static const loanDetails = '/loandetails';
  static const subsidiaryledger = '/subsidiaryledger';
  static const profitandloss = '/profitandloss';
  static const userloandetails = '/userloandetails';
  static const loanhistory = '/loanhistory';
  static const loanhistorydetails = '/loanhistorydetails';
  static const notifications = '/notifications';
  static const polls = '/polls';
  static const pollshistory = '/pollshistory';
  static const balancesheet = '/balancesheet';
  static const cashbook = '/cashbook';
  static const cashbalance = '/cashbalance';
  static const trialbalance = '/trialbalance';
  static const budget = '/budget';
  static const yearlysubs = '/yearlysubs';
  static const yearlyloan = '/yearlyloan';
  static const yearlyshare = '/yearlyshare';
  static const monthlysubs = '/monthlysubs';
  static const monthlyloan = '/monthlyloan';
  static const monthlyshare = '/monthlyshare';
  static const pinpage = '/pinpage';
  static const dividendpayout = '/dividendpayout';
  static const subsintpayout = '/subsintpayout';
  static const addloanpage = '/addloanpage';
  static const updaterequest = '/updaterequest';
  static const updaterequesthistory = '/updaterequesthistory';
  static const register = '/register';
  static const feedback = '/feedback';
}
