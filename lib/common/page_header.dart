import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomHeaderButton extends StatelessWidget {
  const CustomHeaderButton({
    super.key,
    required this.onPressed,
    required this.buttonName,
  });

  final VoidCallback onPressed;
  final String? buttonName;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.black87,
        elevation: 0,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
      ),
      onPressed: onPressed,
      // icon: Icon(
      //   icon,
      //   size: 20,
      // ),
      label: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Text(buttonName ?? "", style: const TextStyle(fontSize: 14)),
      ),
    );
  }
}

class CustomSearchBarWidget extends StatelessWidget {
  const CustomSearchBarWidget({
    super.key,
    required this.searchController,
    required this.searchOnChanged,
  });

  final TextEditingController? searchController;
  final Function(String p1)? searchOnChanged;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 250),
      child: SizedBox(
        height: 45,
        child: TextFormField(
          controller: searchController,
          cursorHeight: 20,
          // searchOnChanged: (value) => Get.find<HomeCtrl>().update(),
          onChanged: searchOnChanged,
          decoration: InputDecoration(
            hoverColor: Colors.transparent,
            contentPadding: const EdgeInsets.symmetric(),
            fillColor: Colors.white,
            // fillColor: Colors.grey.shade100,
            filled: true,
            prefixIcon: const Icon(
              CupertinoIcons.search,
              size: 22,
              color: Colors.black,
            ),
            border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(7)),
            hintText: 'Search',
            hintStyle: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }
}
