import 'package:cloud_firestore/cloud_firestore.dart';

class ReceiptModel {
  final num subscriptionPaid;
  final num longTermInstalmentPaid;
  final num shortTermInstalmentPaid;
  final num longTermInterestPaid;
  final num shortTermInterestPaid;
  final num totalReceived;

  ReceiptModel({
    required this.subscriptionPaid,
    required this.longTermInstalmentPaid,
    required this.shortTermInstalmentPaid,
    required this.longTermInterestPaid,
    required this.shortTermInterestPaid,
    required this.totalReceived,
  });

  factory ReceiptModel.fromJson(Map<String, dynamic> json) {
    return ReceiptModel(
      subscriptionPaid: json['subscriptionPaid'],
      longTermInstalmentPaid: json['longTermInstalmentPaid'],
      shortTermInstalmentPaid: json['shortTermInstalmentPaid'],
      longTermInterestPaid: json['longTermInterestPaid'],
      shortTermInterestPaid: json['shortTermInterestPaid'],
      totalReceived: json['totalReceived'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      'totalReceived': totalReceived,
    };
  }

  // Convert from Firestore snapshot to Receipt object
  factory ReceiptModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;

    return ReceiptModel(
      subscriptionPaid: data['subscriptionPaid'],
      longTermInstalmentPaid: data['longTermInstalmentPaid'],
      shortTermInstalmentPaid: data['shortTermInstalmentPaid'],
      longTermInterestPaid: data['longTermInterestPaid'],
      shortTermInterestPaid: data['shortTermInterestPaid'],
      totalReceived: data['totalReceived'],
    );
  }

  // Convert Receipt object to Firestore document format
  Map<String, dynamic> toSnap() {
    return {
      'subscriptionPaid': subscriptionPaid,
      'longTermInstalmentPaid': longTermInstalmentPaid,
      'shortTermInstalmentPaid': shortTermInstalmentPaid,
      'longTermInterestPaid': longTermInterestPaid,
      'shortTermInterestPaid': shortTermInterestPaid,
      'totalReceived': totalReceived,
    };
  }
}
