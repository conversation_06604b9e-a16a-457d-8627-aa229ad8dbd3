import 'package:cloud_firestore/cloud_firestore.dart';

class YearlySubsModel {
  final DateTime? updatedAt;
  final num year;

  YearlySubsModel({
    required this.updatedAt,
    required this.year,
  });

  factory YearlySubsModel.fromSnapshot(Map<String, dynamic> snap) {
    return YearlySubsModel(
      updatedAt: (snap['updatedAt'] as Timestamp?)?.toDate(),
      year: snap['year'] ?? 0,
    );
  }

  Map<String, dynamic> toSnapshot() {
    return {
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'year': year,
    };
  }

  factory YearlySubsModel.fromJson(Map<String, dynamic> json) {
    return YearlySubsModel(
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
      year: json['year'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'updatedAt': updatedAt?.toIso8601String(),
      'year': year,
    };
  }
}

//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-

class MonthlySubsModel {
  final String month;
  final num subsPaid;
  final num totalSubsPaid;
  final num subsRec;
  final num totalSubsRec;
  final num obSubs;

  MonthlySubsModel({
    required this.month,
    required this.subsPaid,
    required this.totalSubsPaid,
    required this.subsRec,
    required this.totalSubsRec,
    required this.obSubs,
  });

  factory MonthlySubsModel.fromSnapshot(Map<String, dynamic> snap) {
    return MonthlySubsModel(
      month: snap['month'] ?? '',
      subsPaid: snap['subsPaid'] ?? 0,
      totalSubsPaid: snap['totalSubsPaid'] ?? 0,
      subsRec: snap['subsRec'] ?? 0,
      totalSubsRec: snap['totalSubsRec'] ?? 0,
      obSubs: snap['obSubs'] ?? 0,
    );
  }

  Map<String, dynamic> toSnapshot() {
    return {
      'month': month,
      'subsPaid': subsPaid,
      'totalSubsPaid': totalSubsPaid,
      'subsRec': subsRec,
      'totalSubsRec': totalSubsRec,
      'obSubs': obSubs,
    };
  }

  factory MonthlySubsModel.fromJson(Map<String, dynamic> json) {
    return MonthlySubsModel(
      month: json['month'] ?? '',
      subsPaid: json['subsPaid'] ?? 0,
      totalSubsPaid: json['totalSubsPaid'] ?? 0,
      subsRec: json['subsRec'] ?? 0,
      totalSubsRec: json['totalSubsRec'] ?? 0,
      obSubs: json['obSubs'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'subsPaid': subsPaid,
      'totalSubsPaid': totalSubsPaid,
      'subsRec': subsRec,
      'totalSubsRec': totalSubsRec,
      'obSubs': obSubs,
    };
  }
}
