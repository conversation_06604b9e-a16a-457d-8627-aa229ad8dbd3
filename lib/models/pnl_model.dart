import 'package:cloud_firestore/cloud_firestore.dart';

class PnlModel {
  final String docId;
  final DateTime? updatedAt;
  final num year;
  final num totalExp;
  final num totalInc;

  PnlModel({
    required this.docId,
    required this.updatedAt,
    required this.year,
    required this.totalExp,
    required this.totalInc,
  });

  // From JSON
  factory PnlModel.fromJson(Map<String, dynamic> json) {
    return PnlModel(
      docId: json['docId'] as String,
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
      year: json['year'],
      totalExp: json['totalExp'],
      totalInc: json['totalInc'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'year': year,
      'totalExp': totalExp,
      'totalInc': totalInc,
    };
  }

  // From DocumentSnapshot
  factory PnlModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return PnlModel(
      docId: snap.id,
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      year: data['year'],
      totalExp: data['totalExp'],
      totalInc: data['totalInc'],
    );
  }

  // To Firestore data (excluding docId)
  Map<String, dynamic> toSnap() {
    return {
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'year': year,
      'totalInc': totalInc,
      'totalExp': totalExp,
    };
  }
}

class PnlEntry {
  final String docId;
  final bool isExpense;
  final String title;
  final num amount;
  final DateTime createdAt;

  PnlEntry({
    required this.docId,
    required this.isExpense,
    required this.title,
    required this.amount,
    required this.createdAt,
  });

  // From JSON
  factory PnlEntry.fromJson(Map<String, dynamic> json) {
    return PnlEntry(
      docId: json['docId'] as String,
      isExpense: json['isExpense'] as bool,
      title: json['title'] as String,
      amount: json['amount'],
      createdAt: json['createdAt'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'isExpense': isExpense,
      'title': title,
      'amount': amount,
      'createdAt': createdAt,
    };
  }

  // From Firestore DocumentSnapshot
  factory PnlEntry.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return PnlEntry(
      docId: snap.id,
      isExpense: data['isExpense'] as bool,
      title: data['title'] as String,
      amount: data['amount'],
      createdAt: data['createdAt'],
    );
  }

  // To Firestore data (excluding docId)
  Map<String, dynamic> toSnap() {
    return {
      'isExpense': isExpense,
      'title': title,
      'amount': amount,
      'createdAt': createdAt,
    };
  }
}
