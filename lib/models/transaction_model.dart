import 'package:cloud_firestore/cloud_firestore.dart';

class TransactionModel {
  final String docId;
  final String uId;
  final String? userMonthlyId;
  final String? recoveryId;
  final DateTime createdAt;
  final String title;
  // final String status;
  final num amount;
  final bool inn;
  final bool? showInCB;
  final String? loanId;

  TransactionModel({
    required this.docId,
    required this.uId,
    required this.userMonthlyId,
    required this.recoveryId,
    required this.createdAt,
    required this.title,
    // required this.status,
    required this.amount,
    required this.inn,
    required this.loanId,
    required this.showInCB,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      docId: json['docId'],
      uId: json['uId'],
      createdAt: DateTime.parse(json['createdAt']),
      title: json['title'],
      // status: json['status'],
      amount: json['amount'],
      inn: json['inn'],
      userMonthlyId: json['userMonthlyId'],
      recoveryId: json['recoveryId'],
      loanId: json['loanId'],
      showInCB: json['showInCB'],
    );
  }

  factory TransactionModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return TransactionModel(
      docId: snap.id,
      uId: data['uId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      title: data['title'],
      // status: data['status'],
      amount: data['amount'],
      inn: data['inn'],
      userMonthlyId: data['userMonthlyId'],
      recoveryId: data['recoveryId'],
      loanId: data['loanId'],
      showInCB: data['showInCB'],
    );
  }
}
