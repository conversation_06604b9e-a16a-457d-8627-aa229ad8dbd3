import 'package:cloud_firestore/cloud_firestore.dart';

class SubsidiaryLedgerModel {
  // final String uId;
  // final num cpfNo;
  // final String name;
  // final String districtOffice;
  final DateTime? updatedAt;
  final num year;

  // final List<MonthlyLedgerModel> monthlyEntries;

  SubsidiaryLedgerModel({
    // required this.uId,
    // required this.name,
    // required this.cpfNo,
    // required this.districtOffice,
    required this.updatedAt,
    required this.year,

    // required this.monthlyEntries,
  });

  // From JSON
  factory SubsidiaryLedgerModel.fromJson(Map<String, dynamic> json) {
    return SubsidiaryLedgerModel(
      // uId: json['uId'] ?? '',
      // name: json['name'] ?? '',
      // cpfNo: json['cpfNo'] ?? 0,
      // districtOffice: json['districtOffice'] ?? [],
      updatedAt: DateTime.parse(json['updatedAt']),
      year: json['year'] ?? 0,

      // monthlyEntries: (json['monthlyEntries'] as List<dynamic>)
      //         .map((e) => MonthlyLedgerModel.fromJson(e))
      //         .toList() ??
      //     [],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      // 'uId': uId,
      // 'name': name,
      // 'cpfNo': cpfNo,
      // 'districtOffice': districtOffice,
      'updatedAt': updatedAt?.toIso8601String(),
      'year': year,

      // 'monthlyEntries': monthlyEntries.map((e) => e.toJson()).toList(),
    };
  }

  // From Firestore DocumentSnapshot
  factory SubsidiaryLedgerModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;
    return SubsidiaryLedgerModel(
      // uId: data['uId'],
      // name: data['name'],
      // cpfNo: data['cpfNo'],
      // districtOffice: data['districtOffice'],
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp?)?.toDate()
          : null,
      year: data['year'],

      // monthlyEntries: (data['monthlyEntries'] as List<dynamic>)
      //     .map((e) => MonthlyLedgerModel.fromJson(Map<String, dynamic>.from(e)))
      //     .toList(),
    );
  }

  // To Firestore (same as toJson)
  Map<String, dynamic> toSnap() => toJson();
}

class MonthlyLedgerModel {
  final String month;
  final num subscription;
  final num longTermInstallment;
  final num shortTermInstallment;
  final num interest;
  final num totalinterest;
  final num totalsubscription;
  final num totallongTermInstallment;
  final num totalshortTermInstallment;
  final num penalty;
  final num totalPenalty;
  final num obSubs;
  final num obShares;
  final num oblt;
  final num obst;

  MonthlyLedgerModel({
    required this.month,
    required this.subscription,
    required this.longTermInstallment,
    required this.shortTermInstallment,
    required this.interest,
    required this.totalinterest,
    required this.totallongTermInstallment,
    required this.totalshortTermInstallment,
    required this.totalsubscription,
    required this.penalty,
    required this.totalPenalty,
    required this.obSubs,
    required this.obShares,
    required this.oblt,
    required this.obst,
  });

  factory MonthlyLedgerModel.fromJson(Map<String, dynamic> json) {
    return MonthlyLedgerModel(
      month: json['month'] ?? '',
      subscription: json['subscription'] ?? 0,
      longTermInstallment: json['longTermInstallment'] ?? 0,
      shortTermInstallment: json['shortTermInstallment'] ?? 0,
      interest: json['interest'] ?? 0,
      totalinterest: json['totalinterest'] ?? 0,
      totallongTermInstallment: json['totallongTermInstallment'] ?? 0,
      totalshortTermInstallment: json['totalshortTermInstallment'] ?? 0,
      totalsubscription: json['totalsubscription'] ?? 0,
      penalty: json['penalty'] ?? 0,
      totalPenalty: json['totalPenalty'] ?? 0,
      obSubs: json['obSubs'] ?? 0,
      obShares: json['obShares'] ?? 0,
      oblt: json['oblt'] ?? 0,
      obst: json['obst'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'subscription': subscription,
      'longTermInstallment': longTermInstallment,
      'shortTermInstallment': shortTermInstallment,
      'interest': interest,
      'totalinterest': totalinterest,
      'totallongTermInstallment': totallongTermInstallment,
      'totalshortTermInstallment': totalshortTermInstallment,
      'totalsubscription': totalsubscription,
      'penalty': penalty,
      'totalPenalty': totalPenalty,
      'obSubs': obSubs,
      'obShares': obShares,
      'oblt': oblt,
      'obst': obst,
    };
  }

  factory MonthlyLedgerModel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return MonthlyLedgerModel(
      month: data['month'] ?? '',
      subscription: data['subscription'] ?? 0,
      longTermInstallment: data['longTermInstallment'] ?? 0,
      shortTermInstallment: data['shortTermInstallment'] ?? 0,
      interest: data['interest'] ?? 0,
      totalinterest: data['totalinterest'] ?? 0,
      totallongTermInstallment: data['totallongTermInstallment'] ?? 0,
      totalshortTermInstallment: data['totalshortTermInstallment'] ?? 0,
      totalsubscription: data['totalsubscription'] ?? 0,
      penalty: data['penalty'] ?? 0,
      totalPenalty: data['totalPenalty'] ?? 0,
      obSubs: data['obSubs'],
      obShares: data['obShares'],
      oblt: data['oblt'],
      obst: data['obst'],
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'month': month,
      'subscription': subscription,
      'longTermInstallment': longTermInstallment,
      'shortTermInstallment': shortTermInstallment,
      'interest': interest,
      'totalinterest': totalinterest,
      'totallongTermInstallment': totallongTermInstallment,
      'totalshortTermInstallment': totalshortTermInstallment,
      'totalsubscription': totalsubscription,
      'penalty': penalty,
      'totalPenalty': totalPenalty,
      'obSubs': obSubs,
      'obShares': obShares,
      'oblt': oblt,
      'obst': obst,
    };
  }
}
