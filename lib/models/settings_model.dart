import 'package:cloud_firestore/cloud_firestore.dart';

class SettingsModel {
  final String docId;
  final String? dividentRate;
  final double? ltloanInterest;
  final double? stloanInterest;
  final String? subscriptionInterest;
  // final String? shareIntRate;
  final String? defaultLtinstallAmt;
  final String? defaultStinstallAmt;
  final String? defaultSubsinstallAmt;
  final String? maxLtLoanAmt;
  final String? maxStLoanAmt;
  final num shareValue;
  final String? upiString;
  final String? upiId;
  final num? upiAmt;
  final num applicationNo;
  final String? upiPayeeName;
  final String setPin;
  final String? maintenanceString;
  final String andbuildNumber;
  final String iosbuildNumber;

  final String? maxShareValue;
  final String? shareCapital;

  final String generalReservedFund;
  final String charityFund;
  final String coopPublicityFund;
  final String dividentEquivalisationFund;

  SettingsModel({
    this.maintenanceString,
    this.maxLtLoanAmt,
    this.maxStLoanAmt,
    required this.docId,
    this.dividentRate,
    this.subscriptionInterest,
    this.ltloanInterest,
    this.stloanInterest,
    this.upiString,
    this.upiId,
    this.upiAmt,
    this.upiPayeeName,
    // this.shareIntRate,
    this.defaultLtinstallAmt,
    this.defaultStinstallAmt,
    this.defaultSubsinstallAmt,
    required this.applicationNo,
    required this.shareValue,
    required this.setPin,
    required this.andbuildNumber,
    required this.iosbuildNumber,
    required this.maxShareValue,
    required this.shareCapital,
    required this.charityFund,
    required this.coopPublicityFund,
    required this.dividentEquivalisationFund,
    required this.generalReservedFund,
  });

  // Convert a Setting object into a Map
  Map<String, dynamic> toJson() {
    return {
      'subscriptionInterest': subscriptionInterest,
      'dividentRate': dividentRate,
      'ltloanInterest': ltloanInterest,
      // 'shareIntRate': shareIntRate,
      'stloanInterest': stloanInterest,
      'defaultLtinstallAmt': defaultLtinstallAmt,
      'defaultStinstallAmt': defaultStinstallAmt,
      'defaultSubsinstallAmt': defaultSubsinstallAmt,
      'shareValue': shareValue,
      'upiString': upiString,
      'upiId': upiId,
      'upiAmt': upiAmt,
      'upiPayeeName': upiPayeeName,
      'applicationNo': applicationNo,
      'maxLtLoanAmt': maxLtLoanAmt,
      'maxStLoanAmt': maxStLoanAmt,
      'setPin': setPin,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
      'maxShareValue': maxShareValue,
      'shareCapital': shareCapital,
      'generalReservedFund': generalReservedFund,
      'charityFund': charityFund,
      'coopPublicityFund': coopPublicityFund,
      'dividentEquivalisationFund': dividentEquivalisationFund,
    };
  }

  // Convert a Map into a Setting object
  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      docId: json['docId'],
      dividentRate: json['dividentRate'],
      subscriptionInterest: json['subscriptionInterest'],
      ltloanInterest: json['ltloanInterest'],
      // shareIntRate: json['shareIntRate'],
      stloanInterest: json['stloanInterest'],
      defaultLtinstallAmt: json['defaultLtinstallAmt'],
      defaultStinstallAmt: json['defaultStinstallAmt'],
      defaultSubsinstallAmt: json['defaultSubsinstallAmt'],
      shareValue: json['shareValue'],
      upiString: json['upiString'],
      upiId: json['upiId'],
      upiAmt: json['upiAmt'],
      upiPayeeName: json['upiPayeeName'],
      applicationNo: json['applicationNo'],
      maxLtLoanAmt: json['maxLtLoanAmt'],
      maxStLoanAmt: json['maxStLoanAmt'],
      setPin: json['setPin'],
      andbuildNumber: json['andbuildNumber'],
      iosbuildNumber: json['iosbuildNumber'],
      maxShareValue: json['maxShareValue'],
      shareCapital: json['shareCapital'],
      dividentEquivalisationFund: json['dividentEquivalisationFund'],
      coopPublicityFund: json['coopPublicityFund'],
      charityFund: json['charityFund'],
      generalReservedFund: json['generalReservedFund'],
    );
  }

  // Convert a Firestore DocumentSnapshot into a SettingsModel object
  factory SettingsModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return SettingsModel(
      docId: snap.id,
      dividentRate: data['dividentRate'],
      subscriptionInterest: data['subscriptionInterest'],
      ltloanInterest: data['ltloanInterest'],
      stloanInterest: data['stloanInterest'],
      // shareIntRate: data['shareIntRate'],
      defaultLtinstallAmt: data['defaultLtinstallAmt'],
      defaultStinstallAmt: data['defaultStinstallAmt'],
      defaultSubsinstallAmt: data['defaultSubsinstallAmt'],
      shareValue: data['shareValue'],
      upiString: data['upiString'],
      upiId: data['upiId'],
      upiAmt: data['upiAmt'],
      upiPayeeName: data['upiPayeeName'],
      applicationNo: data['applicationNo'],
      maxLtLoanAmt: data['maxLtLoanAmt'],
      maxStLoanAmt: data['maxStLoanAmt'],
      setPin: data['setPin'],
      andbuildNumber: data['andbuildNumber'],
      iosbuildNumber: data['iosbuildNumber'],
      maxShareValue: data['maxShareValue'],
      shareCapital: data['shareCapital'],
      generalReservedFund: data['generalReservedFund'],
      charityFund: data['charityFund'],
      coopPublicityFund: data['coopPublicityFund'],
      dividentEquivalisationFund: data['dividentEquivalisationFund'],
    );
  }

  // Convert a SettingsModel object into a Firestore DocumentSnapshot format
  Map<String, dynamic> toSnap() {
    return {
      'subscriptionInterest': subscriptionInterest,
      'dividentRate': dividentRate,
      'ltloanInterest': ltloanInterest,
      // 'shareIntRate': shareIntRate,
      'stloanInterest': stloanInterest,
      'defaultLtinstallAmt': defaultLtinstallAmt,
      'defaultStinstallAmt': defaultStinstallAmt,
      'defaultSubsinstallAmt': defaultSubsinstallAmt,
      'shareValue': shareValue,
      'upiString': upiString,
      'upiId': upiId,
      'upiAmt': upiAmt,
      'upiPayeeName': upiPayeeName,
      'applicationNo': applicationNo,
      'maxLtLoanAmt': maxLtLoanAmt,
      'maxStLoanAmt': maxStLoanAmt,
      'setPin': setPin,
      'andbuildNumber': andbuildNumber,
      'iosbuildNumber': iosbuildNumber,
      'maxShareValue': maxShareValue,
      'shareCapital': shareCapital,
      'dividentEquivalisationFund': dividentEquivalisationFund,
      'coopPublicityFund': coopPublicityFund,
      'charityFund': charityFund,
      'generalReservedFund': generalReservedFund,
    };
  }
}
