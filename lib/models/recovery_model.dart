import 'package:cloud_firestore/cloud_firestore.dart';

class RecoveryModel {
  final num? subscriptionAmt;
  final num? longTermInstalmentAmt;
  final num? shortTermInstalmentAmt;
  final num? longTermInterestAmt;
  final num? shortTermInterestAmt;
  final num? totalAmt;

  RecoveryModel({
    required this.subscriptionAmt,
    required this.longTermInstalmentAmt,
    required this.shortTermInstalmentAmt,
    required this.longTermInterestAmt,
    required this.shortTermInterestAmt,
    required this.totalAmt,
  });

  factory RecoveryModel.fromJson(Map<String, dynamic> json) {
    return RecoveryModel(
      subscriptionAmt: json['subscriptionAmt'] ?? 0,
      longTermInstalmentAmt: json['longTermInstalmentAmt'] ?? 0,
      shortTermInstalmentAmt: json['shortTermInstalmentAmt'] ?? 0,
      longTermInterestAmt: json['longTermInterestAmt'] ?? 0,
      shortTermInterestAmt: json['shortTermInterestAmt'] ?? 0,
      totalAmt: json['totalAmt'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'subscriptionAmt': subscriptionAmt,
      'longTermInstalmentAmt': longTermInstalmentAmt,
      'shortTermInstalmentAmt': shortTermInstalmentAmt,
      'longTermInterestAmt': longTermInterestAmt,
      'shortTermInterestAmt': shortTermInterestAmt,
      'totalAmt': totalAmt,
    };
  }

  // Factory constructor for 'from snap' (assumed to be deserialization from a snapshot, e.g., Firebase)
  factory RecoveryModel.fromSnap(DocumentSnapshot snap) {
    return RecoveryModel(
      subscriptionAmt: snap['subscriptionAmt'],
      longTermInstalmentAmt: snap['longTermInstalmentAmt'],
      shortTermInstalmentAmt: snap['shortTermInstalmentAmt'],
      longTermInterestAmt: snap['longTermInterestAmt'],
      shortTermInterestAmt: snap['shortTermInterestAmt'],
      totalAmt: snap['totalAmt'],
    );
  }
}

class InputRecoveryModel {
  num subscriptionAmt;
  num longTermInstalmentAmt;
  num shortTermInstalmentAmt;
  num longTermInterestAmt;
  num shortTermInterestAmt;
  num totalAmt;

  InputRecoveryModel({
    required this.subscriptionAmt,
    required this.longTermInstalmentAmt,
    required this.shortTermInstalmentAmt,
    required this.longTermInterestAmt,
    required this.shortTermInterestAmt,
    required this.totalAmt,
  });
  factory InputRecoveryModel.fromSnap(DocumentSnapshot snap) {
    return InputRecoveryModel(
      subscriptionAmt: snap['subscriptionAmt'],
      longTermInstalmentAmt: snap['longTermInstalmentAmt'],
      shortTermInstalmentAmt: snap['shortTermInstalmentAmt'],
      longTermInterestAmt: snap['longTermInterestAmt'],
      shortTermInterestAmt: snap['shortTermInterestAmt'],
      totalAmt: snap['totalAmt'],
    );
  }
  factory InputRecoveryModel.fromJson(Map<String, dynamic> json) {
    return InputRecoveryModel(
      subscriptionAmt: json['subscriptionAmt'],
      longTermInstalmentAmt: json['longTermInstalmentAmt'],
      shortTermInstalmentAmt: json['shortTermInstalmentAmt'],
      longTermInterestAmt: json['longTermInterestAmt'],
      shortTermInterestAmt: json['shortTermInterestAmt'],
      totalAmt: json['totalAmt'],
    );
  }

  // Method for 'to snap' (assumed to be serialization for a snapshot, e.g., saving to Firebase)
  Map<String, dynamic> toMap() {
    return {
      'subscriptionAmt': subscriptionAmt,
      'longTermInstalmentAmt': longTermInstalmentAmt,
      'shortTermInstalmentAmt': shortTermInstalmentAmt,
      'longTermInterestAmt': longTermInterestAmt,
      'shortTermInterestAmt': shortTermInterestAmt,
      'totalAmt': totalAmt,
    };
  }
}
