import 'package:cloud_firestore/cloud_firestore.dart';

class TrialBalModel {
  final String docId;
  final DateTime? updatedAt;
  final DateTime createdAt;
  final num year;
  final num totalRec;
  final num totalPay;

  TrialBalModel({
    required this.docId,
    required this.updatedAt,
    required this.year,
    required this.totalRec,
    required this.totalPay,
    required this.createdAt,
  });

  // From JSON
  factory TrialBalModel.fromJson(Map<String, dynamic> json) {
    return TrialBalModel(
      docId: json['docId'] as String,
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      year: json['year'],
      totalRec: json['totalRec'],
      totalPay: json['totalPay'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'year': year,
      'totalRec': totalRec,
      'totalPay': totalPay,
    };
  }

  // From DocumentSnapshot
  factory TrialBalModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return TrialBalModel(
      docId: snap.id,
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      year: data['year'],
      totalRec: data['totalRec'],
      totalPay: data['totalPay'],
    );
  }

  // To Firestore data (excluding docId)
  Map<String, dynamic> toSnap() {
    return {
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'createdAt': createdAt,
      'year': year,
      'totalRec': totalRec,
      'totalPay': totalPay,
    };
  }
}

class TbEntry {
  final String docId;
  final String title;
  final num amount;
  final DateTime createdAt;
  final num receipt;
  final num payment;

  TbEntry({
    required this.docId,
    required this.title,
    required this.amount,
    required this.createdAt,
    required this.payment,
    required this.receipt,
  });
  // From JSON
  factory TbEntry.fromJson(Map<String, dynamic> json) {
    return TbEntry(
      docId: json['docId'] as String,
      title: json['title'] as String,
      amount: json['amount'],
      createdAt: json['createdAt'],
      payment: json['payment'],
      receipt: json['receipt'],
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'amount': amount,
      'createdAt': createdAt,
      'receipt': receipt,
      'payment': payment,
    };
  }

  // From Firestore DocumentSnapshot
  factory TbEntry.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return TbEntry(
      docId: snap.id,
      title: data['title'] as String,
      amount: data['amount'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      payment: data['payment'],
      receipt: data['receipt'],
    );
  }

  // To Firestore data (excluding docId)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'amount': amount,
      'createdAt': createdAt,
      'receipt': receipt,
      'payment': payment,
    };
  }
}
