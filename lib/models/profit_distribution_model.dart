import 'package:cloud_firestore/cloud_firestore.dart';

class ProfitDistributionModel {
  final String docId;
  final String title;
  final num amount;
  final num percentage;
  final num totalAmt;
  final num year;
  final DateTime createdAt;

  ProfitDistributionModel({
    required this.docId,
    required this.title,
    required this.amount,
    required this.percentage,
    required this.totalAmt,
    required this.year,
    required this.createdAt,
  });

  factory ProfitDistributionModel.fromJson(
      Map<String, dynamic> json, String docId) {
    return ProfitDistributionModel(
      docId: docId,
      title: json['title'] ?? '',
      amount: json['amount'] ?? 0,
      percentage: json['percentage'] ?? 0,
      totalAmt: json['totalAmt'] ?? 0,
      year: json['year'] ?? 0,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'amount': amount,
      'percentage': percentage,
      'totalAmt': totalAmt,
      'year': year,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory ProfitDistributionModel.fromSnap(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProfitDistributionModel.fromJson(data, doc.id);
  }

  Map<String, dynamic> toSnap() => toJson();
}
