import 'package:cloud_firestore/cloud_firestore.dart';

class FeedbackModel {
  final String docId;
  final String uId;
  final String title;
  final String desc;
  final DateTime createdAt;

  FeedbackModel({
    required this.docId,
    required this.uId,
    required this.title,
    required this.desc,
    required this.createdAt,
  });

  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      docId: json['docId'] ?? '',
      uId: json['uId'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'uId': uId,
      'title': title,
      'desc': desc,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory FeedbackModel.fromSnap(dynamic snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    DateTime createdAt;
    var rawCreatedAt = data['createdAt'];

    if (rawCreatedAt is Timestamp) {
      // Firestore web/native returns a Timestamp object
      createdAt = rawCreatedAt.toDate();
    } else if (rawCreatedAt is String) {
      createdAt = DateTime.parse(rawCreatedAt);
    } else if (rawCreatedAt is DateTime) {
      createdAt = rawCreatedAt;
    } else {
      createdAt = DateTime.now();
    }

    return FeedbackModel(
      docId: snapshot.id,
      uId: data['uId'] ?? '',
      title: data['title'] ?? '',
      desc: data['desc'] ?? '',
      createdAt: createdAt,
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'uId': uId,
      'title': title,
      'desc': desc,
      'createdAt': createdAt,
    };
  }
}
