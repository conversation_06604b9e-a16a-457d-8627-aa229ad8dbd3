import 'package:cloud_firestore/cloud_firestore.dart';

class DistrictOfficeModel {
  final String docId;
  final String name;
  final String email;
  final List<String>? extraEmail;
  final String location;

  DistrictOfficeModel(
      {this.extraEmail,
      required this.docId,
      required this.name,
      required this.email,
      required this.location});

  factory DistrictOfficeModel.fromJson(Map<String, dynamic> json) {
    return DistrictOfficeModel(
      docId: json['docId'],
      name: json['name'],
      email: json['email'],
      extraEmail: json['extraEmail'],
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'email': email,
      'extraEmail': extraEmail,
      'location': location,
    };
  }

  factory DistrictOfficeModel.fromSnap(
      DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data();
    if (data == null) {
      throw Exception('Document does not exist');
    }

    return DistrictOfficeModel(
      docId: snap.id,
      name: data['name'],
      email: data['email'],
      extraEmail: (data['extraEmail'] as List?)?.cast<String>(),
      location: data['location'],
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'name': name,
      'email': email,
      'extraEmail': extraEmail,
      'location': location,
    };
  }
}
