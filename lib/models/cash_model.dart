//model to show the transaction of cash coming in. on click of add cash this model will run

import 'package:cloud_firestore/cloud_firestore.dart';

class CashModel {
  final String docID;
  final DateTime createdAt;
  final DateTime addeddate;
  final String? comment;
  final String addedBy;
  final num amount;

  CashModel(
      {required this.docID,
      required this.createdAt,
      required this.addeddate,
      required this.comment,
      required this.addedBy,
      required this.amount});

  // fromJson: Create CashModel from JSON data
  factory CashModel.fromJson(Map<String, dynamic> json) {
    return CashModel(
      docID: json['docID'] as String,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      addeddate: (json['addeddate'] as Timestamp).toDate(),
      comment: json['comment'] as String?,
      addedBy: json['addedBy'] as String,
      amount: json['amount'] as num,
    );
  }

  // toJson: Convert CashModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'docID': docID,
      'createdAt': Timestamp.fromDate(createdAt),
      'addeddate': Timestamp.fromDate(addeddate),
      'comment': comment,
      'addedBy': addedBy,
      'amount': amount,
    };
  }

  // fromSnap: Create CashModel from Firestore DocumentSnapshot
  factory CashModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return CashModel(
      docID: snap.id,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      addeddate: (data['addeddate'] as Timestamp).toDate(),
      comment: data['comment'] as String?,
      addedBy: data['addedBy'] as String,
      amount: data['amount'] as num,
    );
  }

  // toSnap: Convert CashModel to Firestore DocumentSnapshot-compatible data
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'addeddate': Timestamp.fromDate(addeddate),
      'comment': comment,
      'addedBy': addedBy,
      'amount': amount,
    };
  }
}
