import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';

class RecoveryPageModel {
  String doId;
  int month;
  int year;
  List<DoUser> doUser;

  RecoveryPageModel({
    required this.doId,
    required this.month,
    required this.year,
    required this.doUser,
  });

  // Factory constructor to create a RecoveryPageModel from Firestore DocumentSnapshot
  factory RecoveryPageModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return RecoveryPageModel(
      doId: data['doId'] as String,
      month: data['month'] as int,
      year: data['year'] as int,
      doUser: (data['doUser'] as List<dynamic>)
          .map((user) => DoUser.fromSnapshot(user as Map<String, dynamic>))
          .toList(),
    );
  }

  // Method to convert RecoveryPageModel to a map
  Map<String, dynamic> toMap() {
    return {
      'doId': doId,
      'month': month,
      'year': year,
      'doUser': doUser.map((user) => user.toMap()).toList(),
    };
  }
}

class DoUser {
  InputUserModel user;
  List<Loan> loans;
  // InputUserMonthlyRecordModel? lastRecoveryRecord;

  DoUser({
    // required this.lastRecoveryRecord,
    required this.user,
    required this.loans,
  });

  // Factory constructor to create a DoUser from a Firestore Map<String, dynamic>
  factory DoUser.fromSnapshot(Map<String, dynamic> data) {
    return DoUser(
      // lastRecoveryRecord: InputUserMonthlyRecordModel.fromJson(
      //     data['lastRecoveryRecord'] as Map<String, dynamic>),
      user: InputUserModel.fromJson(data['user'] as Map<String, dynamic>),
      loans: (data['loans'] as List<dynamic>)
          .map((loan) => Loan.fromJson(loan as Map<String, dynamic>))
          .toList(),
    );
  }

  // Method to convert DoUser to a map
  Map<String, dynamic> toMap() {
    return {
      // 'lastRecoveryRecord': lastRecoveryRecord?.toJson(),
      'user': user.toJson(),
      'loans': loans.map((loan) => loan.toJson()).toList(),
    };
  }
}
