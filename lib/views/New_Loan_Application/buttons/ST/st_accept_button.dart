import 'package:flutter/material.dart';

class StAcceptButton extends StatelessWidget {
  const StAcceptButton({
    super.key,
    required this.enabled,
    required this.onPressed,
  });
  final bool enabled;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        padding: WidgetStatePropertyAll(
            EdgeInsetsDirectional.symmetric(horizontal: 100, vertical: 15)),
        shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
        backgroundColor: WidgetStatePropertyAll(Colors.green),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: true
          ? () {
              onPressed();
            }
          : () {
              // context.pop();
              // showDialog(
              //   context: context,
              //   builder: (context) => AlertDialog(
              //     title: const Text("Accept Application?"),
              //     content: const Text("Are you sure you want to Accept Application?"),
              //     actions: [
              //       TextButton(
              //           onPressed: () async {
              //             onPressed();
              //             context.pop();
              //             // await FBFireStore.loan
              //             //                       .doc(ctrl.loan[index].docId)
              //             //                       .update({
              //             //                     'isNew': false,
              //             //                     'approvedOn': DateTime.now(),
              //             //                   });
              //           },
              //           child: const Text("Yes")),
              //       TextButton(
              //           onPressed: () async {
              //             if (context.mounted) {
              //               context.pop();
              //             }
              //           },
              //           child: const Text("No")),
              //     ],
              //   ),
              // );
            },
      child: Text(
        enabled ? "SAVE" : "ACCEPT",
        style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.bold),
      ),
    );
  }
}
