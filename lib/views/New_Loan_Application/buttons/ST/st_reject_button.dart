import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/firebase.dart';
import '../../../../shared/methods.dart';
import '../../forms/stloanform.dart';

class StRejectButton extends StatelessWidget {
  const StRejectButton({
    super.key,
    required this.reasonctrl,
    required this.widget,
    required this.onPressed,
  });

  final TextEditingController reasonctrl;
  final Stloanform widget;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        overlayColor: WidgetStatePropertyAll(Colors.transparent),
        foregroundColor: WidgetStatePropertyAll(Colors.transparent),
        shape: WidgetStatePropertyAll(BeveledRectangleBorder(
            side: BorderSide(color: Colors.black, width: 0))),
        backgroundColor: WidgetStatePropertyAll(Colors.transparent),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: () {
        context.pop();
        showDialog(
          context: context,
          builder: (context) => ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 200, maxWidth: 500),
            child: AlertDialog(
              title: const Text("Reject Application?"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Are you sure you want to Reject Application?"),
                  const SizedBox(height: 5),
                  SizedBox(
                      width: 300,
                      child: CustomTextfield(
                          text: "Reason *",
                          controller: reasonctrl,
                          enabled: true)),
                ],
              ),
              actions: [
                TextButton(
                    onPressed: () async {
                      await FBFireStore.loan.doc(widget.loan.docId).update({
                        'rejectionDate': Timestamp.now(),
                        'isNew': false,
                        'rejectionReason': reasonctrl.text
                      });
                      context.pop();
                      showCtcAppSnackBar(
                          context, "Application Successfully Rejected");
                    },
                    child: const Text("Yes")),
                TextButton(
                    onPressed: () async {
                      if (context.mounted) {
                        context.pop();
                      }
                    },
                    child: const Text("No")),
              ],
            ),
          ),
        );
      },
      child: const Text(
        "REJECT",
        style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.normal),
      ),
    );
  }
}
