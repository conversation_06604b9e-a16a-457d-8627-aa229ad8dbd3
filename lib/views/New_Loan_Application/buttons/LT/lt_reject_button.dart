import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/methods.dart';
import '../../forms/ltloanform.dart';

class LtRejectButton extends StatelessWidget {
  const LtRejectButton({
    super.key,
    required this.reasonctrl,
    required this.widget,
    required this.onPressed,
  });

  final TextEditingController reasonctrl;
  final Ltloanform widget;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        overlayColor: WidgetStatePropertyAll(Colors.transparent),
        foregroundColor: WidgetStatePropertyAll(Colors.transparent),
        shape: WidgetStatePropertyAll(BeveledRectangleBorder(
            side: BorderSide(color: Colors.black, width: 0))),
        backgroundColor: WidgetStatePropertyAll(Colors.transparent),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 200, maxWidth: 500),
            child: AlertDialog(
              title: const Text("Reject Application?"),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Are you sure you want to Reject Application?"),
                  const SizedBox(height: 5),
                  SizedBox(
                      width: 300,
                      child: CustomTextfield(
                          text: "Reason *",
                          controller: reasonctrl,
                          enabled: true)),
                ],
              ),
              actions: [
                TextButton(
                    onPressed: () async {
                      onPressed();
                      // context.pop();

                      // await FBFireStore.loan.doc(widget.loan.docId).update({
                      //   'rejectionDate':
                      //       DateFormat('dd-MM-yyyy').format(DateTime.now()),
                      //   'rejectionReason': reasonctrl.text
                      // });
                      // context.pop();
                      showCtcAppSnackBar(
                          context, "Application Successfully Rejected");
                    },
                    child: const Text("Yes")),
                TextButton(
                    onPressed: () async {
                      if (context.mounted) {
                        context.pop();
                      }
                    },
                    child: const Text("No")),
              ],
            ),
          ),
        );
      },
      child: const Text(
        "REJECT",
        style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.normal),
      ),
    );
  }
}
