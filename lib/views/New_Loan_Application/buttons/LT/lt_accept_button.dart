import 'package:flutter/material.dart';

class LtAcceptButton extends StatelessWidget {
  const LtAcceptButton({
    super.key,
    required this.enabled,
    required this.onPressed,
    required this.snackbarKey,
  });

  final bool enabled;
  final Function onPressed;
  final snackbarKey;
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        padding: WidgetStatePropertyAll(
            EdgeInsetsDirectional.symmetric(horizontal: 100, vertical: 15)),
        shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
        backgroundColor: WidgetStatePropertyAll(Colors.green),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: true
          ? () {
              onPressed();
            }
          : () {
              // context.pop();
              // showDialog(
              //   context: context,
              //   builder: (context) => AlertDialog(
              //     title: const Text("Accept Application?"),
              //     content: const Text("Are you sure you want to Accept Application?"),
              //     actions: [
              //       TextButton(
              //           onPressed: () async {
              //             onPressed();
              //             context.pop();
              //           },
              //           child: const Text("Yes")),
              //       TextButton(
              //           onPressed: () async {
              //             if (context.mounted) {
              //               context.pop();
              //             }
              //           },
              //           child: const Text("No")),
              //     ],
              //   ),
              // );
            },
      child: Text(
        enabled ? "SAVE" : "ACCEPT",
        style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.bold),
      ),
    );
  }
}
