import 'package:flutter/material.dart';

class LtEditButton extends StatelessWidget {
  const LtEditButton({
    super.key,
    required this.onPressed,
  });

  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        overlayColor: WidgetStatePropertyAll(Colors.transparent),
        foregroundColor: WidgetStatePropertyAll(Colors.transparent),
        padding: WidgetStatePropertyAll(
            EdgeInsetsDirectional.symmetric(horizontal: 40, vertical: 15)),
        // shape: WidgetStatePropertyAll(BeveledRectangleBorder(
        //     side: BorderSide(color: Colors.black, width: 0))),
        backgroundColor: WidgetStatePropertyAll(Colors.transparent),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: () async {
        onPressed();
      },
      child: const Text(
        "EDIT",
        style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.normal),
      ),
    );
  }
}
