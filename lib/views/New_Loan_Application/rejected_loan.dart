import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/forms/ltloanform.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/forms/stloanform.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../shared/methods.dart';

class RejectedApplicationPage extends StatefulWidget {
  const RejectedApplicationPage({super.key});

  @override
  State<RejectedApplicationPage> createState() =>
      _RejectedApplicationPageState();
}

class _RejectedApplicationPageState extends State<RejectedApplicationPage> {
  // SearchController sctrl = SearchController();
  UserModel? selectedUser;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<Loan> filtered =
          ctrl.loan.where((element) => element.rejectionDate != null).toList();
      if (selectedUser != null) {
        filtered = ctrl.loan
            .where((element) =>
                (element.rejectionDate != null) &&
                (element.uid == selectedUser?.docId))
            .toList();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                DropdownSearch<UserModel>(
                  selectedItem: selectedUser,
                  onChanged: (value) {
                    selectedUser = value;
                    if (selectedUser != null) {
                      setState(() {});
                    }
                  },
                  decoratorProps: DropDownDecoratorProps(
                    decoration: InputDecoration(
                        hintText: "Search User",
                        constraints: const BoxConstraints(maxWidth: 450),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10))),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    searchFieldProps: TextFieldProps(
                      decoration: InputDecoration(
                        hintText: ' Search... ',
                        border: UnderlineInputBorder(),
                      ),
                    ),
                    fit: FlexFit.loose,
                    constraints: BoxConstraints(),
                  ),
                  itemAsString: (item) => item.name,
                  items: (filter, loadProps) {
                    return List.generate(
                            ctrl.users.length, (index) => ctrl.users[index])
                        .toList();
                  },
                  compareFn: (item1, item2) =>
                      item1.name.toLowerCase() == item2.name.toLowerCase(),
                ),
                if (selectedUser != null) SizedBox(width: 5),
                if (selectedUser != null)
                  IconButton(
                      style: OutlinedButton.styleFrom(),
                      onPressed: () {
                        setState(() {
                          selectedUser = null;
                        });
                      },
                      icon: Icon(
                        Icons.clear,
                        size: 30,
                      ))
              ],
            ),
            const SizedBox(height: 40),
            const Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Applied On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Appl No.'),
                HeaderTxt(txt: 'Loan Type'),
              ],
            ),
            const SizedBox(height: 30),
            ...List.generate(
              filtered.length,
              (index) {
                final user = ctrl.users.firstWhereOrNull(
                    (element) => element.docId == filtered[index].uid);
                final doOffice = ctrl.districtoffice.firstWhereOrNull(
                    (element) => element.docId == user?.districtoffice);
                return InkWell(
                  onTap: () => applDetailsDialog(context, ctrl, index),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(DateFormat('dd-MM-yyyy')
                                .format(filtered[index].appliedOn)),
                          ),
                          Expanded(child: Text(user?.name ?? "")),
                          Expanded(child: Text(doOffice?.name ?? "-")),
                          Expanded(
                              child: Text(
                                  filtered[index].applicationNo.toString())),
                          Expanded(child: Text(filtered[index].loanType)),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }

  Future<dynamic> applDetailsDialog(
      BuildContext context, HomeCtrl ctrl, int index) {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        scrollable: true,
        content: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 400),
            child: SizedBox(
                width: 800,
                child: ctrl.loan[index].loanType == 'Long Term Loan'
                    ? Ltloanform(
                        loan: ctrl.loan[index],
                        index: index,
                      )
                    : Stloanform(
                        index: index,
                        loan: ctrl.loan[index],
                      )),
          ),
        ),
        title: Center(child: const Text("Application Details")),
      ),
      // ),
    );
  }
}
