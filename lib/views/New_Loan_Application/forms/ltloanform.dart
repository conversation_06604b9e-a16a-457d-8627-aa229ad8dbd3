// ignore_for_file: use_build_context_synchronously, avoid_print

import 'dart:typed_data';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:signature/signature.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../buttons/LT/lt_accept_button.dart';
import '../buttons/LT/lt_edit_button.dart';
import '../buttons/LT/lt_reject_button.dart';

class Ltloanform extends StatefulWidget {
  const Ltloanform({super.key, required this.index, required this.loan});

  final int index;
  final Loan loan;

  @override
  State<Ltloanform> createState() => _LtloanformState();
}

class _LtloanformState extends State<Ltloanform> {
  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController desigctrl = TextEditingController();
  TextEditingController bacnoctrl = TextEditingController();
  TextEditingController addressctrl = TextEditingController();
  TextEditingController appliedamtctrl = TextEditingController();
  TextEditingController appliedamtwordsctrl = TextEditingController();
  TextEditingController loanreasonctrl = TextEditingController();
  TextEditingController surityname1ctrl = TextEditingController();
  TextEditingController surityname2ctrl = TextEditingController();
  TextEditingController acno1ctrl = TextEditingController();
  TextEditingController acno2ctrl = TextEditingController();
  TextEditingController ltreasonctrl = TextEditingController();
  TextEditingController ltinstallctrl = TextEditingController();

  Uint8List? borrowersignatureImage;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool ltOnSubmitLoad = false;
  // Future<String?> uploadSignimages(Uint8List file) async {
  //   try {
  //     final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
  //     final imageRef = FBStorage.fbstore.ref().child(path);
  //     final task = await imageRef.putData(file);
  //     var downloadurl = await task.ref.getDownloadURL();
  //     return await task.ref.getDownloadURL();
  //   } catch (e) {
  //     print("Error uploading image: $e");
  //     return null;
  //   }
  // }

  bool enabled = false;
  bool ltcheckbox = false;

  @override
  void initState() {
    super.initState();
    appnoctrl.text = widget.loan.applicationNo.toString();
    fullnamectrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.name ??
        "";
    desigctrl.text = widget.loan.designation.toString();
    bacnoctrl.text = widget.loan.bAcNo.toString();
    appliedamtctrl.text = widget.loan.appliedLoanAmt.toString();
    appliedamtwordsctrl.text = widget.loan.appliedLoanAmtinWords.toString();
    loanreasonctrl.text = widget.loan.loanReason.toString();
    surityname1ctrl.text = widget.loan.surityName1.toString();
    surityname2ctrl.text = widget.loan.surityName2.toString();
    acno1ctrl.text = widget.loan.sAcNo1.toString();
    acno2ctrl.text = widget.loan.sAcNo2.toString();
    addressctrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.permanentAddress ??
        "";
    ltinstallctrl.text = widget.loan.monthlyInstallmentAmt.toString();
  }

  num longtermloanShareinRupees = 0;
  num currentLoanShareAmountDeduction = 0;

  calShareValue() {
    final ctrl = Get.find<HomeCtrl>();

    final user = ctrl.users
        .firstWhereOrNull((element) => element.docId == widget.loan.uid);

    final currentShareValue = user?.totalShares;

    num shareValueinRupees = (widget.loan.appliedLoanAmt) / 10;

    if (shareValueinRupees > 20000) {
      shareValueinRupees = 20000;
    }

    if (currentShareValue != 0 && shareValueinRupees == 20000) {
      longtermloanShareinRupees = 20000 - (currentShareValue ?? 0);
      currentLoanShareAmountDeduction = longtermloanShareinRupees;
      // appliedamtctrl.text =
      //     (widget.loan.appliedLoanAmt - longtermloanShareinRupees).toString();
    }

    if (currentShareValue == 0) {
      currentLoanShareAmountDeduction = shareValueinRupees;
      longtermloanShareinRupees = shareValueinRupees;

      // appliedamtctrl.text =
      //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
    } else if (currentShareValue! < 20000 && currentShareValue > 0) {
      num remainingShareValue = 20000 - (currentShareValue);

      if (shareValueinRupees <= remainingShareValue) {
        // appliedamtctrl.text =
        //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
        longtermloanShareinRupees = currentShareValue + shareValueinRupees;
        currentLoanShareAmountDeduction = shareValueinRupees;
      } else {
        longtermloanShareinRupees = 20000;
        // appliedamtctrl.text =
        //     (widget.loan.appliedLoanAmt - remainingShareValue).toString();
        currentLoanShareAmountDeduction = remainingShareValue;
        // }
      }
      if (shareValueinRupees == 20000 && currentLoanShareAmountDeduction == 0) {
        longtermloanShareinRupees = shareValueinRupees;
        // appliedamtctrl.text =
        //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
        currentLoanShareAmountDeduction = shareValueinRupees;
      }
    } else {
      longtermloanShareinRupees = user?.totalShares ?? 0;
      // appliedamtctrl.text = widget.loan.appliedLoanAmt.toString();
      currentLoanShareAmountDeduction = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Get.find<HomeCtrl>()
        .users
        .firstWhereOrNull((element) => element.docId == widget.loan.uid);

    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 15, bottom: 30),
      child: Column(
        children: [
          ltFormHeader(),
          const SizedBox(height: 15),
          CustomTextfield(
            text: 'Full Name',
            controller: fullnamectrl,
            enabled: enabled,
          ),
          CustomTextfield(
              enabled: enabled, text: 'Designation', controller: desigctrl),
          CustomTextfield(
              keyboardType: TextInputType.number,
              enabled: enabled,
              text: 'Account No.',
              controller: bacnoctrl),
          CustomTextfield(
              enabled: enabled, text: 'Full Address', controller: addressctrl),
          CustomTextfield(
              keyboardType: TextInputType.number,
              enabled: enabled,
              text: 'Loan Applied in Rs.',
              controller: appliedamtctrl),
          CustomTextfield(
              enabled: enabled,
              text: 'Loan Amount in Words',
              controller: appliedamtwordsctrl),
          CustomTextfield(
              enabled: enabled,
              text: 'Reason for Loan',
              controller: loanreasonctrl),
          // borrowerSignRow(context),
          const SizedBox(height: 15),
          const Divider(
            color: Colors.black,
            thickness: 2,
          ),
          // const SizedBox(height: 15),
          const SizedBox(height: 15),
          CustomTextfield(
              enabled: enabled,
              text: 'Shri/Smt. (BORROWER)',
              controller: fullnamectrl),
          const SizedBox(height: 15),
          //borrrowers sign
          Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
                height: 80,
                width: 80,
                child: Image.network(widget.loan.bSign ?? "")),
          ),
          const SizedBox(height: 15),
          const Text(" ( 1. ) "),
          const SizedBox(height: 15),
          CustomTextfield(
              enabled: enabled,
              text: "Name of the first surity person",
              controller: surityname1ctrl),
          CustomTextfield(
              keyboardType: TextInputType.number,
              enabled: enabled,
              text: "Account No. of the first surity person",
              controller: acno1ctrl),
          const SizedBox(height: 15),
          //surity 1 sign
          // Align(
          //   alignment: Alignment.centerRight,
          //   child: SizedBox(
          //       height: 80,
          //       width: 80,
          //       child: Image.network(widget.loan.suritySign1)),
          // ),
          // const SizedBox(height: 15),
          const Text(" ( 2. ) "),
          const SizedBox(height: 15),
          CustomTextfield(
              enabled: enabled,
              text: "Name of the second surity person",
              controller: surityname2ctrl),
          CustomTextfield(
              enabled: enabled,
              keyboardType: TextInputType.number,
              text: "Account No. of the second surity person",
              controller: acno2ctrl),
          CustomTextfield(
              text: "Installment Amount",
              controller: ltinstallctrl,
              enabled: enabled),
          const SizedBox(height: 15),
          Row(
            children: [
              Checkbox(
                value: ltcheckbox,
                onChanged: (value) {
                  setState(() {
                    ltcheckbox = value!;
                  });
                },
              ),
              Text(
                "I acknowledge that I have verified the loan details and hereby grant approval for this loan application.",
                style: TextStyle(fontSize: 15),
              ),
            ],
          ),
          const SizedBox(height: 60),

          widget.loan.rejectionDate == null ||
                  widget.loan.rejectionReason == null
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    //Reject Button
                    ltOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : LtRejectButton(
                            reasonctrl: ltreasonctrl,
                            widget: widget,
                            onPressed: () async {
                              if (ltreasonctrl.text.isEmpty) {
                                return showCtcAppSnackBar(
                                    context, "Enter Loan Rejection Reason");
                              }
                              // if (!mounted) return;
                              setState(() {
                                ltOnSubmitLoad = true;
                              });
                              await FBFireStore.loan
                                  .doc(widget.loan.docId)
                                  .update({
                                'isNew': false,
                                'rejectionDate': Timestamp.now(),
                                'rejectionReason': ltreasonctrl.text.toString(),
                              });

                              await FBFireStore.notifications.add({
                                'uId': user?.docId,
                                'title': "Loan Rejected",
                                'desc':
                                    "₹${num.tryParse(appliedamtctrl.text.toString())} , REASON :${ltreasonctrl.text.toString()}",
                                'type': "loanRejected",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.now(),
                              });
                              context.pop();
                              if (mounted) {
                                setState(() {
                                  ltOnSubmitLoad = false;
                                });
                              }
                            },
                          ),
                    //ACCEPT Button
                    ltOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : LtAcceptButton(
                            enabled: !ltOnSubmitLoad && enabled,
                            onPressed: () async {
                              // final now = DateTime.now();

                              if (!ltcheckbox) {
                                return showCtcAppSnackBar(
                                    context, "Please confirm the checkbox");
                              }

                              // if (now.day > 21) {
                              //   return showCtcAppSnackBar(context,
                              //       "Loan requests aren't accepted after the 21th.");
                              // }
                              calShareValue();

                              final batch = FBFireStore.fb.batch();

                              batch.update(
                                  FBFireStore.loan.doc(widget.loan.docId), {
                                // 'applicationNo': Get.find<HomeCtrl>()
                                //         .settings!
                                //         .applicationNo +
                                //     1,
                                'totalLoanAmt': num.tryParse(
                                    widget.loan.appliedLoanAmt.toString()),
                                'totalLoanPaid': 0,
                                'totalLoanDue': num.tryParse(
                                    appliedamtctrl.text.toString()),
                                'appliedLoanAmt': num.tryParse(
                                    widget.loan.appliedLoanAmt.toString()),
                                'isSettled': false,
                                'isNew': false,
                                'approvedOn': Timestamp.now(), // Nullable
                                'processedOn': null, // Nullable
                                'share': longtermloanShareinRupees,
                                'totalInterestPaid': 0,
                                'balance': null,
                                'monthlyInstallmentAmt':
                                    num.tryParse(ltinstallctrl.text)
                              });

                              final userMonthlyDoc = await FBFireStore
                                  .usermonthly
                                  .where('cpfNo', isEqualTo: user?.cpfNo)
                                  .where('selectedmonth',
                                      isEqualTo: DateTime.now().month)
                                  .where('selectedyear',
                                      isEqualTo: DateTime.now().year)
                                  .get();

                              batch.update(
                                  FBFireStore.users.doc(widget.loan.uid), {
                                'ltLoansDue': FieldValue.increment(num.tryParse(
                                        appliedamtctrl.text.toString()) ??
                                    0),
                                'totalLtLoans': FieldValue.increment(
                                    num.tryParse(
                                            appliedamtctrl.text.toString()) ??
                                        0),
                                'totalShares': longtermloanShareinRupees
                              });
                              // batch.update(FBFireStore.settings,
                              //     {'applicationNo': FieldValue.increment(1)});

                              //updating shareValue in usermonthly

                              if (userMonthlyDoc.docs.isNotEmpty) {
                                batch.update(
                                  FBFireStore.usermonthly
                                      .doc(userMonthlyDoc.docs.first.id),
                                  {
                                    'shareValue': FieldValue.increment(
                                        longtermloanShareinRupees),
                                  },
                                );
                              }

                              final socYearlyDoc = await FBFireStore
                                  .societyYearly
                                  .where('selectedyear',
                                      isEqualTo: DateTime.now().year)
                                  .get();

                              if (socYearlyDoc.docs.isNotEmpty) {
                                batch.update(
                                    FBFireStore.societyYearly
                                        .doc(socYearlyDoc.docs.first.id),
                                    {
                                      'totalShareGiven': FieldValue.increment(
                                          longtermloanShareinRupees),
                                    });
                              }

                              batch.set(FBFireStore.transactions.doc(), {
                                "uId": widget.loan.uid,
                                "createdAt": Timestamp.fromDate(DateTime.now()),
                                "title": "Loan Approved",
                                "amount": num.tryParse(
                                    widget.loan.appliedLoanAmt.toString()),
                                "inn": true,
                                "userMonthlyId": null,
                                "recoveryId": null,
                                "loanId": widget.loan.docId,
                              });
                              batch.set(FBFireStore.transactions.doc(), {
                                "uId": widget.loan.uid,
                                "createdAt": Timestamp.fromDate(
                                    DateTime.now().add(Duration(seconds: 10))),
                                "title": "Share Deduction 10%",
                                "amount": currentLoanShareAmountDeduction,
                                "inn": false,
                                "userMonthlyId": null,
                                "recoveryId": null,
                                "loanId": widget.loan.docId,
                              });

                              batch.set(FBFireStore.notifications.doc(), {
                                'uId': widget.loan.uid,
                                'title': "Loan Approved",
                                'desc':
                                    "Loan approved of ${num.tryParse(widget.loan.appliedLoanAmt.toString())}",
                                'type': "loanAccepted",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.fromDate(DateTime.now()),
                              });

                              batch.set(FBFireStore.notifications.doc(), {
                                'uId': widget.loan.uid,
                                'title': "Share Deduction 10%",
                                'desc':
                                    "Share Deduction 10% : $currentLoanShareAmountDeduction",
                                'type': "shareDeduction",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.fromDate(
                                    DateTime.now().add(Duration(seconds: 10))),
                              });

                              try {
                                setState(() {
                                  ltOnSubmitLoad = true;
                                });
                                await batch.commit();
                                if (mounted) {
                                  context.pop();
                                  showCtcAppSnackBar(
                                      duration:
                                          const Duration(milliseconds: 2000),
                                      context,
                                      "Loan Applied Successfully");
                                }
                              } catch (e) {
                                debugPrint("Batch failed: ${e.toString()}");
                              } finally {
                                if (mounted) {
                                  setState(() {
                                    ltOnSubmitLoad = false;
                                  });
                                }
                              }
                            },
                            snackbarKey: snackbarKey,
                          ),

                    //Edit Button
                    ltOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : LtEditButton(
                            onPressed: () {
                              if (!mounted) return;
                              setState(() {
                                enabled = true;
                              });
                            },
                          ),
                  ],
                )
              : SizedBox.shrink()
        ],
      ),
    );
    // );
  }

  Row ltFormHeader() {
    final appNo = Get.find<HomeCtrl>().settings!.applicationNo;

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // const Image(height: 50, image: AssetImage('assets/foodcorpimage2.png')),

        Row(
          children: [
            Text("Application No. : $appNo"),
          ],
        )
      ],
    );
  }

  Row surity2SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s2sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s2sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity2signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }
                          // print("Surity2 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity2signatureImage = null;
                          s2sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity2signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity2signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row surity1SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s1sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s1sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity1signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }

                          // print("Surity1 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity1signatureImage = null;
                          s1sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity1signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity1signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        // print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text("Submit"),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text("Clear Signature"),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}
  // const SizedBox(width: 5),
            // SizedBox(
            //   width: 85,
            //   // height: 28,
            //   child: TextFormField(
            //     decoration: const InputDecoration(
            //       border: OutlineInputBorder(),
            //     ),
            //     // cursorHeight: 10,
            //     validator: (value) {
            //       if (value == null || value.isEmpty) {
            //         return showAppSnackBar(
            //             'Application Number cannot be empty');
            //       }
            //       if (value == '0') {
            //         return showAppSnackBar('Application Number cannot be 0');
            //       }
            //       return null;
            //     },
            //     // enabled: true,
            //     keyboardType: TextInputType.number,
            //     controller: appnoctrl,
            //     // decoration: const InputDecoration(border: OutlineInputBorder()),
            //   ),
            // ),
  // calShareValue();
                              // await FBFireStore.loan
                              //     .doc(widget.loan.docId)
                              //     .update({
                              //   'share': longtermloanShareinRupees *
                              //       (Get.find<HomeCtrl>()
                              //               .settings
                              //               ?.shareValue ??
                              //           0),
                              //   'appliedLoanAmt':
                              //       num.tryParse(appliedamtctrl.text.toString())
                              // });
                              // await FBFireStore.users.doc(user?.docId).update({
                              //   'totalShares': FieldValue.increment(
                              //       longtermloanShareinRupees *
                              //           (Get.find<HomeCtrl>()
                              //                   .settings
                              //                   ?.shareValue ??
                              //               0)),
                              //   'ltLoansDue':
                              //       num.tryParse(appliedamtctrl.text.toString())
                              // });
                              // await FBFireStore.transactions.add({
                              //   "uId": widget.loan.uid,
                              //   "createdAt": Timestamp.now(),
                              //   "title": "Share Deduction 10%",
                              //   "amount": longtermloanShareinRupees *
                              //       (Get.find<HomeCtrl>()
                              //               .settings
                              //               ?.shareValue ??
                              //           0),
                              //   "inn": false,
                              //   "userMonthlyId": null,
                              //   "recoveryId": null,
                              // });
                              // FBFireStore.notifications.add({
                              //   'uId': widget.loan.uid,
                              //   'title': "Share Deduction 10%",
                              //   'desc':
                              //       "Share Deduction 10% : ${longtermloanShareinRupees * (Get.find<HomeCtrl>().settings?.shareValue ?? 0)}",
                              //   'type': "shareDeduction",
                              //   'districtOffice': user?.districtoffice,
                              //   'createdAt': Timestamp.now(),
                              // });