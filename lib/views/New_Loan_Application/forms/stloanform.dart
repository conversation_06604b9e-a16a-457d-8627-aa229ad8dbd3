// ignore_for_file: use_build_context_synchronously, avoid_print

import 'dart:typed_data';
import 'dart:ui';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/views/New_Loan_Application/buttons/ST/st_accept_button.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:signature/signature.dart';
import '../../../models/loan_model.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../buttons/ST/st_edit_button.dart';
import '../buttons/ST/st_reject_button.dart';

class Stloanform extends StatefulWidget {
  const Stloanform({super.key, required this.index, required this.loan});

  final int index;
  final Loan loan;

  @override
  State<Stloanform> createState() => _StloanformState();
}

class _StloanformState extends State<Stloanform> {
  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController acnoctrl = TextEditingController();
  TextEditingController numamtctrl = TextEditingController();
  TextEditingController amtctrl = TextEditingController();
  TextEditingController surity1ctrl = TextEditingController();
  TextEditingController surity2ctrl = TextEditingController();
  TextEditingController balctrl = TextEditingController();
  TextEditingController streasonctrl = TextEditingController();
  TextEditingController stinstallctrl = TextEditingController();
  TextEditingController loanreasonctrl = TextEditingController();

  Uint8List? borrowersignatureImage;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool stOnSubmitLoad = false;
  bool stcheckbox = false;

  // Future<String?> uploadSignimages(Uint8List file) async {
  //   try {
  //     final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
  //     final imageRef = FBStorage.fbstore.ref().child(path);
  //     final task = await imageRef.putData(file);
  //     var downloadurl = await task.ref.getDownloadURL();
  //     return await task.ref.getDownloadURL();
  //   } catch (e) {}
  // }

  @override
  void initState() {
    super.initState();
    appnoctrl.text = widget.loan.applicationNo.toString();
    fullnamectrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.name ??
        "";
    acnoctrl.text = widget.loan.bAcNo.toString();
    numamtctrl.text = widget.loan.appliedLoanAmt.toString();
    amtctrl.text = widget.loan.appliedLoanAmtinWords.toString();
    surity1ctrl.text = widget.loan.surityName1.toString();
    surity2ctrl.text = widget.loan.surityName2.toString();
    // balctrl.text = widget.loan.balance.toString();
    loanreasonctrl.text = widget.loan.loanReason.toString();
    stinstallctrl.text =
        Get.find<HomeCtrl>().settings?.defaultStinstallAmt.toString() ?? "0";
  }

  bool enabled = false;

  num shorttermloanShareRupees = 0;
  num currentLoanShareAmountDeduction = 0;

  calShareValue() {
    final ctrl = Get.find<HomeCtrl>();
    final user = ctrl.users
        .firstWhereOrNull((element) => element.docId == widget.loan.uid);

    final currentShareValue = user?.totalShares;

    num shareValueinRupees = (widget.loan.appliedLoanAmt) / 10;
    if (shareValueinRupees > 20000) {
      shareValueinRupees = 20000;
    }
    if (currentShareValue == 0) {
      shorttermloanShareRupees =
          shareValueinRupees; //  / (ctrl.settings?.shareValue ?? 0);

      // numamtctrl.text =
      //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
      currentLoanShareAmountDeduction = shareValueinRupees;
    } else if (currentShareValue! < 20000 && currentShareValue > 0) {
      num remainingShareValue = 20000 - (currentShareValue);
      if (shareValueinRupees <= remainingShareValue) {
        // numamtctrl.text =
        //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
        shorttermloanShareRupees = currentShareValue + shareValueinRupees;
        currentLoanShareAmountDeduction = shareValueinRupees;
      } else {
        // if (shareValueinRupees >= remainingShareValue) {
        shorttermloanShareRupees = 20000;
        // numamtctrl.text =
        //     (widget.loan.appliedLoanAmt - remainingShareValue).toString();
        currentLoanShareAmountDeduction = remainingShareValue;
        // }
      }

      if (shareValueinRupees == 20000 && currentLoanShareAmountDeduction == 0) {
        shorttermloanShareRupees = shareValueinRupees;
        // numamtctrl.text =
        //     (widget.loan.appliedLoanAmt - shareValueinRupees).toString();
        currentLoanShareAmountDeduction = shareValueinRupees;
      }
    } else {
      // if (currentShareValue == 20000) {
      shorttermloanShareRupees = user?.totalShares ?? 0;
      // numamtctrl.text = widget.loan.appliedLoanAmt.toString();
      currentLoanShareAmountDeduction = 0;
      // }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = Get.find<HomeCtrl>()
        .users
        .firstWhereOrNull((element) => element.docId == widget.loan.uid);
    // calShareValue();
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 15, bottom: 30),
      child: Column(
        children: [
          StFormHeader(
            appnoctrl: appnoctrl,
            enabled: enabled,
          ),
          CustomTextfield(
              keyboardType: TextInputType.number,
              enabled: enabled,
              text: 'Account No.',
              controller: acnoctrl),
          // CustomTextfield(
          //     keyboardType: TextInputType.number,
          //     enabled: enabled,
          //     text: 'Balance',
          //     controller: balctrl),
          CustomTextfield(
              enabled: enabled, text: 'Full Name', controller: fullnamectrl),
          CustomTextfield(
              keyboardType: TextInputType.number,
              enabled: enabled,
              text: 'Loan Applied in Rs.',
              controller: numamtctrl),
          CustomTextfield(
              enabled: enabled,
              text: 'Loan Amount in Words',
              controller: amtctrl),
          const SizedBox(height: 15),

          Align(
            alignment: Alignment.centerRight,
            child: SizedBox(
                height: 80,
                width: 80,
                child: Image.network(widget.loan.bSign ?? "")),
          ),

          // borrowerSignRow(context),
          const SizedBox(height: 15),
          // const Text(" ( 1. ) "),
          CustomTextfield(
              enabled: enabled,
              text: "Name of the first surity person",
              controller: surity1ctrl),
          // surity1SignRow(context),
          const SizedBox(height: 15),

          // Align(
          //   alignment: Alignment.centerRight,
          //   child: SizedBox(
          //       height: 80,
          //       width: 80,
          //       child: Image.network(widget.loan.suritySign1)),
          // ),
          // const SizedBox(height: 15),
          // const Text(" ( 2. ) "),
          CustomTextfield(
              enabled: enabled,
              text: "Name of the second surity person",
              controller: surity2ctrl),
          // surity2SignRow(context),
          // const SizedBox(height: 15),

          // Align(
          //   alignment: Alignment.centerRight,
          //   child: SizedBox(
          //       height: 80,
          //       width: 80,
          //       child: Image.network(widget.loan.suritySign2)),
          // ),
          // const SizedBox(height: 60),
          CustomTextfield(
              text: "Installment Amount",
              controller: stinstallctrl,
              enabled: enabled),

          CustomTextfield(
              text: "Loan Reason",
              controller: loanreasonctrl,
              enabled: enabled),
          const SizedBox(height: 15),
          Row(
            children: [
              Checkbox(
                value: stcheckbox,
                onChanged: (value) {
                  setState(() {
                    stcheckbox = value!;
                  });
                },
              ),
              Text(
                "I acknowledge that I have verified the loan details and hereby grant approval for this loan application.",
                style: TextStyle(fontSize: 15),
              ),
            ],
          ),
          const SizedBox(height: 60),

          widget.loan.rejectionDate == null ||
                  widget.loan.rejectionReason == null
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    //Reject Button
                    stOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : StRejectButton(
                            reasonctrl: streasonctrl,
                            widget: widget,
                            onPressed: () async {
                              if (streasonctrl.text.isEmpty) {
                                return showCtcAppSnackBar(
                                    context, "Enter Loan Rejection Reason");
                              }
                              await FBFireStore.loan
                                  .doc(widget.loan.docId)
                                  .update({
                                'isNew': false,
                                'rejectionDate': Timestamp.now(),
                                'rejectionReason': streasonctrl.text.toString(),
                              });
                              await FBFireStore.notifications.add({
                                'uId': user?.docId,
                                'title': "Loan Rejected",
                                'desc':
                                    "₹${num.tryParse(numamtctrl.text.toString())} , REASON :${streasonctrl.text.toString()}",
                                'type': "loanRejected",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.now(),
                              });
                              if (mounted) {
                                setState(() {
                                  stOnSubmitLoad = false;
                                });
                              }
                            },
                          ),
                    //Accept Button
                    stOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : StAcceptButton(
                            enabled: !stOnSubmitLoad && enabled,
                            onPressed: () async {
                              // final now = DateTime.now();

                              if (!stcheckbox) {
                                return showCtcAppSnackBar(
                                    context, "Please confirm the checkbox");
                              }

                              // if (now.day > 21) {
                              //   return showCtcAppSnackBar(context,
                              //       "Loan requests aren't accepted after the 21th.");
                              // }
                              calShareValue();

                              final batch = FBFireStore.fb.batch();
                              batch.update(
                                  FBFireStore.loan.doc(widget.loan.docId), {
                                // 'applicationNo': num.tryParse(appnoctrl.text),
                                'totalLoanAmt': num.tryParse(widget
                                        .loan.appliedLoanAmt
                                        .toString()) ??
                                    0,
                                'totalLoanDue':
                                    num.tryParse(numamtctrl.text.toString()),
                                'appliedLoanAmt': num.tryParse(widget
                                        .loan.appliedLoanAmt
                                        .toString()) ??
                                    0,
                                'isSettled': false,
                                'isNew': false,
                                'approvedOn': Timestamp.now(), // Nullable
                                'processedOn': null, // Nullable
                                // 'balance': num.tryParse(balctrl.text),
                                'monthlyInstallmentAmt':
                                    num.tryParse(stinstallctrl.text),
                                'loanReason': widget.loan.loanReason ?? "",
                                'bAcno': widget.loan.bAcNo,
                                'share': shorttermloanShareRupees,
                              });

                              batch.update(
                                  FBFireStore.users.doc(widget.loan.uid), {
                                'stLoansDue': FieldValue.increment(
                                    num.tryParse(numamtctrl.text.toString()) ??
                                        0),
                                'totalStLoans': FieldValue.increment(
                                    num.tryParse(numamtctrl.text.toString()) ??
                                        0),
                                'totalShares': shorttermloanShareRupees,
                              });
                              final userMonthlyDoc = await FBFireStore
                                  .usermonthly
                                  .where('cpfNo', isEqualTo: user?.cpfNo)
                                  .where('selectedmonth',
                                      isEqualTo: DateTime.now().month)
                                  .where('selectedyear',
                                      isEqualTo: DateTime.now().year)
                                  .get();

                              if (userMonthlyDoc.docs.isNotEmpty) {
                                batch.update(
                                  FBFireStore.usermonthly
                                      .doc(userMonthlyDoc.docs.first.id),
                                  {
                                    'shareValue': FieldValue.increment(
                                        shorttermloanShareRupees),
                                  },
                                );
                              }

                              final socYearlyDoc = await FBFireStore
                                  .societyYearly
                                  .where('selectedyear',
                                      isEqualTo: DateTime.now().year)
                                  .get();

                              if (socYearlyDoc.docs.isNotEmpty) {
                                batch.update(
                                    FBFireStore.societyYearly
                                        .doc(socYearlyDoc.docs.first.id),
                                    {
                                      'totalShareGiven': FieldValue.increment(
                                          shorttermloanShareRupees),
                                    });
                              }

                              batch.set(FBFireStore.transactions.doc(), {
                                "uId": widget.loan.uid,
                                "createdAt": Timestamp.fromDate(DateTime.now()),
                                "title": "Loan Approved",
                                "amount": num.tryParse(
                                    widget.loan.appliedLoanAmt.toString()),
                                "inn": true,
                                "userMonthlyId": null,
                                "recoveryId": null,
                                "loanId": widget.loan.docId,
                              });
                              batch.set(FBFireStore.transactions.doc(), {
                                "uId": widget.loan.uid,
                                "createdAt": Timestamp.fromDate(
                                    DateTime.now().add(Duration(seconds: 10))),
                                "title": "Share Deduction 10%",
                                "amount": currentLoanShareAmountDeduction,
                                "inn": false,
                                "userMonthlyId": null,
                                "recoveryId": null,
                                "loanId": widget.loan.docId,
                              });

                              batch.set(FBFireStore.notifications.doc(), {
                                'uId': widget.loan.uid,
                                'title': "Loan Approved",
                                'desc':
                                    "Loan approved of ${num.tryParse(widget.loan.appliedLoanAmt.toString())}",
                                'type': "loanAccepted",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.fromDate(DateTime.now()),
                              });
                              batch.set(FBFireStore.notifications.doc(), {
                                'uId': widget.loan.uid,
                                'title': "Share Deduction 10%",
                                'desc':
                                    "Share Deduction 10% : $currentLoanShareAmountDeduction",
                                'type': "shareDeduction",
                                'districtOffice': user?.districtoffice,
                                'createdAt': Timestamp.fromDate(
                                    DateTime.now().add(Duration(seconds: 10))),
                              });

                              try {
                                setState(() {
                                  stOnSubmitLoad = true;
                                });
                                await batch.commit();
                                if (mounted) {
                                  context.pop();
                                  showCtcAppSnackBar(
                                      duration:
                                          const Duration(milliseconds: 2000),
                                      context,
                                      "Loan Applied Successfully");
                                }
                              } catch (e) {
                                debugPrint("Batch failed: ${e.toString()}");
                              } finally {
                                if (mounted) {
                                  setState(() {
                                    stOnSubmitLoad = false;
                                  });
                                }
                              }
                            },
                          ),
                    //Edit Button
                    stOnSubmitLoad
                        ? const CircularProgressIndicator()
                        : StEditButton(
                            onPressed: () {
                              setState(() {
                                enabled = true;
                              });
                            },
                          ),
                  ],
                )
              : SizedBox.shrink()
        ],
      ),
    );
  }

  Row surity2SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s2sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s2sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity2signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }
                          // print("Surity2 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity2signatureImage = null;
                          s2sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity2signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity2signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row surity1SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s1sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s1sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity1signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }

                          // print("Surity1 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity1signatureImage = null;
                          s1sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity1signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity1signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        // print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text("Submit"),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text("Clear Signature"),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}

class StFormHeader extends StatelessWidget {
  const StFormHeader({
    super.key,
    required this.appnoctrl,
    required this.enabled,
  });
  final TextEditingController appnoctrl;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    final stAppNo = Get.find<HomeCtrl>().settings!.applicationNo;
    return Row(mainAxisAlignment: MainAxisAlignment.end, children: [
      // const Image(height: 50, image: AssetImage('assets/foodcorpimage2.png')),
      Row(children: [
        Text("Application No. : $stAppNo"),
      ])
    ]);
  }
}

 // await FBFireStore.loan
                              //     .doc(widget.loan.docId)
                              //     .update({
                              //   'share': shorttermloanShareRupees *
                              //       (Get.find<HomeCtrl>()
                              //               .settings
                              //               ?.shareValue ??
                              //           0),
                              //   'appliedLoanAmt':
                              //       num.tryParse(numamtctrl.text.toString())
                              // });

                              // await FBFireStore.users.doc(user?.docId).update({
                              //   'totalShares': FieldValue.increment(
                              //       shorttermloanShareRupees *
                              //           (Get.find<HomeCtrl>()
                              //                   .settings
                              //                   ?.shareValue ??
                              //               0)),
                              //   'stLoansDue':
                              //       num.tryParse(numamtctrl.text.toString())
                              // });

                              // await FBFireStore.transactions.add({
                              //   "uId": widget.loan.uid,
                              //   "createdAt": Timestamp.now(),
                              //   "title": "Share Deduction 10%",
                              //   "amount": shorttermloanShareRupees *
                              //       (Get.find<HomeCtrl>()
                              //               .settings
                              //               ?.shareValue ??
                              //           0),
                              //   "inn": false,
                              //   "userMonthlyId": null,
                              //   "recoveryId": null,
                              // });

                              // FBFireStore.notifications.add({
                              //   'uId': widget.loan.uid,
                              //   'title': "Share Deduction 10%",
                              //   'desc':
                              //       "Share Deduction 10% : ${shorttermloanShareRupees * (Get.find<HomeCtrl>().settings?.shareValue ?? 0)}",
                              //   'type': "shareDeduction",
                              //   'districtOffice': user?.districtoffice,
                              //   'createdAt': Timestamp.now(),
                              // });