// ignore_for_file: use_build_context_synchronously, deprecated_member_use
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateRequestPage extends StatefulWidget {
  const UpdateRequestPage({super.key});

  @override
  State<UpdateRequestPage> createState() => _UpdateRequestPageState();
}

class _UpdateRequestPageState extends State<UpdateRequestPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      final pendingRequests =
          ctrl.updateRequests.where((req) => req.accepted == null).toList();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            CustomHeaderButton(
                onPressed: () {
                  context.push(Routes.updaterequesthistory);
                },
                buttonName: "Earlier Applications"),
            const SizedBox(height: 50),
            Row(
              children: const [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: 'CpfNo'),
                HeaderTxt(txt: "User Name"),
                HeaderTxt(txt: "Request Type"),
                SizedBox(width: 50),
                HeaderTxt(txt: "Requested On"),
                HeaderTxt(txt: "Status"),
              ],
            ),
            const SizedBox(height: 20),
            if (pendingRequests.isEmpty)
              Center(
                child: const Text(
                  "No pending requests.",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              )
            else
              ...List.generate(pendingRequests.length, (index) {
                final updateRequest = pendingRequests[index];
                final user = ctrl.users.firstWhere(
                    (element) => element.docId == updateRequest.uId);

                final bool isAlreadyDecided = updateRequest.accepted != null;

                return _buildRequestRow(
                    index: index,
                    user: user,
                    updateRequest: updateRequest,
                    isAlreadyDecided: isAlreadyDecided,
                    dialogContext: context);
              }),
          ],
        ),
      );
    });
  }

  Widget _buildRequestRow({
    required int index,
    required UserModel user,
    required dynamic updateRequest,
    required bool isAlreadyDecided,
    required BuildContext dialogContext,
  }) {
    return InkWell(
      onTap: () => _showRequestDetails(updateRequest, user, isAlreadyDecided),
      child: Container(
        height: 40,
        color: index % 2 == 0 ? Colors.white : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(children: [
            Expanded(child: Text("${index + 1}")),
            Expanded(child: Text(user.cpfNo.toString())),
            Expanded(child: Text(user.name)),
            Expanded(
              child: Text(
                updateRequest.requestType,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 50),
            Expanded(
              child: Text(
                DateFormat('dd-MM-yyyy').format(updateRequest.createdAt),
              ),
            ),
            Expanded(
              child: Text(
                updateRequest.accepted == null
                    ? "Pending"
                    : (updateRequest.accepted! ? "Accepted" : "Rejected"),
              ),
            )
          ]),
        ),
      ),
    );
  }

  void _showRequestDetails(updateRequest, user, bool isAlreadyDecided) {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, dialogSetState) {
          bool isLoading = false;
          return AlertDialog(
            title: const Text('Request Details'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow("User Name:", user.name),
                  _buildDetailRow("Request Type:", updateRequest.requestType),
                  _buildDetailRow("Remarks:", updateRequest.remarks),
                  _buildDetailRow("Document Name:", updateRequest.docName),
                  _buildDetailRow(
                    "Status:",
                    updateRequest.accepted == null
                        ? "Pending"
                        : (updateRequest.accepted! ? "Accepted" : "Rejected"),
                  ),
                  _buildDetailRow(
                    "Requested On:",
                    DateFormat('dd-MM-yyyy').format(updateRequest.createdAt),
                  ),
                  if ((updateRequest.supportiveDoc ?? "").isNotEmpty)
                    Row(
                      children: [
                        const Text(
                          "Supportive Document: ",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        IconButton(
                          icon: const Icon(Icons.download_rounded,
                              color: Colors.blue),
                          tooltip: "Download Supportive Document",
                          onPressed: () async {
                            final url = updateRequest.supportiveDoc!;
                            if (await canLaunch(url)) {
                              await launch(url);
                            } else {
                              showCtcAppSnackBar(
                                  context, 'Could not launch document URL');
                            }
                          },
                        ),
                      ],
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: isLoading || isAlreadyDecided
                    ? null
                    : () async {
                        dialogSetState(() => isLoading = true);
                        await FBFireStore.requests
                            .doc(updateRequest.docId)
                            .update({'accepted': false});
                        Navigator.of(context).pop();
                        dialogSetState(() => isLoading = false);
                      },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Reject'),
              ),
              TextButton(
                onPressed: isLoading || isAlreadyDecided
                    ? null
                    : () async {
                        dialogSetState(() => isLoading = true);
                        try {
                          await FBFireStore.requests
                              .doc(updateRequest.docId)
                              .update({'accepted': true});
                          Navigator.of(context).pop();
                          showCtcAppSnackBar(
                              context, 'Request accepted successfully!');
                        } catch (e) {
                          dialogSetState(() => isLoading = false);
                          showCtcAppSnackBar(
                              context, 'Failed to accept request: $e');
                        }
                      },
                style: TextButton.styleFrom(foregroundColor: Colors.green),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Confirm'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: RichText(
        text: TextSpan(
          style: const TextStyle(color: Colors.black, fontSize: 14),
          children: [
            TextSpan(
                text: "$label ",
                style: const TextStyle(fontWeight: FontWeight.bold)),
            TextSpan(text: value),
          ],
        ),
      ),
    );
  }
}
