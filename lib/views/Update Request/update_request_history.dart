// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateRequestHistory extends StatelessWidget {
  const UpdateRequestHistory({super.key});

  void _showRequestDetails(BuildContext context, updateRequest, user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow("User Name:", user.name),
              _buildDetailRow("Request Type:", updateRequest.requestType),
              _buildDetailRow("Remarks:", updateRequest.remarks),
              _buildDetailRow("Document Name:", updateRequest.docName),
              _buildDetailRow(
                "Status:",
                updateRequest.accepted == null
                    ? "Pending"
                    : (updateRequest.accepted! ? "Accepted" : "Rejected"),
              ),
              _buildDetailRow(
                "Requested On:",
                DateFormat('dd-MM-yyyy').format(updateRequest.createdAt),
              ),
              if ((updateRequest.supportiveDoc ?? "").isNotEmpty)
                Row(
                  children: [
                    const Text(
                      "Supportive Document: ",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: const Icon(Icons.download_rounded,
                          color: Colors.blue),
                      tooltip: "Download Supportive Document",
                      onPressed: () async {
                        final url = updateRequest.supportiveDoc!;
                        if (await canLaunch(url)) {
                          await launch(url);
                        } else {
                          showCtcAppSnackBar(
                              context, 'Could not launch document URL');
                        }
                      },
                    ),
                  ],
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  static Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: RichText(
        text: TextSpan(
          style: const TextStyle(color: Colors.black, fontSize: 14),
          children: [
            TextSpan(
                text: "$label ",
                style: const TextStyle(fontWeight: FontWeight.bold)),
            TextSpan(text: value),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      final historyRequests =
          ctrl.updateRequests.where((req) => req.accepted != null).toList();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 30),
            Row(
              children: const [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "User Name"),
                HeaderTxt(txt: "Request Type"),
                SizedBox(width: 50),
                HeaderTxt(txt: "Requested On"),
                HeaderTxt(txt: "Status"),
              ],
            ),
            const SizedBox(height: 10),
            if (historyRequests.isEmpty)
              const Text(
                "No completed/rejected requests yet.",
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              )
            else
              ...List.generate(historyRequests.length, (index) {
                final updateRequest = historyRequests[index];
                final user = ctrl.users.firstWhere(
                    (element) => element.docId == updateRequest.uId);

                return InkWell(
                  onTap: () =>
                      _showRequestDetails(context, updateRequest, user),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(children: [
                        Expanded(child: Text("${index + 1}")),
                        Expanded(child: Text(user.name)),
                        Expanded(
                          child: Text(
                            updateRequest.requestType,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 50),
                        Expanded(
                          child: Text(
                            DateFormat('dd-MM-yyyy')
                                .format(updateRequest.createdAt),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            updateRequest.accepted! ? "Accepted" : "Rejected",
                            style: TextStyle(
                                color: updateRequest.accepted!
                                    ? Colors.green
                                    : Colors.red),
                          ),
                        ),
                      ]),
                    ),
                  ),
                );
              }),
          ],
        ),
      );
    });
  }
}
