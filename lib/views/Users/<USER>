// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/districtoffice_model.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/transaction_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:foodcorp_admin/views/Loan/loan.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../models/user_model.dart';

class UserDetailsPage extends StatefulWidget {
  const UserDetailsPage({super.key, this.userdocId});
  final String? userdocId;
  // final UserModel user;

  @override
  State<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends State<UserDetailsPage>
    with SingleTickerProviderStateMixin {
  UserModel? usermodelw;
  late TabController tabCtrl;
  bool fullLoader = true;
  bool isLoading = false;
  bool addingTransLoading = false;

  TextEditingController userPinCtrl = TextEditingController();

  SelectedLoan selectedLoan = SelectedLoan.longtermloan;

  String? selectedType;
  bool isExpense = false;

  String _enteredPin = '';
  bool _pinVerified = false;

  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(length: 3, vsync: this);
    getData();
  }

  @override
  void dispose() {
    tabCtrl.dispose();
    super.dispose();
  }

  Future<void> getData() async {
    if (widget.userdocId != null) {
      final userData = await FBFireStore.users.doc(widget.userdocId).get();
      usermodelw = UserModel.fromSnap(userData);
    }
    setState(() => fullLoader = false);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    double h = size.height;

    return Scaffold(
      appBar: AppBar(
          title: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(usermodelw?.name.toUpperCase() ?? ""),
        ],
      )),
      body: GetBuilder<HomeCtrl>(builder: (ctrl) {
        List<Loan> filtered = ctrl.loan
            .where((element) => element.uid == usermodelw?.docId)
            .where((element) => selectedLoan == SelectedLoan.longtermloan
                ? element.loanType == 'Long Term Loan'
                : element.loanType == 'Emergency Loan')
            .where((loan) {
          final user = ctrl.users.firstWhereOrNull((u) => u.docId == loan.uid);
          final search = sctrl.text.toLowerCase();
          return loan.applicationNo.toString().contains(search) ||
              (user?.name.toLowerCase().contains(search) ?? false);
        }).toList();

        final transactions = ctrl.transactions
            .where((element) => element.uId == usermodelw?.docId)
            .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // ctrl.transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return fullLoader
            ? Center(
                child: SizedBox(
                    height: 30, width: 30, child: CircularProgressIndicator()))
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          child: SizedBox(
                            height: 40,
                            width: 500,
                            child: TabBar(
                              labelColor: Colors.black,
                              dividerColor: Colors.black,
                              indicatorSize: TabBarIndicatorSize.tab,
                              controller: tabCtrl,
                              indicatorColor: Colors.green.shade300,
                              onTap: (value) {
                                setState(() {});
                              },
                              tabs: [
                                Tab(
                                  child: Text(
                                    "Details",
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                Tab(
                                  child: Text(
                                    "Transactions",
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                                Tab(
                                  child: Text(
                                    "Loan",
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (tabCtrl.index == 1)
                          Padding(
                              padding: const EdgeInsets.only(right: 10),
                              child: Row(
                                children: [
                                  // CustomHeaderButton(
                                  //     onPressed: () => showDialog(
                                  //           barrierDismissible: false,
                                  //           context: context,
                                  //           builder: (context) =>
                                  //               StatefulBuilder(
                                  //             builder: (context, setState2) {
                                  //               return WillPopScope(
                                  //                 onWillPop: () async =>
                                  //                     !isLoading,
                                  //                 child: AlertDialog(
                                  //                   title:
                                  //                       Text("Add Transaction"),
                                  //                   content: Column(
                                  //                     mainAxisSize:
                                  //                         MainAxisSize.min,
                                  //                     children: [
                                  //                       CustomTextfield(
                                  //                         text: 'Title',
                                  //                         enabled: true,
                                  //                         keyboardType:
                                  //                             TextInputType
                                  //                                 .text,
                                  //                         onFieldSubmitted:
                                  //                             (p0) {},
                                  //                         controller: titleCtrl,
                                  //                       ),
                                  //                       CustomTextfield(
                                  //                         text: 'Amount',
                                  //                         enabled: true,
                                  //                         keyboardType:
                                  //                             TextInputType
                                  //                                 .number,
                                  //                         onFieldSubmitted:
                                  //                             (p0) {},
                                  //                         controller:
                                  //                             transAmtCtrl,
                                  //                       ),
                                  //                       Row(
                                  //                         children: [
                                  //                           Checkbox(
                                  //                             value: isExpense,
                                  //                             onChanged: (val) {
                                  //                               setState2(() {
                                  //                                 isExpense =
                                  //                                     val ??
                                  //                                         false;
                                  //                               });
                                  //                             },
                                  //                           ),
                                  //                           const Text(
                                  //                               'Is Expense'),
                                  //                         ],
                                  //                       ),
                                  //                       // SizedBox(height: 20),
                                  //                       // if (selectedType !=
                                  //                       //         null &&
                                  //                       //     !_pinVerified)
                                  //                       //   TextFormField(
                                  //                       //     decoration:
                                  //                       //         const InputDecoration(
                                  //                       //       filled: true,
                                  //                       //       border:
                                  //                       //           OutlineInputBorder(),
                                  //                       //       labelText:
                                  //                       //           'Enter PIN',
                                  //                       //     ),
                                  //                       //     controller:
                                  //                       //         userPinCtrl,
                                  //                       //     obscureText: true,
                                  //                       //     onChanged: (text) =>
                                  //                       //         setState(() =>
                                  //                       //             _enteredPin =
                                  //                       //                 text),
                                  //                       //     onFieldSubmitted:
                                  //                       //         (_) => _verifyPin(
                                  //                       //             setState2),
                                  //                       //   ),
                                  //                     ],
                                  //                   ),
                                  //                   backgroundColor:
                                  //                       Colors.white,
                                  //                   actions: [
                                  //                     TextButton(
                                  //                       onPressed: () async =>
                                  //                           addingTransLoading
                                  //                               ? CircularProgressIndicator()
                                  //                               : await handleAddTransactionOnPressed(
                                  //                                   setState2:
                                  //                                       setState2,
                                  //                                   context:
                                  //                                       context,
                                  //                                   userdocId:
                                  //                                       usermodelw?.docId ??
                                  //                                           '',
                                  //                                   isExpense:
                                  //                                       isExpense,
                                  //                                 )
                                  //                       // (_pinVerified &&
                                  //                       //         selectedType !=
                                  //                       //             null &&
                                  //                       //         !isLoading)
                                  //                       //     ? () async => handlePayoutConfirmation(
                                  //                       //         setState2:
                                  //                       //             setState2,
                                  //                       //         context:
                                  //                       //             context,
                                  //                       //         selectedType:
                                  //                       //             selectedType,
                                  //                       //         pinVerified:
                                  //                       //             _pinVerified,
                                  //                       //         usermodelw:
                                  //                       //             usermodelw,
                                  //                       //         userdocId:
                                  //                       //             usermodelw
                                  //                       //                     ?.docId ??
                                  //                       //                 "")
                                  //                       //     : null
                                  //                       ,
                                  //                       child: isLoading
                                  //                           ? CircularProgressIndicator(
                                  //                               strokeWidth: 2)
                                  //                           : Text("Confirm"),
                                  //                     ),
                                  //                     TextButton(
                                  //                       onPressed: isLoading
                                  //                           ? null
                                  //                           : () async =>
                                  //                               context.pop(),
                                  //                       child: Text("Cancel"),
                                  //                     ),
                                  //                   ],
                                  //                 ),
                                  //               );
                                  //             },
                                  //           ),
                                  //         ),
                                  //     buttonName: 'Add Transactions'),
                                  SizedBox(width: 10),
                                  CustomHeaderButton(
                                    onPressed: () => showDialog(
                                      barrierDismissible: false,
                                      context: context,
                                      builder: (context) => StatefulBuilder(
                                        builder: (context, setState2) {
                                          final amountToBePaid = selectedType ==
                                                  'share'
                                              ? usermodelw?.totalShares ?? 0
                                              : selectedType == 'subs'
                                                  ? usermodelw?.totalSubs ?? 0
                                                  : 0;

                                          return WillPopScope(
                                            onWillPop: () async => !isLoading,
                                            child: AlertDialog(
                                              title: Text(
                                                  "Select Type for Payout"),
                                              content: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  DropdownButtonFormField<
                                                      String>(
                                                    decoration: InputDecoration(
                                                      labelText: "Select Type",
                                                      border:
                                                          OutlineInputBorder(),
                                                    ),
                                                    value: selectedType,
                                                    items: const [
                                                      DropdownMenuItem(
                                                        value: 'subs',
                                                        child: Text(
                                                            'Subscription Payout'),
                                                      ),
                                                      DropdownMenuItem(
                                                        value: 'share',
                                                        child: Text(
                                                            'Share Payout'),
                                                      ),
                                                    ],
                                                    onChanged: (value) {
                                                      setState2(() {
                                                        selectedType = value;
                                                      });
                                                    },
                                                  ),
                                                  SizedBox(height: 20),
                                                  if (selectedType != null)
                                                    Text(
                                                      "Amount to be Paid : ₹$amountToBePaid",
                                                      style: TextStyle(
                                                        fontSize: 16,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  SizedBox(height: 20),
                                                  if (selectedType != null &&
                                                      !_pinVerified)
                                                    TextFormField(
                                                      decoration:
                                                          const InputDecoration(
                                                        filled: true,
                                                        border:
                                                            OutlineInputBorder(),
                                                        labelText: 'Enter PIN',
                                                      ),
                                                      controller: userPinCtrl,
                                                      obscureText: true,
                                                      onChanged: (text) =>
                                                          setState(() =>
                                                              _enteredPin =
                                                                  text),
                                                      onFieldSubmitted: (_) =>
                                                          _verifyPin(setState2),
                                                    ),
                                                ],
                                              ),
                                              backgroundColor: Colors.white,
                                              actions: [
                                                TextButton(
                                                  onPressed: isLoading
                                                      ? null
                                                      : () async =>
                                                          context.pop(),
                                                  child: Text("Cancel"),
                                                ),
                                                TextButton(
                                                  onPressed: (_pinVerified &&
                                                          selectedType !=
                                                              null &&
                                                          !isLoading)
                                                      ? () async =>
                                                          handlePayoutConfirmation(
                                                              setState2:
                                                                  setState2,
                                                              context: context,
                                                              selectedType:
                                                                  selectedType,
                                                              pinVerified:
                                                                  _pinVerified,
                                                              usermodelw:
                                                                  usermodelw,
                                                              userdocId: usermodelw
                                                                      ?.docId ??
                                                                  "")
                                                      : null,
                                                  child: isLoading
                                                      ? CircularProgressIndicator(
                                                          strokeWidth: 2)
                                                      : Text("Confirm"),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                    buttonName: "Subs/Share Payout",
                                  ),
                                ],
                              ))
                      ]),
                  SizedBox(
                    height: h * 0.7,
                    child: TabBarView(
                      physics: const NeverScrollableScrollPhysics(),
                      controller: tabCtrl,
                      children: [
                        detailsTab(),
                        transactionsTab(transactions, ctrl),
                        loanTab(filtered, ctrl, context),
                      ],
                    ),
                  ),
                ],
              );
      }),
    );
  }

  Future<void> handlePayoutConfirmation({
    required BuildContext context,
    required String? selectedType,
    required bool pinVerified,
    required UserModel? usermodelw,
    required String userdocId,
    required void Function(void Function()) setState2,
  }) async {
    if (selectedType == null) {
      showCtcAppSnackBar(context, "Please select a type");
      return;
    }

    if (!pinVerified) {
      showCtcAppSnackBar(context, "Please enter correct PIN");
      return;
    }

    setState2(() => isLoading = true);

    final batch = FBFireStore.fb.batch();

    try {
      // Fetch userMonthly
      final userMonthlyDoc = await FBFireStore.usermonthly
          .where('cpfNo', isEqualTo: usermodelw?.cpfNo)
          .where('selectedmonth', isEqualTo: DateTime.now().month)
          .where('selectedyear',
              isEqualTo: TheFinancialYear.getCurrentYearForDatabase())
          .get();

      final recoveryMonthlyDoc = await FBFireStore.recoverymonthly
          .where('doId', isEqualTo: usermodelw?.districtoffice)
          .where('selectedmonth', isEqualTo: DateTime.now().month)
          .where('selectedyear',
              isEqualTo: TheFinancialYear.getCurrentYearForDatabase())
          .get();

      final societyYearlyDoc = await FBFireStore.societyYearly
          .where('selectedyear',
              isEqualTo: TheFinancialYear.getCurrentYearForDatabase())
          .get();

      DocumentReference userMonthlyDocRef;

      if (selectedType == 'share') {
        if (recoveryMonthlyDoc.docs.isEmpty) {
          batch.set(FBFireStore.recoverymonthly.doc(), {
            'obLt': null,
            'obSt': null,
            'loanPaidLt': null,
            'loanPaidst': null,
            'loanTotal': null,
            'subs': null,
            'ltInstallment': null,
            'stInstallment': null,
            'interest': null,
            'total': null,
            'installmentRec': null,
            'installmentRecDate': null,
            'ltCb': null,
            'stCb': null,
            'subscriptionPaid': null,
            'longTermInstalmentPaid': null,
            'shortTermInstalmentPaid': null,
            'longTermInterestPaid': null,
            'shortTermInterestPaid': null,
            'totalReceived': null,
            'selectedyear': TheFinancialYear.getCurrentYearForDatabase(),
            'selectedmonth': DateTime.now().month,
            'doId': usermodelw?.districtoffice,
          });
        }

        if (userMonthlyDoc.docs.isNotEmpty) {
          userMonthlyDocRef =
              FBFireStore.usermonthly.doc(userMonthlyDoc.docs.first.id);
          batch.update(userMonthlyDocRef, {
            'societySharesPayout': usermodelw?.totalShares,
          });
        } else {
          userMonthlyDocRef = FBFireStore.usermonthly.doc();
          batch.set(userMonthlyDocRef, {
            'selectedyear': TheFinancialYear.getCurrentYearForDatabase(),
            'selectedmonth': DateTime.now().month,
            'cpfNo': usermodelw?.cpfNo,
            'name': usermodelw?.name,
            'districtoffice': usermodelw?.districtoffice,
            'obLt': null,
            'obSt': null,
            'obSubs': null,
            'loanPaidLt': null,
            'loanPaidst': null,
            'loanTotal': null,
            'subs': null,
            'ltInstallment': null,
            'stInstallment': null,
            'interest': null,
            'total': null,
            'installmentRec': null,
            'installmentRecDate': null,
            'ltCb': null,
            'stCb': null,
            'subscriptionPaid': 0,
            'longTermInstalmentPaid': 0,
            'shortTermInstalmentPaid': 0,
            'longTermInterestPaid': 0,
            'shortTermInterestPaid': 0,
            'isPaid': false,
            'status': null,
            'dues': null,
            'penalty': null,
            'penaltyPaid': 0,
            'shareValue': usermodelw?.totalShares,
            'societySubsPayout': null,
            'societySharesPayout': usermodelw?.totalShares,
          });
        }

        batch.update(
          FBFireStore.societyYearly.doc(societyYearlyDoc.docs.first.id),
          {
            'totalShareGiven':
                FieldValue.increment(usermodelw?.totalShares ?? 0),
          },
        );

        batch.set(FBFireStore.transactions.doc(), {
          'title': 'Share Payout',
          'amount': usermodelw?.totalShares,
          'uId': usermodelw?.docId,
          'createdAt': DateTime.now(),
          'inn': true,
          'userMonthlyId': userMonthlyDocRef.id,
          'recoveryId': null,
        });

        batch.set(FBFireStore.notifications.doc(), {
          'title': 'Share Payout',
          'desc': '₹${usermodelw?.totalShares}',
          'uId': usermodelw?.docId,
          'type': 'Share Payout',
          'districtOffice': usermodelw?.districtoffice,
          'createdAt': DateTime.now(),
        });

        batch.update(FBFireStore.users.doc(userdocId), {'totalShares': 0});
      } else {
        if (recoveryMonthlyDoc.docs.isEmpty) {
          batch.set(FBFireStore.recoverymonthly.doc(), {
            'obLt': null,
            'obSt': null,
            'loanPaidLt': null,
            'loanPaidst': null,
            'loanTotal': null,
            'subs': null,
            'ltInstallment': null,
            'stInstallment': null,
            'interest': null,
            'total': null,
            'installmentRec': null,
            'installmentRecDate': null,
            'ltCb': null,
            'stCb': null,
            'subscriptionPaid': null,
            'longTermInstalmentPaid': null,
            'shortTermInstalmentPaid': null,
            'longTermInterestPaid': null,
            'shortTermInterestPaid': null,
            'totalReceived': null,
            'selectedyear': TheFinancialYear.getCurrentYearForDatabase(),
            'selectedmonth': DateTime.now().month,
            'doId': usermodelw?.districtoffice,
          });
        }

        if (userMonthlyDoc.docs.isNotEmpty) {
          userMonthlyDocRef =
              FBFireStore.usermonthly.doc(userMonthlyDoc.docs.first.id);
          batch.update(userMonthlyDocRef, {
            'societySubsPayout': usermodelw?.totalSubs,
          });
        } else {
          userMonthlyDocRef = FBFireStore.usermonthly.doc();
          batch.set(userMonthlyDocRef, {
            'selectedyear': TheFinancialYear.getCurrentYearForDatabase(),
            'selectedmonth': DateTime.now().month,
            'cpfNo': usermodelw?.cpfNo,
            'name': usermodelw?.name,
            'districtoffice': usermodelw?.districtoffice,
            'obLt': null,
            'obSt': null,
            'obSubs': null,
            'loanPaidLt': null,
            'loanPaidst': null,
            'loanTotal': null,
            'subs': null,
            'ltInstallment': null,
            'stInstallment': null,
            'interest': null,
            'total': null,
            'installmentRec': null,
            'installmentRecDate': null,
            'ltCb': null,
            'stCb': null,
            'subscriptionPaid': 0,
            'longTermInstalmentPaid': 0,
            'shortTermInstalmentPaid': 0,
            'longTermInterestPaid': 0,
            'shortTermInterestPaid': 0,
            'isPaid': false,
            'status': null,
            'dues': null,
            'penalty': null,
            'penaltyPaid': 0,
            'shareValue': null,
            'societySubsPayout': usermodelw?.totalSubs,
            'societySharesPayout': null,
          });
        }

        batch.set(FBFireStore.transactions.doc(), {
          'title': 'Subscription Payout',
          'amount': usermodelw?.totalSubs,
          'uId': usermodelw?.docId,
          'createdAt': DateTime.now(),
          'inn': true,
          'userMonthlyId': userMonthlyDocRef.id,
          'recoveryId': null,
        });

        batch.set(FBFireStore.notifications.doc(), {
          'title': 'Subscription Payout',
          'desc': '₹${usermodelw?.totalSubs}',
          'uId': usermodelw?.docId,
          'type': 'Subscription Payout',
          'districtOffice': usermodelw?.districtoffice,
          'createdAt': DateTime.now(),
        });

        batch.update(FBFireStore.users.doc(userdocId), {'totalSubs': 0});
      }

      await batch.commit();
      setState2(() => isLoading = false);

      context.pop();
    } catch (e) {
      debugPrint("Payout Error: ${e.toString()}");

      setState2(() => isLoading = false);
      showCtcAppSnackBar(context, "Error in processing payout");
    }
  }

  void _verifyPin(void Function(void Function()) setState2) {
    final String correctPin =
        Get.find<HomeCtrl>().settings?.setPin.toString() ?? "";

    if (_enteredPin.trim().isEmpty) {
      showCtcAppSnackBar(context, 'Please Enter PIN');
      return;
    }

    if (_enteredPin == correctPin) {
      setState2(() {
        _pinVerified = true;
      });
      showCtcAppSnackBar(context, 'PIN Verified');
    } else {
      setState2(() {
        _pinVerified = false;
      });
      showCtcAppSnackBar(context, 'Incorrect PIN');
      userPinCtrl.clear();
    }
  }

  SingleChildScrollView loanTab(
      List<Loan> filtered, HomeCtrl ctrl, BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 25, left: 25, right: 25),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // const SizedBox(height: 20),
            CupertinoSlidingSegmentedControl<SelectedLoan>(
              thumbColor: Colors.green.shade300,
              groupValue: selectedLoan,
              children: {
                SelectedLoan.longtermloan: Text(
                  "Long Term Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.longtermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                SelectedLoan.shorttermloan: Text(
                  "Emergency Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.shorttermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              },
              onValueChanged: (SelectedLoan? value) {
                if (value != null) {
                  setState(() {
                    selectedLoan = value;
                  });
                }
              },
            ),
            const SizedBox(height: 30),

            Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Approved On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Appl No.'),
                HeaderTxt(txt: 'Loan Amount'),
              ],
            ),
            const SizedBox(height: 30),
            ...List.generate(
              filtered.length,
              (index) {
                final loan = filtered[index];
                final user = ctrl.users
                    .firstWhereOrNull((element) => element.docId == loan.uid);
                final doOffice = ctrl.districtoffice.firstWhereOrNull(
                    (element) => element.docId == user?.districtoffice);

                return InkWell(
                  onTap: () => context.push(
                    '${Routes.loanDetails}/${loan.docId}',
                  ),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0
                        ? const Color.fromARGB(255, 243, 241, 241)
                        : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(
                              DateFormat('dd-MM-yyyy').format(loan.approvedOn!),
                            ),
                          ),
                          Expanded(child: Text(user?.name ?? "")),
                          Expanded(child: Text(doOffice?.name ?? "-")),
                          Expanded(child: Text(loan.applicationNo.toString())),
                          Expanded(child: Text(loan.appliedLoanAmt.toString())),
                          // Expanded(child: Text(loan.loanType)),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),

            // StaggeredGrid.extent(
            //   maxCrossAxisExtent: 400,
            //   mainAxisSpacing: 3,
            //   crossAxisSpacing: 10,
            //   children: [
            //     UserCustomTextField(
            //         initialvalue:
            //             usermodelw?.ltLoansDue.toString() ??
            //                 "",
            //         enabled: false,
            //         labeltext: 'Long Term Loans Due'),
            //     UserCustomTextField(
            //         initialvalue:
            //             usermodelw?.stLoansDue.toString() ??
            //                 "",
            //         enabled: false,
            //         labeltext: 'Short Term Loans Due'),
            //     UserCustomTextField(
            //         initialvalue: usermodelw?.totalLtLoans
            //                 .toString() ??
            //             "",
            //         enabled: false,
            //         labeltext: 'Total Long term Loans'),
            //     UserCustomTextField(
            //         initialvalue: usermodelw?.totalStLoans
            //                 .toString() ??
            //             "",
            //         enabled: false,
            //         labeltext: 'Total Short term Loans'),
            //     UserCustomTextField(
            //         initialvalue: usermodelw?.totalLtIntPaid
            //                 .toString() ??
            //             "",
            //         enabled: false,
            //         labeltext:
            //             'Total Long term Interest Paid'),
            //     UserCustomTextField(
            //         initialvalue: usermodelw?.totalStIntPaid
            //                 .toString() ??
            //             "",
            //         enabled: false,
            //         labeltext:
            //             'Total Short term Interest Paid'),
            //   ],
            // ),
            // SizedBox(height: 15),
            // ...List.generate(
            //   currentUserLoan.length,
            //   (index) => InkWell(
            //     splashColor: Colors.transparent,
            //     hoverColor: Colors.transparent,
            //     focusColor: Colors.transparent,
            //     onTap: () =>
            //         context.push(Routes.userloandetails),
            //     child: StaggeredGrid.extent(
            //         maxCrossAxisExtent: 250,
            //         children: [
            //           UserCustomCard(
            //               extraTexxt:
            //                   DateFormat("dd/MM/yyyy")
            //                       .format(ctrl.loan.first
            //                           .createdAt),
            //               dataTexxt:
            //                   "APPL. NO. ${ctrl.loan.first.applicationNo}"
            //                       .toString()),
            //         ]),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  SingleChildScrollView transactionsTab(
      List<TransactionModel> transactions, HomeCtrl ctrl) {
    return SingleChildScrollView(
      child: Padding(
          padding: const EdgeInsets.all(25),
          child: Column(
            children: [
              ...List.generate(
                transactions.length,
                (index) => Column(
                  children: [
                    ListTile(
                        contentPadding:
                            EdgeInsets.symmetric(vertical: 10, horizontal: 3),
                        trailing: Text(
                          "₹${transactions[index].amount.toString()}",
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.w500),
                        ),
                        subtitle: Text(
                          DateFormat("dd-MM-yyyy - hh:mm a")
                              .format(ctrl.transactions[index].createdAt),
                          style: const TextStyle(
                              fontSize: 12, fontWeight: FontWeight.w400),
                        ),
                        title: Text(
                          transactions[index].title,
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                        leading: Icon(
                          transactions[index].inn
                              ? CupertinoIcons.arrow_down_right
                              : CupertinoIcons.arrow_up_right,
                          color: transactions[index].inn
                              ? const Color(0xFFA4E672)
                              : const Color(0xFFE67A72),
                        )),
                    Divider(
                      color: Colors.grey.shade300,
                      thickness: 1,
                    ),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  GetBuilder<HomeCtrl> detailsTab() {
    return GetBuilder(builder: (ctrl) {
      final singleUserDo = ctrl.districtoffice.firstWhere(
        (element) => element.docId == usermodelw?.districtoffice,
        orElse: () => DistrictOfficeModel(
          docId: 'unknown',
          name: 'N/A', email: '',
          location: '', // or "Unknown"
        ),
      );
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(25),
          child: StaggeredGrid.extent(
            maxCrossAxisExtent: 377,
            mainAxisSpacing: 3,
            crossAxisSpacing: 10,
            children: [
              UserCustomTextField(
                  initialvalue: usermodelw?.name.toUpperCase() ?? "",
                  enabled: false,
                  labeltext: 'Name'),
              UserCustomTextField(
                  initialvalue: usermodelw?.cpfNo.toString() ?? "",
                  enabled: false,
                  labeltext: 'Cpf No.'),
              UserCustomTextField(
                  initialvalue: usermodelw?.employeeNo.toString() ?? "",
                  enabled: false,
                  labeltext: 'Employee No.'),
              UserCustomTextField(
                  initialvalue: singleUserDo.name,
                  enabled: false,
                  labeltext: 'DO'),
              UserCustomTextField(
                  initialvalue: usermodelw?.email ?? "",
                  enabled: false,
                  labeltext: 'Email'),
              UserCustomTextField(
                  initialvalue: usermodelw?.phoneNo ?? "",
                  enabled: false,
                  labeltext: 'Phone No.'),
              UserCustomTextField(
                  initialvalue: usermodelw?.permanentAddress.toString() ?? "",
                  enabled: false,
                  labeltext: 'Current Address'),
              UserCustomTextField(
                  initialvalue: usermodelw?.bankAcName.toString() ?? "",
                  enabled: false,
                  labeltext: 'Bank Account Name'),
              UserCustomTextField(
                  initialvalue: usermodelw?.bankName.toString() ?? "",
                  enabled: false,
                  labeltext: 'Name of Bank'),
              UserCustomTextField(
                  initialvalue: usermodelw?.ifscCode.toString() ?? "",
                  enabled: false,
                  labeltext: 'IFSC Code'),
              UserCustomTextField(
                  initialvalue: usermodelw?.bankAcNo.toString() ?? "",
                  enabled: false,
                  labeltext: 'Bank Account No.'),
              UserCustomTextField(
                  initialvalue: usermodelw?.totalShares.toString() ?? "",
                  enabled: false,
                  labeltext: 'Total Shares'),
              UserCustomTextField(
                  initialvalue: usermodelw?.totalSubs.toString() ?? "-",
                  enabled: false,
                  labeltext: 'Total Subscription'),
              UserCustomTextField(
                  initialvalue: usermodelw?.totalDivident.toString() ?? "-",
                  enabled: false,
                  labeltext: 'Total Divident'),
              UserCustomTextField(
                  initialvalue: usermodelw?.nomineeName ?? '',
                  enabled: false,
                  labeltext: 'Nominee Name'),
              UserCustomTextField(
                  initialvalue: usermodelw?.nomineeRelation ?? "",
                  enabled: false,
                  labeltext: 'Nominee Relation'),
            ],
          ),
        ),
      );
    });
  }
}
