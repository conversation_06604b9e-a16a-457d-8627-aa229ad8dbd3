import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../controller/homectrl.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';

class ArchivedUserPage extends StatefulWidget {
  const ArchivedUserPage({super.key});

  @override
  State<ArchivedUserPage> createState() => _ArchivedUserPageState();
}

String? selectedoffice;
bool loadingarchived = false;

class _ArchivedUserPageState extends State<ArchivedUserPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final filteredList =
            ctrl.users.where((element) => element.archived).toList();

        if ((selectedoffice != null) && (selectedoffice != "all")) {
          filteredList
              .where(
                (element) => element.districtoffice == selectedoffice,
              )
              .toList();
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(40),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
                  decoration: InputDecoration(
                    hintText: "Select DO",
                    constraints: const BoxConstraints(maxWidth: 450),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  value: selectedoffice,
                  items: [
                    const DropdownMenuItem(value: 'all', child: Text("All")),
                    ...List.generate(
                      ctrl.districtoffice.length,
                      (index) {
                        return DropdownMenuItem(
                          value: ctrl.districtoffice[index].docId,
                          child: Text(ctrl.districtoffice[index].name),
                        );
                      },
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedoffice = value;
                    });
                  },
                ),
              ),
              const SizedBox(height: 40),
              const Row(
                children: [
                  HeaderTxt(txt: "Sr.No."),
                  HeaderTxt(txt: "CPF No."),
                  HeaderTxt(txt: "Employee No."),
                  HeaderTxt(txt: "Name"),
                  HeaderTxt(txt: "Email"),
                  HeaderTxt(txt: "DO"),
                  // HeaderTxt(txt: ""),
                  // HeaderTxt(txt: ""),
                ],
              ),
              const SizedBox(height: 20),
              ...List.generate(
                filteredList.length,
                (index) {
                  final singleUserDo = ctrl.districtoffice.firstWhere(
                    (element) =>
                        element.docId ==
                        filteredList[index].districtoffice.toString(),
                  );

                  return InkWell(
                    onTap: () {
                      context.go(
                        '${Routes.adduserform}/${filteredList[index].docId}',
                        extra: filteredList[index],
                      );
                    },
                    child: Container(
                      height: 40,
                      color: index % 2 == 0 ? Colors.white : null,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: Row(
                          children: [
                            Expanded(child: Text("${index + 1}")),
                            Expanded(
                              child: Text(filteredList[index].cpfNo.toString()),
                            ),
                            Expanded(
                              child: Text(
                                  filteredList[index].employeeNo.toString()),
                            ),
                            Expanded(child: Text(filteredList[index].name)),
                            Expanded(
                              child: Text(
                                filteredList[index].email ?? "",
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(width: 25),
                            Expanded(child: Text(singleUserDo.name)),
                            // const Expanded(child: Text("")),
                            // const Expanded(child: Text("")),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              )
            ],
          ),
        );
      },
    );
  }
}
