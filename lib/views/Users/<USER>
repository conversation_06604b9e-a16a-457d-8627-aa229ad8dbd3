// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/districtoffice_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import '../../models/user_model.dart';
import '../../shared/firebase.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

SearchController sctrl = SearchController();

class _UsersPageState extends State<UsersPage> {
  String? selectedoffice;
  List<DistrictOfficeModel> doffice = [];

  @override
  void initState() {
    super.initState();
    selectedoffice = 'all';
  }

  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<UserModel> filteredUsers = ctrl.users
          .where(
        (element) => !element.archived,
      )
          .where(
        (element) {
          bool search = element.name.contains(sctrl.text) ||
              element.email!.contains(sctrl.text) ||
              element.cpfNo.toString().contains(sctrl.text) ||
              element.employeeNo.toString().contains(sctrl.text);

          bool office = selectedoffice == 'all' ||
              element.districtoffice == selectedoffice;

          return search && office;
        },
      ).toList()
        ..sort((a, b) => a.name.compareTo(b.name));

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomSearchBarWidget(
                      searchController: sctrl,
                      searchOnChanged: (p0) {
                        setState(() {});
                      },
                    ),
                    SizedBox(width: 10),
                    // DropdownButtonHideUnderline(
                    //     child: DropdownButtonFormField(
                    //   decoration: InputDecoration(
                    //       constraints: const BoxConstraints(maxWidth: 450),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(10))),
                    //   value: selectedoffice,
                    //   items: [
                    //     const DropdownMenuItem(
                    //         value: 'all', child: Text("All")),
                    //     ...List.generate(
                    //       ctrl.districtoffice.length,
                    //       (index) {
                    //         return DropdownMenuItem(
                    //             value: ctrl.districtoffice[index].docId,
                    //             child: Text(ctrl.districtoffice[index].name));
                    //       },
                    //     ),
                    //   ],
                    //   onChanged: (value) {
                    //     setState(() {
                    //       selectedoffice = value;
                    //     });
                    //   },
                    // )),
                    if (selectedoffice != 'all') SizedBox(width: 5),
                    if (selectedoffice != 'all')
                      IconButton(
                          style: OutlinedButton.styleFrom(),
                          onPressed: () {
                            setState(() {
                              selectedoffice = 'all';
                            });
                          },
                          icon: Icon(
                            Icons.clear,
                            size: 30,
                          )),
                  ],
                ),
                Row(
                  children: [
                    CustomHeaderButton(
                        onPressed: () {
                          context.go(Routes.archivedUser);
                        },
                        buttonName: "ARCHIVED USER"),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                        onPressed: () =>
                            context.go('${Routes.adduserform}/${null}'),
                        buttonName: "ADD USER"),
                  ],
                )
              ],
            ),
            const SizedBox(height: 40),
            const Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: 'CPF No.'),
                HeaderTxt(txt: 'Employee No.'),
                HeaderTxt(txt: 'Name'),
                // HeaderTxt(txt: 'Email'),
                HeaderTxt(txt: 'DO'),
                Spacer()
              ],
            ),
            const SizedBox(height: 10),
            ctrl.users.isEmpty
                ? const Center(
                    heightFactor: 9,
                    child: Text(
                      softWrap: true,
                      "No Users Available",
                      style: TextStyle(fontSize: 20),
                    ),
                  )
                : Column(
                    children: [
                      ...List.generate(
                        filteredUsers.length,
                        (index) {
                          final singleUserDo = ctrl.districtoffice.firstWhere(
                            (element) =>
                                element.docId ==
                                filteredUsers[index].districtoffice,
                            orElse: () => DistrictOfficeModel(
                              docId: 'unknown',
                              name: 'N/A', email: '',
                              location: '', // or "Unknown"
                            ),
                          );
                          return InkWell(
                            onTap: () => context.push(
                                '${Routes.userDetails}/${filteredUsers[index].docId}',
                                extra: filteredUsers[index]),
                            child: Container(
                              height: 40,
                              color: index % 2 == 0 ? Colors.white : null,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10.0),
                                child: Row(
                                  children: [
                                    Expanded(child: Text("${index + 1}")),
                                    Expanded(
                                        child: Text(filteredUsers[index]
                                            .cpfNo
                                            .toString())),
                                    Expanded(
                                        child: Text(filteredUsers[index]
                                            .employeeNo
                                            .toString())),
                                    Expanded(
                                        child: Text(filteredUsers[index]
                                            .name
                                            .toUpperCase())),
                                    // Expanded(
                                    //     child: Text(
                                    //         overflow: TextOverflow.ellipsis,
                                    //         filteredUsers[index].email)),
                                    Expanded(child: Text(singleUserDo.name)),
                                    Expanded(
                                        child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        filteredUsers[index].isActive == true
                                            ? Icon(
                                                Icons.check_box_outlined,
                                                color: Colors.green.shade400,
                                              )
                                            : Icon(
                                                Icons
                                                    .cancel_presentation_outlined,
                                                color: Colors.red,
                                              ),
                                        SizedBox(width: 10),
                                        IconButton(
                                            onPressed: () => context.go(
                                                '${Routes.adduserform}/${filteredUsers[index].docId}',
                                                extra: filteredUsers[index]),
                                            icon: const Icon(
                                              Icons.edit,
                                              color: Color.fromARGB(
                                                  255, 90, 203, 148),
                                            )),
                                        deleteButton(
                                            filteredUsers, index, context)
                                      ],
                                    )),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      )
                    ],
                  )
          ],
        ),
      );
    });
  }

  IconButton deleteButton(
      List<UserModel> filteredUsers, int index, BuildContext context) {
    return IconButton(
      onPressed: () async {
        final user = filteredUsers[index];
        final uid = user.docId;

// .where((element) => element.rejectionDate == null)
//           .where((element) => element.isSettled == false)
//           .where((element) => element.isNew == false)

        final loanSnapshot = await FBFireStore.loan
            .where("uid", isEqualTo: uid)
            .where("rejectionDate", isEqualTo: null)
            .where("isSettled", isEqualTo: false)
            .where("isNew", isEqualTo: false)
            .get();

        // if (loanSnapshot.docs.isEmpty) {
        //   return showAppSnackBar("No loans found for this user");
        // }

        bool loading = false;

        return showDialog(
          context: context,
          builder: (context) => StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                  backgroundColor: Colors.white,
                  title: const Text("Archive User?"),
                  content: const Text("Are you sure you want to archive user?"),
                  actions: [
                    loading
                        ? const CircularProgressIndicator()
                        : TextButton(
                            onPressed: () async {
                              try {
                                setState2(() => loading = true);

                                if (loanSnapshot.docs.isNotEmpty) {
                                  showCtcAppSnackBar(context,
                                      "Loan of the particular user exists!");
                                  return;
                                }

                                // for (final doc in loanSnapshot.docs) {
                                //   // await doc.reference.update({
                                //   //   'isSettled': true,
                                //   //   'settledOn':
                                //   //       Timestamp.fromDate(DateTime.now()),
                                //   //   'totalLoanDue': 0,
                                //   // });
                                // }

                                await FBFireStore.users
                                    .doc(uid)
                                    .update({'archived': true});

                                if (context.mounted) context.pop();
                              } catch (e) {
                                debugPrint("Error: $e");
                              } finally {
                                setState2(() => loading = false);
                              }
                            },
                            child: const Text("Yes"),
                          ),
                    TextButton(
                      onPressed: () {
                        if (context.mounted) context.pop();
                      },
                      child: const Text("No"),
                    ),
                  ]);
            },
          ),
        );
      },
      icon: const Icon(Icons.delete),
    );
  }
}
