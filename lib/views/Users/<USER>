import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class UserLoanDetailsPage extends StatefulWidget {
  const UserLoanDetailsPage({super.key, this.loanId});
  final String? loanId;

  @override
  State<UserLoanDetailsPage> createState() => _UserLoanDetailsPageState();
}

class _UserLoanDetailsPageState extends State<UserLoanDetailsPage> {
  Loan? loanModelw;
  UserModel? userModelw;

  bool userloanfullLoader = true;

  final ctrl = Get.find<HomeCtrl>();

  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    if (widget.loanId != null) {
      final loanData = await FBFireStore.loan.doc(widget.loanId).get();
      loanModelw = Loan.fromSnap(loanData);
      if (loanModelw != null) {
        final userData = await FBFireStore.users.doc(loanModelw?.uid).get();
        userModelw = UserModel.fromSnap(userData);
      }
    }
    setState(() => userloanfullLoader = false);
  }

  loadData() async {
    loanModelw?.applicationNo.toString() ?? 0;
    loanModelw?.uid;
    loanModelw?.createdAt;
    loanModelw?.totalLoanAmt.toString() ?? 0;
    loanModelw?.appliedLoanAmt.toString() ?? 0;
    loanModelw?.totalLoanPaid.toString() ?? 0;
    loanModelw?.totalLoanDue.toString() ?? 0;
    loanModelw?.loanType;
    loanModelw?.isSettled;
    loanModelw?.settledOn.toString();
    loanModelw?.isNew;
    loanModelw?.appliedOn.toString();
    // loanModelw?.applicationNo.toString();
    loanModelw?.approvedOn.toString();
    loanModelw?.processedOn.toString();
    loanModelw?.share.toString() ?? 0;
    loanModelw?.totalInterestPaid.toString() ?? 0;
    loanModelw?.designation;
    loanModelw?.bAcNo;
    loanModelw?.appliedLoanAmtinWords;
    loanModelw?.loanReason;
    loanModelw?.bSign;
    loanModelw?.surityName1;
    loanModelw?.surityName2;
    loanModelw?.sAcNo1.toString();
    loanModelw?.sAcNo2.toString();
    // loanModelw?.balance.toString();
    loanModelw?.rejectionDate.toString() ?? "-";
    loanModelw?.rejectionReason;
    loanModelw?.monthlyInstallmentAmt;
    loanModelw?.loanPreClosureReason;

    // setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final doOffice = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == userModelw?.districtoffice);
    return Scaffold(
      appBar: AppBar(),
      body: userloanfullLoader
          ? Center(
              child: SizedBox(
                  height: 30, width: 30, child: CircularProgressIndicator()))
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(
                  top: 25,
                  left: 25,
                  right: 25,
                ),
                child: StaggeredGrid.extent(
                  maxCrossAxisExtent: 377,
                  mainAxisSpacing: 3,
                  crossAxisSpacing: 10,
                  children: [
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.applicationNo.toString() ?? "",
                        enabled: false,
                        labeltext: 'Application No.'),
                    UserCustomTextField(
                        initialvalue: DateFormat('dd - MM - yyyy')
                            .format(loanModelw!.appliedOn),
                        enabled: false,
                        labeltext: 'Applied On.'),
                    UserCustomTextField(
                        initialvalue: DateFormat('dd - MM - yyyy')
                            .format(loanModelw!.approvedOn!),
                        enabled: false,
                        labeltext: 'Approved On.'),
                    UserCustomTextField(
                        initialvalue: userModelw?.name ?? "",
                        enabled: false,
                        labeltext: 'Name'),
                    UserCustomTextField(
                        initialvalue: userModelw?.cpfNo.toString() ?? "",
                        enabled: false,
                        labeltext: 'CPF No.'),
                    UserCustomTextField(
                        initialvalue: doOffice?.name ?? "",
                        enabled: false,
                        labeltext: 'District Office'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.designation ?? "",
                        enabled: false,
                        labeltext: 'Designation'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.loanReason ?? "",
                        enabled: false,
                        labeltext: 'Loan Reason'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.loanType ?? "",
                        enabled: false,
                        labeltext: 'Loan Type'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.surityName1 ?? "",
                        enabled: false,
                        labeltext: 'Surity Name of the First Person'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.surityName2 ?? "",
                        enabled: false,
                        labeltext: 'Surity Name of the Second Person'),
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.appliedLoanAmt.toString() ?? "",
                        enabled: false,
                        labeltext: 'Applied Loan Amount'),
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.appliedLoanAmtinWords.toString() ?? "",
                        enabled: false,
                        labeltext: 'Applied Loan Amount in words'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.approvedOn.toString() ?? "",
                        enabled: false,
                        labeltext: 'Approved On'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.bAcNo.toString() ?? "",
                        enabled: false,
                        labeltext: 'Bank Account No.'),
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.monthlyInstallmentAmt.toString() ?? "",
                        enabled: false,
                        labeltext: 'Monthly Installment Amount'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.share.toString() ?? "",
                        enabled: false,
                        labeltext: 'Shares'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.totalLoanAmt.toString() ?? "",
                        enabled: false,
                        labeltext: 'Total Loan Amount'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.totalLoanDue.toString() ?? "",
                        enabled: false,
                        labeltext: 'Total Loan Due'),
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.totalLoanPaid.toString() ?? "",
                        enabled: false,
                        labeltext: 'Total Loan Paid'),
                    UserCustomTextField(
                        initialvalue:
                            loanModelw?.totalInterestPaid.toString() ?? "",
                        enabled: false,
                        labeltext: 'Total Interest Paid'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.loanPreClosureReason ?? "-",
                        enabled: false,
                        labeltext: 'Loan Pre - Closure Reason'),
                    UserCustomTextField(
                        initialvalue: loanModelw!.rejectionDate != null
                            ? DateFormat('dd - MM - yyyy')
                                .format(loanModelw!.rejectionDate!)
                            : '-',
                        enabled: false,
                        labeltext: 'Rejection Date'),
                    UserCustomTextField(
                        initialvalue: loanModelw?.rejectionReason ?? "-",
                        enabled: false,
                        labeltext: 'Rejection Reason'),
                  ],
                ),
              ),
            ),
    );
  }
}
