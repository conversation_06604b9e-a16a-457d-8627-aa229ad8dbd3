// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/expenses_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:foodcorp_admin/views/Expenses/forms/add_cash.dart';
import 'package:foodcorp_admin/views/Expenses/forms/add_expense.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

class ExpensesPage extends StatefulWidget {
  const ExpensesPage({super.key});

  @override
  State<ExpensesPage> createState() => _ExpensesPageState();
}

bool loading = false;

final searchctrl = TextEditingController();

int? selectedMonth = DateTime.now().month;

int? selectedYear = DateTime.now().year;

DateTime? selectedDate;

String? selectedMonthYear;

final List<String> month = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec'
];

class _ExpensesPageState extends State<ExpensesPage> {
  @override
  void initState() {
    super.initState();
    selectedMonth = DateTime.now().month;
    selectedYear = DateTime.now().year;
    selectedMonthYear = null;
  }

  @override
  Widget build(BuildContext context) {
    // print(selectedMonth);
    // print(selectedYear);
    // print(selectedMonthYear);
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      bool isSameDate(DateTime a, DateTime b) {
        return a.year == b.year && a.month == b.month && a.day == b.day;
      }

      List<Expense> allFiltered = ctrl.expenses.where((element) {
        final searchText = searchctrl.text.toLowerCase();

        final elementName = element.name.toLowerCase();
        final elementExpenseBy = element.expenseBy.toLowerCase();

        final matchesSearch = elementName.contains(searchText) ||
            elementExpenseBy.contains(searchText);

        bool matchesDate = true;
        if (selectedDate != null) {
          matchesDate = isSameDate(element.expenseDate, selectedDate!);
        }

        bool matchesMonth = true;
        if (selectedMonthYear != null) {
          matchesMonth = (element.expenseDate.month == selectedMonth &&
              element.expenseDate.year == selectedYear);
        }

        return matchesSearch && matchesDate && matchesMonth;
      }).toList()
        ..sort((a, b) => b.expenseDate.compareTo(a.expenseDate));

      final cashDep = ctrl.cashModel
              .where((e) => e.amount != null)
              .fold<num>(0, (sum, e) => sum + e.amount) +
          ctrl.expenses
              .where((e) => e.isIncome == true && e.cash == true)
              .fold<num>(0, (sum, e) => sum + e.amount);

      final cashSpent = ctrl.expenses
          .where((e) => e.cash == true && e.isIncome == false)
          .fold<num>(0, (sum, e) => sum + e.amount);

      return !ctrl.cashdataloaded
          ? Center(
              child: SizedBox(
                  height: 25, width: 25, child: CircularProgressIndicator()))
          : Padding(
              padding: const EdgeInsets.only(top: 40, left: 40, right: 40),
              child: SingleChildScrollView(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomSearchBarWidget(
                            searchOnChanged: (p0) {
                              allFiltered = ctrl.expenses
                                  .where((element) => element.name.contains(p0))
                                  .toList();
                              setState(() {});
                            },
                            searchController: searchctrl,
                          ),
                          Row(children: [
                            ///Month and Year Dropdown
                            DropdownButtonHideUnderline(
                              child: DropdownButtonFormField<String>(
                                focusColor: Colors.transparent,
                                dropdownColor: Colors.white,
                                value: selectedMonthYear,
                                decoration: InputDecoration(
                                  hintText: "Select Month and Year",
                                  constraints:
                                      const BoxConstraints(maxWidth: 220),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                items: () {
                                  int currentYear = DateTime.now().year;
                                  int currentMonth = DateTime.now().month;
                                  int endYear = currentYear + 0;

                                  List<DropdownMenuItem<String>> items = [];

                                  for (int year = currentYear;
                                      year <= endYear;
                                      year++) {
                                    int startMonth = (year == currentYear)
                                        ? currentMonth
                                        : 1;
                                    for (int monthIndex = startMonth - 1;
                                        monthIndex < 12;
                                        monthIndex++) {
                                      items.add(
                                        DropdownMenuItem<String>(
                                          value: '${month[monthIndex]} $year',
                                          child: Text(
                                              '${month[monthIndex]} $year'),
                                        ),
                                      );
                                    }
                                  }

                                  return items;
                                }(),
                                onChanged: (newMonthYear) {
                                  setState(() {
                                    selectedMonthYear = newMonthYear;
                                    if (newMonthYear != null) {
                                      final monthYear = newMonthYear.split(' ');
                                      selectedMonth =
                                          month.indexOf(monthYear[0]) + 1;
                                      selectedYear = int.parse(monthYear[1]);
                                      selectedDate = null;
                                    } else {
                                      selectedMonth = null;
                                      selectedYear = null;
                                    }
                                  });
                                },
                              ),
                            ),
                            SizedBox(width: 10),
                            //Date Picker
                            SizedBox(
                              width: 200,
                              child: InkWell(
                                onTap: () async {
                                  final currentFYStartYear = TheFinancialYear
                                      .getCurrentFinancialYearStartYear();
                                  final fyStart = TheFinancialYear
                                      .getFinancialYearStartDate(
                                          currentFYStartYear - 1);
                                  final fyEnd =
                                      TheFinancialYear.getFinancialYearEndDate(
                                          currentFYStartYear + 1);

                                  selectedDate = await showDatePicker(
                                          context: context,
                                          initialDate:
                                              selectedDate ?? DateTime.now(),
                                          firstDate: fyStart,
                                          lastDate: fyEnd) ??
                                      DateTime.now();

                                  setState(() {
                                    selectedMonthYear = null;
                                  });
                                },
                                child: TextFormField(
                                  style: TextStyle(color: Colors.black),
                                  enabled: false,
                                  controller: TextEditingController(
                                      text: selectedDate
                                              ?.toLocal()
                                              .toString()
                                              .split(' ')[0] ??
                                          'Select Date'),
                                  decoration: InputDecoration(
                                    filled: true,
                                    prefixIcon: const Icon(
                                      Icons.date_range,
                                      size: 20,
                                      color: Colors.black,
                                    ),
                                    border: OutlineInputBorder(
                                        borderSide: BorderSide.none,
                                        borderRadius: BorderRadius.circular(6)),
                                    hintText: 'ExpenseDate',
                                  ),
                                ),
                              ),
                            ),
                            if (selectedDate != null)
                              IconButton(
                                  hoverColor: Colors.transparent,
                                  style: OutlinedButton.styleFrom(),
                                  onPressed: () {
                                    setState(() {
                                      selectedDate = null;
                                    });
                                  },
                                  icon: Icon(
                                    Icons.clear,
                                    size: 20,
                                  )),
                            SizedBox(width: 10),
                            AddExpenseButton(),
                            const SizedBox(width: 8),
                            AddIncomeButton(),
                          ])
                        ]),
                    const SizedBox(height: 25),
                    Container(
                      height: 55,
                      color: Colors.white,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            "Cash Balance : ${cashDep - cashSpent}",
                            style: TextStyle(
                              letterSpacing: 0.5,
                              fontSize: 16.5,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            "Cash Deposited : $cashDep",
                            style: TextStyle(
                              letterSpacing: 0.5,
                              fontSize: 16.5,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            "Cash Spent : $cashSpent",
                            style: TextStyle(
                              letterSpacing: 0.5,
                              fontSize: 16.5,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Row(
                            children: [
                              AddCashButton(),
                              SizedBox(width: 20),
                              CashHistoryButton(),
                            ],
                          ),
                        ],
                      ),
                      // [],
                    ),
                    SizedBox(height: 25),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          HeaderTxt(txt: "Name"),
                          HeaderTxt(txt: "Expense By"),
                          HeaderTxt(txt: "Amount"),
                          HeaderTxt(txt: "CreatedAt"),
                          SizedBox(width: 30),
                          HeaderTxt(txt: "ExpenseDate"),
                          HeaderTxt(txt: ""),
                        ],
                      ),
                    ),
                    const SizedBox(height: 30),
                    ...List.generate(allFiltered.length, (index) {
                      return Column(
                        children: [
                          ListContainer(filtered: allFiltered, index: index),
                          SizedBox(height: 7)
                        ],
                      );
                    })
                  ])));
    });
  }
}

class AddIncomeButton extends StatelessWidget {
  const AddIncomeButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.black87,
        elevation: 0,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),
      onPressed: () async {
        showDialog(
          context: context,
          builder: (context) => const AlertDialog(
            backgroundColor: Colors.white,
            actions: [
              AddExpenseForm(
                isIncome: true,
                expense: null,
              )
            ],
          ),
        );
      },
      label: const Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Text("Add Income", style: TextStyle(fontSize: 14)),
      ),
    );
  }
}

class AddExpenseButton extends StatelessWidget {
  const AddExpenseButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.black87,
        elevation: 0,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),
      onPressed: () async {
        showDialog(
          context: context,
          builder: (context) => const AlertDialog(
            backgroundColor: Colors.white,
            actions: [
              AddExpenseForm(
                isIncome: false,
                expense: null,
              )
            ],
          ),
        );
      },
      label: const Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Text("Add Expense", style: TextStyle(fontSize: 14)),
      ),
    );
  }
}

class CashHistoryButton extends StatelessWidget {
  const CashHistoryButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 18),
          backgroundColor: Colors.black87,
          elevation: 0,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        onPressed: () {
          context.push(Routes.cashHistory);
        },
        label: Text(
          "Cash History",
          style: TextStyle(
            letterSpacing: 0.5,
            fontSize: 14,
          ),
        ));
  }
}

class AddCashButton extends StatelessWidget {
  const AddCashButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 18),
          backgroundColor: Colors.black87,
          elevation: 0,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => const AlertDialog(
              backgroundColor: Colors.white,
              actions: [AddCashForm()],
            ),
          );
        },
        label: Text(
          "Add Cash",
          style: TextStyle(
            letterSpacing: 0.5,
            fontSize: 14,
          ),
        ));
  }
}

class ListContainer extends StatelessWidget {
  const ListContainer({
    super.key,
    required this.filtered,
    required this.index,
  });

  final List<Expense> filtered;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Container(
        // color: index % 2 == 0 ? Colors.white : null,
        height: 50,
        color: filtered[index].isIncome
            ? const Color.fromARGB(255, 222, 240, 223)
            : const Color.fromARGB(255, 242, 229, 229),
        child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Row(children: [
              Expanded(child: Text(filtered[index].name)),
              Expanded(child: Text(filtered[index].expenseBy)),
              Expanded(child: Text(filtered[index].amount.toString())),
              Expanded(
                  child: Text(DateFormat("dd - MM - yyyy : hh:mm")
                      .format(filtered[index].createdAt))),
              SizedBox(width: 30),
              Expanded(
                  child: Text(DateFormat("dd - MM - yyyy : hh:mm")
                      .format(filtered[index].expenseDate))),
              Expanded(
                  child:
                      Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                if (filtered[index].cash == true)
                  Text(
                    softWrap: true,
                    "From Cash",
                    style: TextStyle(fontSize: 10),
                  ),
                SizedBox(width: 2),
                if (filtered[index].attachmentString != null &&
                    filtered[index].attachmentString!.isNotEmpty)
                  IconButton(
                    onPressed: () async {
                      final url = filtered[index].attachmentString!;
                      if (await canLaunchUrl(Uri.parse(url))) {
                        await launchUrl(Uri.parse(url),
                            mode: LaunchMode.externalApplication);
                      } else {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text('Could not open attachment')),
                          );
                        }
                      }
                    },
                    icon: Icon(Icons.insert_drive_file),
                  ),
                IconButton(
                    onPressed: () async {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          backgroundColor: Colors.white,
                          actions: [
                            AddExpenseForm(
                              isIncome: filtered[index].isIncome,
                              expense: filtered[index],
                            )
                          ],
                        ),
                      );
                    },
                    icon: const Icon(
                      Icons.edit,
                      color: Colors.black,
                    )),
                IconButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) =>
                            StatefulBuilder(builder: (context, setState2) {
                          return AlertDialog(
                            backgroundColor: Colors.white,
                            title: const Text("Delete!!"),
                            content:
                                const Text("Are you sure you want to delete?"),
                            actions: [
                              loading
                                  ? const CircularProgressIndicator()
                                  : TextButton(
                                      onPressed: () async {
                                        setState2(() => loading = true);

                                        try {
                                          final expenseDoc = filtered[index];
                                          final amount = expenseDoc.amount;
                                          final isCash =
                                              expenseDoc.cash == true;

                                          // 🔹 Get Yearly record
                                          final yearQuery = await FBFireStore
                                              .societyYearly
                                              .where('selectedyear',
                                                  isEqualTo: TheFinancialYear
                                                      .getCurrentYearForDatabase())
                                              .limit(1)
                                              .get();

                                          if (yearQuery.docs.isEmpty) {
                                            showCtcAppSnackBar(context,
                                                "Society Yearly not found.");
                                            setState2(() => loading = false);
                                            return;
                                          }

                                          final yearDoc = yearQuery.docs.first;
                                          final yearDocRef = FBFireStore
                                              .societyYearly
                                              .doc(yearDoc.id);
                                          final data = yearDoc.data();

                                          final currentTotalExpenses =
                                              (data['totalExpenses'] ?? 0)
                                                  as num;
                                          final currentCashBalance =
                                              (data['cashBalance'] ?? 0) as num;

                                          final batch = FBFireStore.fb.batch();
                                          final expenseDocRef = FBFireStore
                                              .expense
                                              .doc(expenseDoc.docID);

                                          batch.delete(expenseDocRef);

                                          final updateMap = <String, num>{
                                            'totalExpenses': !expenseDoc
                                                    .isIncome
                                                ? currentTotalExpenses - amount
                                                : currentTotalExpenses,
                                          };

                                          if (isCash) {
                                            if (expenseDoc.isIncome) {
                                              // Removing cash income → remove from balance
                                              updateMap['cashBalance'] =
                                                  currentCashBalance - amount;
                                            } else {
                                              // Removing cash expense → add back to balance
                                              updateMap['cashBalance'] =
                                                  currentCashBalance + amount;
                                            }
                                          }

                                          batch.update(yearDocRef, updateMap);
                                          await batch.commit();

                                          if (context.mounted) context.pop();
                                        } catch (e) {
                                          showCtcAppSnackBar(context,
                                              'Error deleting expense: $e');
                                        } finally {
                                          setState2(() => loading = false);
                                        }
                                      },
                                      child: const Text("Yes"),
                                    ),
                              TextButton(
                                onPressed: () {
                                  if (context.mounted) context.pop();
                                },
                                child: const Text("No"),
                              ),
                            ],
                          );
                        }),
                      );
                    },
                    icon: Icon(Icons.delete))
              ]))
            ])));
  }
}
