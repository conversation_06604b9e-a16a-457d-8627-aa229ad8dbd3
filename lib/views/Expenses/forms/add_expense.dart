import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/expenses_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';

class AddExpenseForm extends StatefulWidget {
  const AddExpenseForm({super.key, this.expense, required this.isIncome});
  final Expense? expense;
  final bool isIncome;
  @override
  State<AddExpenseForm> createState() => _AddExpenseFormState();
}

class _AddExpenseFormState extends State<AddExpenseForm> {
  TextEditingController namectrl = TextEditingController();
  TextEditingController commentctrl = TextEditingController();
  TextEditingController amountctrl = TextEditingController();
  TextEditingController expenseByctrl = TextEditingController();

  DateTime expenseDate = DateTime.now();
  String? attachmentUrl;
  bool loading = false;
  bool? iscashchecked = false;
  bool uploadingAttachment = false;

  // New state for financial year & paid now/payment date
  late List<String> financialYears;
  String? selectedYear;
  bool paidNow = false;
  DateTime? paymentDate;

  @override
  void initState() {
    super.initState();
    generateFinancialYears();
    if (widget.expense != null) {
      loadData();
    } else {
      namectrl.clear();
      commentctrl.clear();
      amountctrl.clear();
      expenseByctrl.clear();
      selectedYear = financialYears[2]; // default current FY
    }
  }

  // Generate financial years using TheFinancialYear utility
  void generateFinancialYears() {
    financialYears = TheFinancialYear.generateFinancialYearsList();
    financialYears.add("Others"); // Keep the Others option
  }

  Future<void> loadData() async {
    namectrl.text = widget.expense?.name ?? "";
    commentctrl.text = widget.expense?.comment ?? "";
    expenseByctrl.text = widget.expense?.expenseBy ?? "";
    amountctrl.text = widget.expense?.amount.toString() ?? "";
    iscashchecked = widget.expense?.cash;
    expenseDate = widget.expense!.expenseDate;
    attachmentUrl = widget.expense?.attachmentString ?? "";
    selectedYear = widget.expense?.selectedYear ?? financialYears[2];
    paidNow = widget.expense?.paidNow ?? false;
    paymentDate = widget.expense?.paymentDate;
  }

  Future<void> pickAndUploadFile() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      withData: true,
    );

    if (result != null && result.files.single.bytes != null) {
      Uint8List fileBytes = result.files.single.bytes!;
      String fileName = result.files.single.name;

      if (fileBytes.lengthInBytes > 3 * 1024 * 1024) {
        if (context.mounted) {
          showCtcAppSnackBar(context, "File size should not exceed 3MB.");
        }
        return;
      }

      try {
        final ref = FirebaseStorage.instance.ref('attachments/$fileName');
        await ref.putData(fileBytes);
        String url = await ref.getDownloadURL();
        attachmentUrl = url;
      } catch (e) {
        debugPrint('Upload failed: ${e.toString()}');
      }
    }
  }

  final List<String> kOptions = <String>[
    'EXECUTIVE MEETING',
    'RICKSHAW CHARAGES',
    'ADMISSITION A/C',
    'ADMISSION FEES',
    'STAMPING A/C',
    'STATIONARY A/C',
    'PRINTING A/C',
    'BANK CHARGES',
    'EDUCATION GIFT',
    'EDUCATION FUND',
    'INTERNAL AUDITOR FEES',
    'HONORIUM',
    'HONORARIUM PAYABLE',
    'REMUNARATION PAYABLE',
    'INTERNAL AUDIT FEES PAYABLE',
    'BENUVALENT FUND',
    'AMT PAYABLE TO EX MEMBER',
    'RTGS RETURN',
    'GOVERNMENT AUDIT FEES',
    'MISC.EXP',
    'ANNUAL GENERAL MEETING',
    'GIFT ACCOUNT',
    'DEPRICIATION',
    'AMT PAYABLE TO EX MEMBER',
    'AUDITOR FEES (GOVT)',
    'EXECUTIVE COMMITTEE',
    'LOCAL CONVEYANCE CHARGES',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      constraints: const BoxConstraints(maxWidth: 700, maxHeight: 700),
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.isIncome ? "Income" : "Expense",
                  style: const TextStyle(
                      fontSize: 30,
                      color: Colors.black,
                      fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 10),
                StaggeredGrid.extent(
                  maxCrossAxisExtent: 480,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 20,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Title *',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        LayoutBuilder(
                          builder: (context, constraints) {
                            final double maxWidth = constraints.maxWidth;
                            return Autocomplete<String>(
                              optionsViewBuilder: (BuildContext context,
                                  AutocompleteOnSelected<String> onSelected,
                                  Iterable<String> options) {
                                return Align(
                                  alignment: Alignment.topLeft,
                                  child: Material(
                                    elevation: 4,
                                    child: ConstrainedBox(
                                      constraints: BoxConstraints(
                                        maxWidth: maxWidth,
                                        maxHeight: 200,
                                        minWidth: maxWidth,
                                      ),
                                      child: ListView.builder(
                                        padding: EdgeInsets.zero,
                                        shrinkWrap: true,
                                        itemCount: options.length,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          final option =
                                              options.elementAt(index);
                                          return ListTile(
                                            title: Text(option),
                                            onTap: () {
                                              onSelected(option);
                                            },
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                );
                              },
                              optionsBuilder:
                                  (TextEditingValue textEditingValue) {
                                if (textEditingValue.text.isEmpty) {
                                  return const Iterable<String>.empty();
                                }
                                return kOptions.where((String option) {
                                  return option.toLowerCase().contains(
                                      textEditingValue.text.toLowerCase());
                                });
                              },
                              onSelected: (String selection) {
                                namectrl.text = selection;
                              },
                              fieldViewBuilder: (BuildContext context,
                                  TextEditingController
                                      fieldTextEditingController,
                                  FocusNode focusNode,
                                  VoidCallback onFieldSubmitted) {
                                fieldTextEditingController.text = namectrl.text;
                                return TextFormField(
                                  controller: fieldTextEditingController,
                                  focusNode: focusNode,
                                  onChanged: (value) {
                                    namectrl.text = value;
                                  },
                                  decoration: InputDecoration(
                                    filled: true,
                                    prefixIcon: const Icon(
                                      CupertinoIcons.person,
                                      size: 20,
                                      color: Colors.black,
                                    ),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide.none,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    hintText: 'Title',
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(bottom: 4.0),
                          child: widget.isIncome
                              ? Text(
                                  'IncomeBy *',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                )
                              : Text(
                                  'ExpenseBy *',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                ),
                        ),
                        TextField(
                          controller: expenseByctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.text_snippet_outlined,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText:
                                widget.isIncome ? 'IncomeBy' : 'ExpenseBy',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Amount *',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: amountctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.currency_rupee_sharp,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Amount',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: widget.isIncome
                              ? Text(
                                  'IncomeDate *',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                )
                              : Text(
                                  'ExpenseDate *',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                ),
                        ),
                        InkWell(
                          onTap: () async {
                            expenseDate = await showDatePicker(
                                    context: context,
                                    initialDate: expenseDate,
                                    firstDate: TheFinancialYear
                                        .getFinancialYearStartDate(TheFinancialYear
                                                .getCurrentFinancialYearStartYear() -
                                            1),
                                    lastDate: TheFinancialYear
                                        .getFinancialYearEndDate(TheFinancialYear
                                                .getCurrentFinancialYearStartYear() +
                                            1)) ??
                                DateTime.now();
                            setState(() {});
                          },
                          child: TextFormField(
                            style: TextStyle(color: Colors.black),
                            enabled: false,
                            controller: TextEditingController(
                                text: expenseDate.toString().split(' ').first),
                            maxLines: 1,
                            decoration: InputDecoration(
                              filled: true,
                              prefixIcon: const Icon(
                                Icons.date_range,
                                size: 20,
                                color: Colors.black,
                              ),
                              border: OutlineInputBorder(
                                  borderSide: BorderSide.none,
                                  borderRadius: BorderRadius.circular(6)),
                              hintText: 'ExpenseDate *',
                            ),
                          ),
                        ),
                      ],
                    ),
                    // Financial Year Dropdown
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text('Year',
                              style: TextStyle(fontWeight: FontWeight.w600)),
                        ),
                        DropdownButtonFormField<String>(
                          value: selectedYear,
                          items: financialYears
                              .map((fy) =>
                                  DropdownMenuItem(value: fy, child: Text(fy)))
                              .toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedYear = value;
                            });
                          },
                          decoration: InputDecoration(
                            filled: true,
                            border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            hintText: 'Select Financial Year',
                          ),
                        ),
                      ],
                    ),
                    // Paid Now Checkbox and conditional PaymentDate
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Checkbox(
                              value: paidNow,
                              onChanged: (value) {
                                setState(() {
                                  paidNow = value ?? false;
                                  if (!paidNow) paymentDate = null;
                                });
                              },
                            ),
                            Text(widget.isIncome ? "Received Now" : "Paid Now"),
                          ],
                        ),
                        if (paidNow) ...[
                          SizedBox(height: 10),
                        ],
                        if (paidNow)
                          InkWell(
                            onTap: () async {
                              final pickedDate = await showDatePicker(
                                context: context,
                                initialDate: paymentDate ?? DateTime.now(),
                                firstDate: TheFinancialYear
                                    .getFinancialYearStartDate(TheFinancialYear
                                            .getCurrentFinancialYearStartYear() -
                                        2),
                                lastDate: TheFinancialYear
                                    .getFinancialYearEndDate(TheFinancialYear
                                            .getCurrentFinancialYearStartYear() +
                                        2),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  paymentDate = pickedDate;
                                });
                              }
                            },
                            child: TextFormField(
                              style: TextStyle(color: Colors.black),
                              enabled: false,
                              controller: TextEditingController(
                                  text: paymentDate
                                          ?.toString()
                                          .split(' ')
                                          .first ??
                                      ""),
                              decoration: InputDecoration(
                                filled: true,
                                prefixIcon: Icon(
                                  Icons.date_range,
                                  color: Colors.black,
                                ),
                                border: OutlineInputBorder(
                                    borderSide: BorderSide.none,
                                    borderRadius: BorderRadius.circular(6)),
                                hintText: widget.isIncome
                                    ? 'Received Date'
                                    : 'Payment Date',
                              ),
                            ),
                          ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Comment',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: commentctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.comment,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Comment',
                          ),
                        ),
                      ],
                    ),
                    Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Checkbox(
                            value: iscashchecked,
                            onChanged: (value) {
                              setState(() {
                                iscashchecked = value;
                              });
                            },
                          ),
                          Text("CASH")
                        ],
                      ),
                    ),
                    (widget.isIncome == false)
                        ? (attachmentUrl != null)
                            ? uploadingAttachment
                                ? Center(child: CircularProgressIndicator())
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Expanded(
                                        child: Text(
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            '$attachmentUrl'),
                                      ),
                                      IconButton(
                                          onPressed: () async {
                                            if (attachmentUrl != null) {
                                              final ref =
                                                  FirebaseStorage.instance.ref(
                                                      'attachments/$attachmentUrl');
                                              try {
                                                await ref.delete();
                                              } catch (e) {
                                                debugPrint(
                                                    "Error deleting file: ${e.toString()}");
                                              }
                                            }
                                            setState(() {
                                              attachmentUrl = null;
                                            });
                                          },
                                          icon: Icon(Icons
                                              .restore_from_trash_outlined))
                                    ],
                                  )
                            : ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.all(16),
                                  backgroundColor: Colors.green.shade300,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8)),
                                ),
                                onPressed: () async {
                                  setState(() {
                                    uploadingAttachment = true;
                                  });
                                  await pickAndUploadFile();
                                  setState(() {
                                    uploadingAttachment = false;
                                  });
                                },
                                child: Text(
                                  "Add Attachment",
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 16),
                                ))
                        : SizedBox.shrink(),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      loading
                          ? const CircularProgressIndicator()
                          : ElevatedButton(
                              onPressed: () async {
                                if (amountctrl.text.isEmpty ||
                                    namectrl.text.isEmpty ||
                                    expenseByctrl.text.isEmpty ||
                                    selectedYear == null) {
                                  showCtcAppSnackBar(
                                      context, "Please Enter All Details");
                                  return;
                                }

                                setState(() => loading = true);

                                try {
                                  final amount = num.tryParse(amountctrl.text);
                                  if (amount == null) {
                                    showCtcAppSnackBar(
                                        context, "Invalid amount");
                                    setState(() => loading = false);
                                    return;
                                  }

                                  // 🔹 Query yearly record
                                  final yearQuery = await FBFireStore
                                      .societyYearly
                                      .where('selectedyear',
                                          isEqualTo: TheFinancialYear
                                              .getCurrentYearForDatabase())
                                      .limit(1)
                                      .get();

                                  if (yearQuery.docs.isEmpty) {
                                    showCtcAppSnackBar(
                                        context, "Yearly data not found");
                                    setState(() => loading = false);
                                    return;
                                  }

                                  final yearDoc = yearQuery.docs.first;
                                  final yearDocRef =
                                      FBFireStore.societyYearly.doc(yearDoc.id);
                                  final data = yearDoc.data();

                                  final currentTotalExpenses =
                                      (data['totalExpenses'] ?? 0) as num;
                                  final currentCashBalance =
                                      (data['cashBalance'] ?? 0) as num;

                                  // 🔹 Prevent negative cash for expenses
                                  if (!widget.isIncome &&
                                      iscashchecked == true &&
                                      amount > currentCashBalance) {
                                    showCtcAppSnackBar(
                                        context, "Not enough cash balance");
                                    setState(() => loading = false);
                                    return;
                                  }

                                  // 🔹 Batch write
                                  final batch = FBFireStore.fb.batch();
                                  final newExpenseRef = widget.expense == null
                                      ? FBFireStore.expense.doc()
                                      : FBFireStore.expense
                                          .doc(widget.expense!.docID);

                                  // Set/Update doc data
                                  final expenseData = {
                                    'name': namectrl.text,
                                    'expenseBy': expenseByctrl.text,
                                    'comment': commentctrl.text,
                                    'expenseDate': expenseDate,
                                    'selectedYear': selectedYear ?? "",
                                    'paidNow': paidNow,
                                    'paymentDate': paymentDate,
                                    'createdAt': DateTime.now(),
                                    'isIncome': widget.isIncome,
                                    'amount': amount,
                                    'cash': iscashchecked == true,
                                  };
                                  if (!widget.isIncome &&
                                      attachmentUrl != null) {
                                    expenseData['attachmentString'] =
                                        attachmentUrl ?? "";
                                  }

                                  if (widget.expense == null) {
                                    batch.set(newExpenseRef, expenseData);

                                    // Update yearly totals
                                    if (!widget.isIncome) {
                                      batch.update(yearDocRef, {
                                        'totalExpenses':
                                            currentTotalExpenses + amount,
                                        if (iscashchecked == true)
                                          'cashBalance':
                                              currentCashBalance - amount
                                      });
                                    } else {
                                      if (iscashchecked == true) {
                                        batch.update(yearDocRef, {
                                          'cashBalance':
                                              currentCashBalance + amount
                                        });
                                      }
                                    }
                                  } else {
                                    batch.update(newExpenseRef, expenseData);
                                    // For updates, adjust cash balance differences if needed
                                  }

                                  await batch.commit();

                                  // Reset form
                                  namectrl.clear();
                                  expenseByctrl.clear();
                                  commentctrl.clear();
                                  amountctrl.clear();
                                  iscashchecked = false;
                                  paidNow = false;
                                  paymentDate = null;

                                  if (context.mounted) context.pop();
                                } catch (e) {
                                  showCtcAppSnackBar(
                                      context, "An error occurred: $e");
                                  debugPrint(e.toString());
                                } finally {
                                  if (context.mounted) {
                                    setState(() => loading = false);
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green.shade300,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8)),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(vertical: 8.0),
                                child: SizedBox(
                                  width: 100,
                                  child: Center(
                                    child: Text(
                                      "Submit",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 16),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      }),
    );
  }
}
