// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../../controller/homectrl.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/const.dart';

class AddCashForm extends StatefulWidget {
  const AddCashForm({super.key});

  @override
  State<AddCashForm> createState() => _AddCashFormState();
}

// TextEditingController namectrl = TextEditingController();
TextEditingController commentctrl = TextEditingController();
TextEditingController amountctrl = TextEditingController();
TextEditingController addedByctrl = TextEditingController();

DateTime addedDate = DateTime.now();

bool loading = false;

class _AddCashFormState extends State<AddCashForm> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      constraints: const BoxConstraints(maxWidth: 700),
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Add Cash",
                  // widget.isIncome ? "Income" : "Expense",
                  style: const TextStyle(
                      fontSize: 30,
                      color: Colors.black,
                      fontWeight: FontWeight.w600),
                ),
                // const Text(
                //   'Lorem ipsum dolor lorem ispsum dolor',
                // ),
                const SizedBox(height: 10),
                StaggeredGrid.extent(
                  maxCrossAxisExtent: 480,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 20,
                  children: [
                    // Column(
                    //   crossAxisAlignment: CrossAxisAlignment.start,
                    //   children: [
                    //     const Padding(
                    //       padding: EdgeInsets.only(bottom: 4.0),
                    //       child: Text(
                    //         'Name',
                    //         style: TextStyle(fontWeight: FontWeight.w600),
                    //       ),
                    //     ),
                    //     TextFormField(
                    //       controller: namectrl,
                    //       decoration: InputDecoration(
                    //         // fillColor: Colors.white,
                    //         filled: true,
                    //         prefixIcon: const Icon(
                    //           CupertinoIcons.person,
                    //           size: 20,
                    //           color: Colors.black,
                    //         ),
                    //         border: OutlineInputBorder(
                    //             borderSide: BorderSide.none,
                    //             borderRadius: BorderRadius.circular(6)),
                    //         hintText: 'Name',
                    //       ),
                    //     ),
                    //   ],
                    // ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Added By',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: addedByctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            // fillColor: Colors.white,
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.text_snippet_outlined,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Added By',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Amount',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: amountctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            // fillColor: Colors.white,
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.currency_rupee_sharp,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Amount',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Added Date',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            addedDate = await showDatePicker(
                                    context: context,
                                    initialDate: addedDate,
                                    firstDate: TheFinancialYear
                                        .getFinancialYearStartDate(TheFinancialYear
                                                .getCurrentFinancialYearStartYear() -
                                            1),
                                    lastDate: TheFinancialYear
                                        .getFinancialYearEndDate(TheFinancialYear
                                                .getCurrentFinancialYearStartYear() +
                                            1)) ??
                                DateTime.now();
                            setState(() {});
                            // print(addedDate);
                          },
                          child: TextFormField(
                            style: TextStyle(color: Colors.black),
                            enabled: false,
                            controller: TextEditingController(
                                text: addedDate.toString().split(' ').first),
                            // initialValue:
                            //     expenseDate.toString().split(' ').first,
                            maxLines: 1,
                            decoration: InputDecoration(
                              filled: true,
                              prefixIcon: const Icon(
                                Icons.date_range,
                                size: 20,
                                color: Colors.black,
                              ),
                              border: OutlineInputBorder(
                                  borderSide: BorderSide.none,
                                  borderRadius: BorderRadius.circular(6)),
                              hintText: 'ExpenseDate',
                            ),
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Comment',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: commentctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            // fillColor: Colors.white,
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.comment,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Comment',
                          ),
                        ),
                      ],
                    ),
                    // Center(
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.center,
                    //     children: [
                    //       Checkbox(
                    //         value: true,
                    //         onChanged: (value) {},
                    //       ),
                    //       Text("CASH")
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 25.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      loading
                          ? const CircularProgressIndicator()
                          : ElevatedButton(
                              onPressed: () async {
                                // Input validation
                                if (amountctrl.text.isEmpty ||
                                    addedByctrl.text.isEmpty ||
                                    commentctrl.text.isEmpty) {
                                  showCtcAppSnackBar(
                                      context, "Please Enter All Details");
                                  return;
                                }

                                setState(() {
                                  loading = true;
                                });

                                try {
                                  final amount = num.tryParse(amountctrl.text);
                                  if (amount == null) {
                                    showCtcAppSnackBar(
                                        context, "Invalid Amount");
                                    setState(() {
                                      loading = false;
                                    });
                                    return;
                                  }

                                  // Get the yearly document
                                  final querySnapshot = await FBFireStore
                                      .societyYearly
                                      .where('selectedyear',
                                          isEqualTo: TheFinancialYear
                                              .getCurrentYearForDatabase())
                                      .limit(1)
                                      .get();

                                  if (querySnapshot.docs.isEmpty) {
                                    showCtcAppSnackBar(
                                        context, "Yearly data not found");
                                    setState(() {
                                      loading = false;
                                    });
                                    return;
                                  }

                                  final doc = querySnapshot.docs.first;
                                  final docRef =
                                      FBFireStore.societyYearly.doc(doc.id);
                                  final cashRef = FBFireStore.cash
                                      .doc(); // Auto-generate doc ID

                                  final currentBalance =
                                      (doc['cashBalance'] ?? 0) as num;

                                  // Create batch
                                  final batch = FBFireStore.fb.batch();

                                  // Add cash document
                                  batch.set(cashRef, {
                                    'addedBy': addedByctrl.text,
                                    'comment': commentctrl.text,
                                    'addeddate': addedDate,
                                    'createdAt': DateTime.now(),
                                    'amount': amount,
                                  });

                                  // Update balance
                                  batch.update(docRef, {
                                    'cashBalance': currentBalance + amount,
                                  });

                                  print(
                                      "soc yearly cash bal : $currentBalance + $amount");

                                  // Commit the batch
                                  await batch.commit();

                                  showCtcAppSnackBar(context, "Cash Added");

                                  // Clear controls
                                  addedByctrl.clear();
                                  commentctrl.clear();
                                  amountctrl.clear();

                                  if (context.mounted) {
                                    context.pop();
                                  }
                                } catch (e) {
                                  showCtcAppSnackBar(
                                      context, "An error occurred: $e");
                                } finally {
                                  if (context.mounted) {
                                    setState(() {
                                      loading = false;
                                    });
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green.shade300,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(vertical: 8.0),
                                child: SizedBox(
                                  width: 100,
                                  child: Center(
                                    child: Text(
                                      "ADD CASH",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 16),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      }),
    );
  }
}
