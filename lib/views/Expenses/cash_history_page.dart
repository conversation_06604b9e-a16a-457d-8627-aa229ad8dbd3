import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:get/get.dart';

import '../../shared/methods.dart';

class CashHistoryPage extends StatefulWidget {
  const CashHistoryPage({super.key});

  @override
  State<CashHistoryPage> createState() => _CashHistoryPageState();
}

class _CashHistoryPageState extends State<CashHistoryPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return SingleChildScrollView(
        padding: EdgeInsets.all(40),
        child: Column(
          children: [
            const Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Added Date"),
                HeaderTxt(txt: "Added By"),
                HeaderTxt(txt: "Amount"),
                HeaderTxt(txt: 'Comment'),
              ],
            ),
            SizedBox(height: 50),
            ...List.generate(
              ctrl.cashModel.length,
              (index) {
                return Container(
                  color: index % 2 == 0 ? Colors.white : null,
                  height: 40,
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    children: [
                      Expanded(child: Text("${index + 1}")),
                      Expanded(
                          child: Text(ctrl.cashModel[index].addeddate
                              .toString()
                              .split(' ')
                              .first)),
                      Expanded(child: Text(ctrl.cashModel[index].addedBy)),
                      Expanded(
                          child: Text(ctrl.cashModel[index].amount.toString())),
                      Expanded(
                          child:
                              Text(ctrl.cashModel[index].comment.toString())),
                    ],
                  ),
                );
              },
            )
          ],
        ),
      );
    });
  }
}
