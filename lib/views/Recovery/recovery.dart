// ignore_for_file:  use_build_context_synchronously

import 'dart:convert';
import 'dart:typed_data';
import 'package:csv/csv.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/recovery_page_model.dart';
import 'package:foodcorp_admin/models/society_yearlyrecord_model.dart';
import 'package:foodcorp_admin/models/user_monthlyrecord_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pluto_grid/pluto_grid.dart';
import '../../common/page_header.dart';
import '../../models/districtoffice_model.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../models/recovery_monthly_model.dart';
import '../../shared/firebase.dart';

class RecoveryPage extends StatefulWidget {
  const RecoveryPage({super.key});

  @override
  State<RecoveryPage> createState() => _RecoveryPageState();
}

class _RecoveryPageState extends State<RecoveryPage> {
  SearchController sctrl = SearchController();
  String? selectedoffice;
  int? selectedMonth;
  int? selectedYear;
  List<RecoveryPageModel> recoveryPageModel = [];
  List<RecoveryMonthlyModel> recoveryMonthlyModel = [];
  num dues = 0;
  num totalPenalty = 0;

  final hctrl = Get.find<HomeCtrl>();

  List<DistrictOfficeModel> doffice = Get.find<HomeCtrl>().districtoffice;
  List<UserMonthlyRecordModel> fbData = [];
  List<SocietyYearlyRecordModel> fbsyDataList = [];
  QuerySnapshot<Map<String, dynamic>>? fbUserMonthlyDataFetch;
  QuerySnapshot<Map<String, dynamic>>? fbUserDataFetch;
  QuerySnapshot<Map<String, dynamic>>? fbRecoveryMonthlyDataFetch;
  QuerySnapshot<Map<String, dynamic>>? fbSocietyYearlyDataFetch;
  QuerySnapshot<Map<String, dynamic>>? lastMonthData;

  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  late PlutoGridStateManager stateManager;

  bool mailSent = false;
  bool savingData = false;
  bool applyPrevMonthData = false;
  bool resetingData = false;
  bool downloadingData = false;

  final List<PlutoColumn> columns = [
    PlutoColumn(
      enableEditingMode: false,
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.text(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'NAME',
      field: 'name',
      type: PlutoColumnType.text(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'District Office',
      field: 'district_office',
      type: PlutoColumnType.text(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
        enableEditingMode: false,
        title: 'OB LT',
        field: 'ob_lt',
        type: PlutoColumnType.number()),
    PlutoColumn(
        enableEditingMode: false,
        title: 'OB ST',
        field: 'ob_st',
        type: PlutoColumnType.number()),
    PlutoColumn(
        enableEditingMode: false,
        title: 'LOAN PAID LT',
        field: 'loan_paid_lt',
        type: PlutoColumnType.number()),
    PlutoColumn(
        enableEditingMode: false,
        title: 'LOAN PAID ST',
        field: 'loan_paid_st',
        type: PlutoColumnType.number()),
    PlutoColumn(
        enableEditingMode: false,
        title: 'LOAN TOTAL',
        field: 'loan_total',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'SUB',
        field: 'sub',
        type: PlutoColumnType.number(),
        backgroundColor: Color(0xffC9E9D2)),
    PlutoColumn(
      enableEditingMode: true,
      title: 'LT INSTALLMENT',
      field: 'lt_installment',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: true,
      title: 'ST INSTALLMENT',
      field: 'st_installment',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: true,
      title: 'INTEREST',
      field: 'interest',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'DUES',
      field: 'dues',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'PENALTY',
      field: 'penalty',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      enableEditingMode: false,
      title: 'TOTAL',
      field: 'total',
      type: PlutoColumnType.number(),
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
        enableEditingMode: false,
        title: 'LT CLOSING BALANCE',
        field: 'lt_closing_balance',
        type: PlutoColumnType.number()),
    PlutoColumn(
        enableEditingMode: false,
        title: 'ST CLOSING BALANCE',
        field: 'st_closing_balance',
        type: PlutoColumnType.number()),
    PlutoColumn(
        backgroundColor: Color(0xffC9E9D2),
        title: 'INST. RECEIVED',
        field: 'installment_received',
        type: PlutoColumnType.number()),
    PlutoColumn(
        backgroundColor: Color(0xffC9E9D2),
        title: 'INST. RECEIVED DATE',
        field: 'installmentRecDate',
        type: PlutoColumnType.date()),
    PlutoColumn(
        title: 'SUBSCRIPTION PAID',
        field: 'subscriptionPaid',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'LT INTEREST PAID',
        field: 'longTermInterestPaid',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'ST INTEREST PAID',
        field: 'shortTermInterestPaid',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'LT INSTALLMENT PAID',
        field: 'longTermInstalmentPaid',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'ST INSTALLMENT PAID',
        field: 'shortTermInstalmentPaid',
        type: PlutoColumnType.number()),
    PlutoColumn(
        title: 'PENALTY PAID',
        field: 'penaltyPaid',
        type: PlutoColumnType.number()),

    // PlutoColumn(
    //     title: 'TOTAL RECEIVED',
    //     field: 'totalReceived',
    //     type: PlutoColumnType.number()),
    PlutoColumn(
      title: 'Status',
      field: 'status',
      type: PlutoColumnType.text(),
      // enableEditingMode: false,
      // renderer: (PlutoColumnRendererContext context) {
      //   num? installmentReceived = num.tryParse(
      //       context.row.cells['installment_received']?.value.toString() ?? '0');
      //   num? total =
      //       num.tryParse(context.row.cells['total']?.value.toString() ?? '0');
      //   bool isMissingPayment = (installmentReceived != total);
      //   return isMissingPayment
      //       ? const Icon(Icons.warning, color: Colors.red)
      //       : const Icon(Icons.check_circle, color: Colors.green);
      // },
    ),
  ];

  void recipetCalc(PlutoGridOnChangedEvent event) {
    final cpfNo = event.row.cells['cpfno']?.value;
    final user = hctrl.users.firstWhereOrNull(
      (element) => element.cpfNo == cpfNo,
    );

    // If user is inactive, skip ALL calculations
    if (user != null && !user.isActive) {
      return; // Don't perform any calculations
    }

    final obLt =
        num.tryParse(event.row.cells['ob_lt']?.value.toString() ?? "0") ?? 0;
    final obSt =
        num.tryParse(event.row.cells['ob_st']?.value.toString() ?? "0") ?? 0;
    final loanPaidLt = num.tryParse(
            event.row.cells['loan_paid_lt']?.value.toString() ?? "0") ??
        0;
    final loanPaidSt = num.tryParse(
            event.row.cells['loan_paid_st']?.value.toString() ?? "0") ??
        0;
    final ltInstallment = num.tryParse(
            event.row.cells['lt_installment']?.value.toString() ?? "0") ??
        0;
    final stInstallment = num.tryParse(
            event.row.cells['st_installment']?.value.toString() ?? "0") ??
        0;
    final sub =
        num.tryParse(event.row.cells['sub']?.value.toString() ?? "0") ?? 0;

    // 1. Update loan_total
    final loanTotal = obLt + obSt + loanPaidLt + loanPaidSt;
    event.row.cells['loan_total']?.value = loanTotal;

    // 2. Update interest
    final ltInterestRate =
        num.tryParse(hctrl.settings?.ltloanInterest.toString() ?? "0") ?? 0;
    final stInterestRate =
        num.tryParse(hctrl.settings?.stloanInterest.toString() ?? "0") ?? 0;
    num interest =
        num.tryParse(event.row.cells['interest']?.value.toString() ?? "0") ?? 0;
    if (event.column.field != 'interest') {
      final interest = ((obLt + loanPaidLt) * ltInterestRate / 1200) +
          ((obSt + loanPaidSt) * stInterestRate / 1200);
      event.row.cells['interest']?.value = interest;
    }

    // 3. Update total
    final total = ltInstallment + stInstallment + sub + interest;
    event.row.cells['total']?.value = total;

    // 4. Update closing balances
    event.row.cells['lt_closing_balance']?.value =
        obLt + loanPaidLt - ltInstallment;
    event.row.cells['st_closing_balance']?.value =
        obSt + loanPaidSt - stInstallment;

    if (event.column.field == 'installment_received') {
      num subscription =
          num.tryParse('${hctrl.settings?.defaultSubsinstallAmt ?? 0}') ?? 0;
      num installmentRec =
          (event.row.cells['installment_received']?.value ?? 0);
      num remainingAmt = installmentRec;

      // PREVIOUS DUES & PENALTY
      final prevDues = event.row.cells['dues']?.value ?? 0;
      final prevPenalty = event.row.cells['penalty']?.value ?? 0;
      final totalExpected = (event.row.cells['total']?.value ?? 0);

      if (prevDues > 0 || prevPenalty > 0) {
        final double totalVal = toDouble(event.row.cells['total']?.value);
        final double installmentRecVal =
            toDouble(event.row.cells['installment_received']?.value);

        final int totalCeil = totalVal.round();
        final int installmentRecCeil = installmentRecVal.round();

        if (installmentRecCeil == 0) {
          event.row.cells['dues']?.value = totalCeil;
          event.row.cells['status']?.value = 'Unpaid';
        } else if (installmentRecCeil >= totalCeil) {
          // Use >= to avoid floating point issues
          event.row.cells['dues']?.value = 0;
          event.row.cells['penalty']?.value = 0;
          event.row.cells['status']?.value = 'Paid';
        } else {
          event.row.cells['dues']?.value = (totalCeil - installmentRecCeil);
          event.row.cells['status']?.value = 'Partially Paid';
        }
      } else {
        final double totalVal = toDouble(event.row.cells['total']?.value);
        final double installmentRecVal =
            toDouble(event.row.cells['installment_received']?.value);

        if (installmentRecVal >= totalVal) {
          // Paid in full or overpaid
          event.row.cells['dues']?.value = 0;
          event.row.cells['penalty']?.value = 0;
          event.row.cells['status']?.value = 'Paid';
        } else if (installmentRecVal > 0) {
          event.row.cells['dues']?.value =
              (totalVal - installmentRecVal).round();
          // ...calculate penalty as before...
          event.row.cells['status']?.value = 'Partially Paid';
        } else {
          event.row.cells['dues']?.value = totalVal.round();
          event.row.cells['status']?.value = 'Unpaid';
        }
      }

      if (remainingAmt - subscription > 0) {
        event.row.cells['subscriptionPaid']?.value = subscription;
        remainingAmt -= subscription;
      } else {
        event.row.cells['subscriptionPaid']?.value = remainingAmt;
        remainingAmt = 0;
      }

      // Interest Calculations

      num longTermInterest = (((event.row.cells['ob_lt']?.value ?? 0) +
              (event.row.cells['loan_paid_lt']?.value ?? 0)) *
          (num.tryParse(hctrl.settings?.ltloanInterest.toString() ?? "0") ??
              0) /
          1200);

      if ((event.row.cells['ob_lt']?.value ?? 0) > 0) {
        if (remainingAmt - longTermInterest > 0) {
          event.row.cells['longTermInterestPaid']?.value = longTermInterest;
          remainingAmt -= longTermInterest;
        } else {
          event.row.cells['longTermInterestPaid']?.value = remainingAmt;
          remainingAmt = 0;
        }
      }

      num shortTermInterest = (((event.row.cells['ob_st']?.value ?? 0) +
              (event.row.cells['loan_paid_st']?.value ?? 0)) *
          (num.tryParse(hctrl.settings?.stloanInterest.toString() ?? "0") ??
              0) /
          1200);

      if ((event.row.cells['ob_st']?.value ?? 0) > 0) {
        if (remainingAmt - shortTermInterest > 0) {
          event.row.cells['shortTermInterestPaid']?.value = shortTermInterest;
          remainingAmt -= shortTermInterest;
        } else {
          event.row.cells['shortTermInterestPaid']?.value = remainingAmt;
          remainingAmt = 0;
        }
      }

      if (remainingAmt - (event.row.cells['lt_installment']?.value ?? 0) > 0) {
        event.row.cells['longTermInstalmentPaid']?.value =
            event.row.cells['lt_installment']?.value;
        remainingAmt -= event.row.cells['lt_installment']?.value;
      } else {
        event.row.cells['longTermInstalmentPaid']?.value = remainingAmt;
        remainingAmt = 0;
      }

      if (remainingAmt - (event.row.cells['st_installment']?.value ?? 0) > 0) {
        event.row.cells['shortTermInstalmentPaid']?.value =
            event.row.cells['st_installment']?.value;
        remainingAmt -= event.row.cells['st_installment']?.value;
      } else {
        event.row.cells['shortTermInstalmentPaid']?.value = remainingAmt;
        remainingAmt = 0;
      }

      //penaltyPaid

      if (remainingAmt - (event.row.cells['penalty']?.value ?? 0) > 0) {
        event.row.cells['penaltyPaid']?.value =
            event.row.cells['penalty']?.value;
        remainingAmt -= event.row.cells['penalty']?.value;
      } else {
        event.row.cells['penaltyPaid']?.value = remainingAmt;
        remainingAmt = 0;
      }

      // FINAL DUES CALCULATION
      num dues = 0;
      if (installmentRec < totalExpected) {
        dues = totalExpected - installmentRec;

        // Penalty Calculation
        final ltIntPaid = event.row.cells['longTermInterestPaid']?.value ?? 0;
        final stIntPaid = event.row.cells['shortTermInterestPaid']?.value ?? 0;

        // print("ltIntPaid : $ltIntPaid");
        // print("stIntPaid : $stIntPaid");

        final defaultLtInterestRate =
            num.tryParse(hctrl.settings?.ltloanInterest.toString() ?? "0") ?? 0;
        final defaultStInterestRate =
            num.tryParse(hctrl.settings?.stloanInterest.toString() ?? "0") ?? 0;

        num longTermInterest = ((event.row.cells['ob_lt']?.value ??
                0 + event.row.cells['loan_paid_lt']?.value ??
                0) *
            defaultLtInterestRate /
            1200);

        num shortTermInterest = ((event.row.cells['ob_st']?.value ??
                0 + event.row.cells['loan_paid_st']?.value ??
                0) *
            defaultStInterestRate /
            1200);

        final unpaidLongTermInterest =
            (longTermInterest - ltIntPaid).clamp(0, double.infinity);
        final unpaidShortTermInterest =
            (shortTermInterest - stIntPaid).clamp(0, double.infinity);

        final ltUnpaid =
            unpaidLongTermInterest > 0 ? unpaidLongTermInterest : 0;
        final stUnpaid =
            unpaidShortTermInterest > 0 ? unpaidShortTermInterest : 0;

        final ltloanpenalty = ((ltUnpaid * defaultLtInterestRate) / 1200);
        final stloanpenalty = ((stUnpaid * defaultStInterestRate) / 1200);

        final totalPenalty = (ltloanpenalty.round() + stloanpenalty.round())
            .clamp(0, double.infinity);

        event.row.cells['dues']?.value = dues;
        event.row.cells['penalty']?.value = totalPenalty;

        // Update status

        final double totalVal = toDouble(event.row.cells['total']?.value);
        final double installmentRecVal =
            toDouble(event.row.cells['installment_received']?.value);

        final int totalCeil = totalVal.round();
        final int installmentRecCeil = installmentRecVal.round();

        if (installmentRecCeil == 0) {
          event.row.cells['dues']?.value = totalCeil;
          event.row.cells['status']?.value = 'Unpaid';
        } else if (installmentRecCeil == totalCeil) {
          event.row.cells['dues']?.value = 0;
          event.row.cells['penalty']?.value = 0;
          event.row.cells['status']?.value = 'Paid';
        } else {
          event.row.cells['dues']?.value = (totalCeil - installmentRecCeil);
          event.row.cells['status']?.value = 'Partially Paid';
        }
      } else {
        // Fully Paid
        event.row.cells['dues']?.value = 0;
        event.row.cells['penalty']?.value = 0;
        event.row.cells['status']?.value = 'Paid';
      }
    }

    stateManager.notifyListeners();
  }

  double toDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  @override
  void initState() {
    super.initState();
    DateTime currentDate = DateTime.now();
    selectedMonth = currentDate.month;
    selectedYear = TheFinancialYear.getCurrentYearForDatabase();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    selectedoffice = null;
    dataFetchLocally();
  }

  Future<String> generatePDF() async {
    try {
      final pdfDocument = pw.Document();

      List<Map<String, String?>> recoveryData = rows.map((row) {
        return {
          'cpfNo': row.cells['cpf_no']?.value.toString(),
          'memberName': row.cells['name']?.value.toString(),
          'districtOffice': row.cells['district_office']?.value.toString(),
          'total': row.cells['total']?.value.toString(),
        };
      }).toList();

      final totalRow = rows.last;
      if (totalRow.cells['name']?.value == 'TOTAL') {
        recoveryData.add({
          'cpfNo': '',
          'memberName': 'TOTAL',
          'districtOffice': '',
          'total': totalRow.cells['total']?.value.toString(),
        });
      }

      if (recoveryData.isEmpty) {
        // print("Error: recoveryData is empty or undefined");
        return '';
      }

      pdfDocument.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return [
              pw.Text('Recovery Statement',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  )),
              pw.SizedBox(height: 20),
              pw.Table(
                border: pw.TableBorder.all(),
                columnWidths: {
                  0: const pw.FixedColumnWidth(100),
                  1: const pw.FixedColumnWidth(200),
                  2: const pw.FixedColumnWidth(150),
                  3: const pw.FixedColumnWidth(80),
                },
                children: [
                  pw.TableRow(
                    decoration:
                        const pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('CPF NO.',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('NAME OF MEMBER',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('DISTRICT OFFICE',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('TOTAL',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                    ],
                  ),
                  ...recoveryData.map((data) {
                    final isTotal = data['memberName'] == 'TOTAL';
                    return pw.TableRow(
                      decoration: isTotal
                          ? const pw.BoxDecoration(color: PdfColors.grey200)
                          : null,
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(data['cpfNo'] ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            data['memberName'] ?? '',
                            style: isTotal
                                ? pw.TextStyle(fontWeight: pw.FontWeight.bold)
                                : null,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(data['districtOffice'] ?? ''),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            data['total'] ?? '',
                            style: isTotal
                                ? pw.TextStyle(fontWeight: pw.FontWeight.bold)
                                : null,
                          ),
                        ),
                      ],
                    );
                  }),
                ],
              ),
              pw.SizedBox(height: 20),
              pw.Text('Thanking You,', style: pw.TextStyle(fontSize: 12)),
              pw.Text('FCI Emp. Co. Op. Credit Society.',
                  style: pw.TextStyle(fontSize: 12)),
              pw.Text('Baroda', style: pw.TextStyle(fontSize: 12)),
            ];
          },
        ),
      );

      final pdfFileBytes = await pdfDocument.save();
      // print('PDF bytes length: ${pdfFileBytes.length}');
      String base64Pdf = base64Encode(pdfFileBytes);
      return base64Pdf;
    } catch (e) {
      debugPrint(e.toString());
      // print('Error generating PDF: $e');
      return '';
    }
  }

  String getEmailForDO(String? officeId) {
    var office = doffice.firstWhereOrNull((office) => office.docId == officeId);
    return office?.email ?? '<EMAIL>';
  }

  // final last6Months = List.generate(7, (index) {
  //   final date = DateTime(DateTime.now().year, DateTime.now().month - index, 1);
  //   return date;
  // }).reversed.toList();

  List<DateTime> generateMonthsToShow() {
    final now = DateTime.now();
    final fyStartYear = now.month >= 4 ? now.year : now.year - 1;
    final startMonth = 4; // April
    final endMonth =
        now.month >= 4 ? now.month : (now.month + 12); // For Jan–Mar

    final monthsToShow = <DateTime>[];
    for (int m = startMonth; m <= endMonth; m++) {
      final month = ((m - 1) % 12) + 1;
      final year = m <= 12 ? fyStartYear : fyStartYear + 1;
      monthsToShow.add(DateTime(year, month, 1));
    }
    return monthsToShow;
  }

  @override
  Widget build(BuildContext context) {
    final monthsToShow = generateMonthsToShow();

    final size = MediaQuery.sizeOf(context);

    if (recoveryPageModel.isEmpty) {
      dataFetchLocally();
    }

    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ctrl.districtoffice.sort((a, b) => a.name.compareTo(b.name));
      return SingleChildScrollView(
        physics: NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            const SizedBox(width: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomSearchBarWidget(
                  searchController: sctrl,
                  searchOnChanged: (query) {
                    if (query.isEmpty) {
                      // Clear filters
                      stateManager.rows.clear();
                      // stateManager.setFilter((element) => true);
                    } else {
                      final lowerQuery = query.toLowerCase();
                      stateManager.setFilter((PlutoRow row) {
                        final cpfNo = row.cells['cpf_no']?.value
                                ?.toString()
                                .toLowerCase() ??
                            '';
                        final empNo = row.cells['employeeNo']?.value
                                ?.toString()
                                .toLowerCase() ??
                            '';
                        final name = row.cells['name']?.value
                                ?.toString()
                                .toLowerCase() ??
                            '';
                        return cpfNo.contains(lowerQuery) ||
                            empNo.contains(lowerQuery) ||
                            name.contains(lowerQuery);
                      });
                    }
                    stateManager.notifyListeners();
                  },
                ),
                SizedBox(width: 10),
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  focusColor: Colors.transparent,
                  dropdownColor: Colors.white,
                  decoration: InputDecoration(
                      hintText: "Select DO",
                      hintStyle: TextStyle(fontSize: 30),
                      constraints:
                          const BoxConstraints(maxWidth: 250, maxHeight: 45),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(5))),
                  initialValue: selectedoffice,
                  items: [
                    DropdownMenuItem(
                      value: 'ALL',
                      child: Text('ALL'),
                    ),
                    ...ctrl.districtoffice.map((office) => DropdownMenuItem(
                          value: office.docId,
                          child: Text(office.name),
                        )),
                  ],
                  onChanged: (value) async {
                    setState(() {
                      selectedoffice = value;
                    });

                    dataFetchLocally();
                    await calcRowsData(false);
                    await loadPrevMonthBalances();
                    stateManager.removeAllRows();
                    setState(() {
                      stateManager.insertRows(0, rows);
                    });
                  },
                )),
                SizedBox(width: 10),
                Row(
                  children: [
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField<DateTime>(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        initialValue: monthsToShow.firstWhere(
                            (d) =>
                                d.month == selectedMonth &&
                                d.year == selectedYear,
                            orElse: () => DateTime.now()),
                        decoration: InputDecoration(
                          hintText: "Select Month",
                          constraints: const BoxConstraints(
                              maxWidth: 155, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: monthsToShow.map((date) {
                          final formatted =
                              "${month[date.month - 1]} ${date.year}";
                          return DropdownMenuItem<DateTime>(
                            value: date,
                            child: Text(formatted),
                          );
                        }).toList(),
                        onChanged: (value) async {
                          if (value != null) {
                            setState(() {
                              selectedMonth = value.month;
                            });

                            // Clear existing rows
                            stateManager.removeAllRows();

                            // Fetch new month data
                            await loadPrevMonthBalances();

                            // Check if rows list is empty (no data for selected month)
                            if (rows.isEmpty) {
                              showCtcAppSnackBar(
                                  context, "No data for this month");
                              return;
                            }

                            // Apply previous month balances and populate the grid
                            await applyPrevMonthBalances();
                            stateManager.appendRows(rows);

                            // print(
                            //     "Selected month: $selectedMonth, year: $selectedYear");
                          }
                          // if (value != null) {
                          //   selectedMonth = value.month;
                          //   selectedYear = value.year;
                          //   stateManager.removeAllRows();
                          //   await loadPrevMonthBalances();
                          //   await applyPrevMonthBalances();
                          //   stateManager.appendRows(rows);
                          //   setState(() {});
                          // }
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    DropdownButtonHideUnderline(
                        child: DropdownButtonFormField(
                      focusColor: Colors.transparent,
                      dropdownColor: Colors.white,
                      initialValue: selectedYear,
                      decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5))),
                      items: List.generate(
                        yearList.length,
                        (index) {
                          return DropdownMenuItem(
                            value: yearList[index],
                            child: Text(yearList[index].toString()),
                          );
                        },
                      ),
                      onChanged: (value) async {
                        setState(() {
                          selectedYear = value;
                        });
                        await applyPrevMonthBalances();
                        stateManager.removeAllRows();
                        stateManager.appendRows(rows);
                      },
                    )),
                  ],
                ),
                Spacer(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    resetingData
                        ? Center(
                            child: CircularProgressIndicator(
                              color: Colors.black,
                            ),
                          )
                        : IconButton(
                            iconSize: 35,
                            color: selectedoffice == 'ALL'
                                ? Colors.grey
                                : Colors.black87,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onPressed: () {
                              if (rows.isEmpty) {
                                showCtcAppSnackBar(context, "No data to Reset");
                              } else {
                                if (selectedoffice == 'ALL') {
                                  return;
                                }
                                showDialog(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    backgroundColor: Colors.white,
                                    title: const Text("Reset?"),
                                    content: const Text(
                                        "Are you sure you want to Reset?"),
                                    actions: [
                                      TextButton(
                                          onPressed: () {
                                            onReset();
                                          },
                                          child: const Text("Yes")),
                                      TextButton(
                                          onPressed: () async {
                                            if (context.mounted) {
                                              context.pop();
                                            }
                                          },
                                          child: const Text("No")),
                                    ],
                                  ),
                                );
                              }
                            },
                            icon: Icon(Icons.refresh)),
                    SizedBox(width: 10),
                    mailSent
                        ? Center(
                            child: CircularProgressIndicator(
                              color: Colors.black,
                            ),
                          )
                        : IconButton(
                            iconSize: 35,
                            color: selectedoffice == 'ALL'
                                ? Colors.grey
                                : Colors.black87,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onPressed: () {
                              onSend(ctrl);
                            },
                            icon: Icon(Icons.send)),
                    SizedBox(width: 10),
                    savingData
                        ? Center(
                            child: CircularProgressIndicator(
                              color: Colors.black,
                            ),
                          )
                        : SizedBox(
                            height: 45,
                            width: 90,
                            child: ElevatedButton(
                                style: ButtonStyle(
                                  overlayColor: WidgetStatePropertyAll(
                                      Colors.transparent),
                                  backgroundColor: WidgetStateProperty.all(
                                      selectedoffice == 'ALL'
                                          ? Colors.grey
                                          : Colors.black87),
                                  elevation: WidgetStateProperty.all(0),
                                  foregroundColor:
                                      WidgetStateProperty.all(Colors.white),
                                  shape: WidgetStateProperty.all(
                                      RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(4))),
                                ),
                                onPressed: selectedoffice == 'ALL'
                                    ? () {}
                                    : () async {
                                        onSave(ctrl);
                                      },
                                child: Text(
                                  "SAVE",
                                  style: TextStyle(fontSize: 16),
                                ))),
                    SizedBox(width: 10),
                    downloadingData
                        ? Center(
                            child: CircularProgressIndicator(
                              color: Colors.black,
                            ),
                          )
                        : IconButton(
                            iconSize: 35,
                            color: Colors.black,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onPressed: () {
                              onDownload(ctrl);
                            },
                            icon: Icon(Icons.download))
                  ],
                ),
              ],
            ),
            const SizedBox(height: 40),
            Container(
              decoration:
                  BoxDecoration(border: Border.all(color: Colors.transparent)),
              height: size.height - 138,
              child: PlutoGrid(
                columns: columns,
                rows: rows,
                onLoaded: (PlutoGridOnLoadedEvent event) {
                  stateManager = event.stateManager;
                  stateManager.setShowColumnFilter(true);
                  stateManager.notifyListenersOnPostFrame();
                },
                onChanged: (PlutoGridOnChangedEvent event) {
                  if (event.row.cells['name']?.value == 'TOTAL') {
                    event.row.cells[event.column.field]?.value = event.oldValue;
                    stateManager.notifyListeners();
                    return;
                  }
                  recipetCalc(event);
                  // final newValueee = event.value;
                  // print("Cell changed : New Value: $newValueee");
                  // print(event);
                },
                configuration: const PlutoGridConfiguration(
                    scrollbar: PlutoGridScrollbarConfig(
                        draggableScrollbar: true,
                        isAlwaysShown: true,
                        scrollbarThickness:
                            PlutoScrollbar.defaultThicknessWhileDragging)),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> onDownload(HomeCtrl ctrl) async {
    try {
      final doName = ctrl.districtoffice
              .firstWhereOrNull((element) => element.docId == selectedoffice)
              ?.name ??
          'ALL';

      if (rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to download");
        setState(() {
          downloadingData = false;
        });
        return;
      }
      if (selectedoffice == null) {
        showCtcAppSnackBar(context, "Select DO");
        setState(() {
          downloadingData = false;
        });
        return;
      }

      setState(() {
        downloadingData = true;
      });

      final headers = [
        'SR. NO',
        'CPF NO.',
        'NAME',
        'District Office',
        'OB LT',
        'OB ST',
        'LOAN PAID LT',
        'LOAN PAID ST',
        'LOAN TOTAL',
        'SUB',
        'LT INSTALLMENT',
        'ST INSTALLMENT',
        'INTEREST',
        'DUES',
        'PENALTY',
        'TOTAL',
        'LT CLOSING BALANCE',
        'ST CLOSING BALANCE',
        'INST. RECEIVED',
        'INST. RECEIVED DATE',
        'SUBSCRIPTION PAID',
        'LT INTEREST PAID',
        'ST INTEREST PAID',
        'LT INSTALLMENT PAID',
        'ST INSTALLMENT PAID',
        'PENALTY PAID',
        'Status',
      ];

      List<List<dynamic>> csvData = [
        headers,
      ];

      for (final row in rows) {
        csvData.add([
          row.cells['sr_no']?.value ?? '',
          row.cells['cpf_no']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['district_office']?.value ?? '',
          row.cells['ob_lt']?.value ?? '',
          row.cells['ob_st']?.value ?? '',
          row.cells['loan_paid_lt']?.value ?? '',
          row.cells['loan_paid_st']?.value ?? '',
          row.cells['loan_total']?.value ?? '',
          row.cells['sub']?.value ?? '',
          row.cells['lt_installment']?.value ?? '',
          row.cells['st_installment']?.value ?? '',
          row.cells['interest']?.value ?? '',
          row.cells['dues']?.value ?? '',
          row.cells['penalty']?.value ?? '',
          row.cells['total']?.value ?? '',
          row.cells['lt_closing_balance']?.value ?? '',
          row.cells['st_closing_balance']?.value ?? '',
          row.cells['installment_received']?.value ?? '',
          row.cells['installmentRecDate']?.value ?? '',
          row.cells['subscriptionPaid']?.value ?? '',
          row.cells['longTermInterestPaid']?.value ?? '',
          row.cells['shortTermInterestPaid']?.value ?? '',
          row.cells['longTermInstalmentPaid']?.value ?? '',
          row.cells['shortTermInstalmentPaid']?.value ?? '',
          row.cells['penaltyPaid']?.value ?? '',
          row.cells['status']?.value ?? '',
        ]);
      }

      String csv = const ListToCsvConverter().convert(csvData);

      String name = selectedoffice == 'ALL'
          ? 'Recovery_ALL_${selectedMonth ?? ""}_${selectedYear ?? ""}.csv'
          : 'Recovery_${doName}_${selectedMonth ?? ""}_${selectedYear ?? ""}.csv';

      await FileSaver.instance.saveFile(
        name: name,
        bytes: Uint8List.fromList(csv.codeUnits),
        // ext: "csv",
        mimeType: MimeType.text,
      );

      showCtcAppSnackBar(context, "CSV Downloaded");
      setState(() {
        downloadingData = false;
      });
    } catch (e, stack) {
      showCtcAppSnackBar(context, "Failed to download CSV: $e");
      debugPrint("Error in onDownload: $e");
      debugPrint(stack.toString());
      setState(() {
        downloadingData = false;
      });
    } finally {
      setState(() {
        downloadingData = false;
      });
    }
  }

  Future onSave(HomeCtrl ctrl) async {
    if (rows.isEmpty) return showCtcAppSnackBar(context, "No data to Save");

    if (selectedoffice == 'ALL') {
      return;
    }

    if (selectedoffice != null) {
      try {
        savingData = true;
        setState(() {});

        double totalObLt = 0;
        double totalObSt = 0;
        double totalLoanPaidLt = 0;
        double totalLoanPaidSt = 0;
        double totalLoanTotal = 0;
        double totalSubs = 0;
        double totalInstallmentRec = 0;
        double totalInterest = 0;
        double totalReceived = 0;
        double totalLtInstallment = 0;
        double totalStInstallment = 0;
        double totalLtCb = 0;
        double totalStCb = 0;
        double totalSubscriptionPaid = 0;
        double totalLongTermInstalmentPaid = 0;
        double totalShortTermInstalmentPaid = 0;
        double totalLongTermInterestPaid = 0;
        double totalShortTermInterestPaid = 0;

        for (var row in rows) {
          if (row.cells['name']?.value == 'TOTAL') continue;
          totalObLt += row.cells['ob_lt']?.value ?? 0;
          totalObSt += row.cells['ob_st']?.value ?? 0;
          totalLoanPaidLt += row.cells['loan_paid_lt']?.value ?? 0;
          totalLoanPaidSt += row.cells['loan_paid_st']?.value ?? 0;
          totalLoanTotal += row.cells['loan_total']?.value ?? 0;
          totalSubs += row.cells['sub']?.value ?? 0;
          totalInstallmentRec += row.cells['installment_received']?.value ?? 0;
          totalInterest += row.cells['interest']?.value ?? 0;
          totalReceived += row.cells['totalReceived']?.value ?? 0;
          totalLtInstallment += row.cells['lt_installment']?.value ?? 0;
          totalStInstallment += row.cells['st_installment']?.value ?? 0;
          totalLtCb += row.cells['lt_closing_balance']?.value ?? 0;
          totalStCb += row.cells['st_closing_balance']?.value ?? 0;
          totalSubscriptionPaid += row.cells['subscriptionPaid']?.value ?? 0;
          totalLongTermInstalmentPaid +=
              row.cells['longTermInstalmentPaid']?.value ?? 0;
          totalShortTermInstalmentPaid +=
              row.cells['shortTermInstalmentPaid']?.value ?? 0;
          totalLongTermInterestPaid +=
              row.cells['longTermInterestPaid']?.value ?? 0;
          totalShortTermInterestPaid +=
              row.cells['shortTermInterestPaid']?.value ?? 0;
        }

        fbRecoveryMonthlyDataFetch = await FBFireStore.recoverymonthly
            .where('selectedyear', isEqualTo: selectedYear)
            .where('selectedmonth', isEqualTo: selectedMonth)
            .where('doId', isEqualTo: selectedoffice)
            .get();

        if (selectedYear != null) {
          fbSocietyYearlyDataFetch = await FBFireStore.societyYearly
              .where('selectedyear', isEqualTo: selectedYear)
              .limit(1)
              .get();
        } else {
          return;
        }

        final fbUsersMonthlyDataFetch = await FBFireStore.usermonthly
            .where('selectedyear', isEqualTo: selectedYear)
            .where('selectedmonth', isEqualTo: selectedMonth)
            .where('districtoffice', isEqualTo: selectedoffice)
            .get();

        String? dateString = rows.first.cells['installmentRecDate']?.value;

        DateTime? date = dateString!.isEmpty
            ? null
            : DateFormat("yyyy-MM-dd").parse(dateString);

        if (fbSocietyYearlyDataFetch != null) {
          fbsyDataList = fbSocietyYearlyDataFetch!.docs
              .map((e) => SocietyYearlyRecordModel.fromSnap(e))
              .toList();
        }

        final batch1 = FBFireStore.fb.batch();

        if (fbSocietyYearlyDataFetch?.docs.isEmpty ?? true) {
          batch1.set(FBFireStore.societyYearly.doc(), {
            // 'doId': selectedoffice,
            'createdAt': Timestamp.now(),
            'selectedyear': selectedYear,
            'updatedAt': null,
            'OB': totalObLt.round() + totalObSt.round(),
            'CB': totalStCb.round() + totalLtCb.round(),
            'totalSubscription': totalSubs.round(),
            'intOnSubscription': totalInterest.round(),
            'subscriptionInterestRate':
                num.tryParse(ctrl.settings?.subscriptionInterest ?? "0"),
            'totalLoanGiven': totalLoanPaidLt.round() + totalLoanPaidSt.round(),
            'totalLoanReceived': totalLoanTotal.round(),
            'ltLoanReceived': totalLtInstallment.round(),
            'stLoanReceived': totalStInstallment.round(),
            'ltLoanGiven': totalLongTermInstalmentPaid.round(),
            'stLoanGiven': totalShortTermInstalmentPaid.round(),
            'ltIntAmt': totalLongTermInterestPaid.round(),
            'stIntAmt': totalShortTermInterestPaid.round(),
            'totalIntAmt': totalInterest.round(),
            'loanIntRate':
                num.tryParse(ctrl.settings!.ltloanInterest.toString()),
            'totalPendingLoan': totalLoanTotal.round() -
                totalLoanPaidLt.round() -
                totalLoanPaidSt.round(),
            'ltPendingLoan': totalObLt.round() - totalLtInstallment.round(),
            'stPendingLoan': totalStCb.round() - totalStInstallment.round(),
            'totalExpenses': 0,
            'expensesIds': [],
            'totalDividend': (totalSubs.round() *
                    (num.tryParse(
                            ctrl.settings?.dividentRate.toString() ?? "0") ??
                        0)) /
                100,
            'totalMonthlyShareGiven': totalSubscriptionPaid.round(),
            'totalShareGiven': totalSubscriptionPaid.round(),
            'monthlyDividend': (totalSubs.round() *
                    (num.tryParse(ctrl.settings?.dividentRate ?? "0") ?? 0)) /
                100,
            'dividendRate': num.tryParse(ctrl.settings?.dividentRate ?? "0"),
            'cashBalance': num.tryParse(0.toString()),
          });
        }

        //creating RecoveryMonthly

        if (fbRecoveryMonthlyDataFetch?.size == 0) {
          final rdocRef = FBFireStore.recoverymonthly.doc();
          batch1.set(rdocRef, {
            'obLt': totalObLt.round(),
            'obSt': totalObSt.round(),
            'loanPaidLt': totalLoanPaidLt.round(),
            'loanPaidst': totalLoanPaidSt.round(),
            'loanTotal': totalLoanTotal.round(),
            'subs': totalSubs.round(),
            'ltInstallment': totalLtInstallment.round(),
            'stInstallment': totalStInstallment.round(),
            'interest': totalInterest.round(),
            'total': totalReceived.round(),
            'installmentRec': totalInstallmentRec.round(),
            'installmentRecDate': date,
            'ltCb': totalLtCb.round(),
            'stCb': totalStCb.round(),
            'subscriptionPaid': totalSubscriptionPaid.round(),
            'longTermInstalmentPaid': totalLongTermInstalmentPaid.round(),
            'shortTermInstalmentPaid': totalShortTermInstalmentPaid.round(),
            'longTermInterestPaid': totalLongTermInterestPaid.round(),
            'shortTermInterestPaid': totalShortTermInterestPaid.round(),
            'totalReceived': totalReceived.round(),
            'selectedyear': selectedYear,
            'selectedmonth': selectedMonth,
            'doId': selectedoffice,
          });

          //creating UserMonthly

          if (fbUsersMonthlyDataFetch.size == 0) {
            for (var row in rows) {
              if (row.cells['name']?.value == 'TOTAL') continue;
              final docRef = FBFireStore.usermonthly.doc();
              batch1.set(docRef, {
                'selectedyear': selectedYear,
                'selectedmonth': selectedMonth,
                'districtoffice': selectedoffice,
                'cpfNo': row.cells['cpf_no']?.value,
                'name': row.cells['name']?.value,
                'obLt': row.cells['ob_lt']?.value.round(),
                'obSt': row.cells['ob_st']?.value.round(),
                'loanPaidLt': row.cells['loan_paid_lt']?.value.round(),
                'loanPaidst': row.cells['loan_paid_st']?.value.round(),
                'loanTotal': row.cells['loan_total']?.value.round(),
                'subs': num.tryParse(
                        row.cells['sub']?.value.round().toString() ?? '') ??
                    0,
                'ltInstallment': row.cells['lt_installment']?.value.round(),
                'stInstallment': row.cells['st_installment']?.value.round(),
                'interest': row.cells['interest']?.value.round(),
                'total': row.cells['total']?.value.round(),
                'installmentRec':
                    row.cells['installment_received']?.value.round(),
                'installmentRecDate': date,
                'ltCb': row.cells['lt_closing_balance']?.value.round(),
                'stCb': row.cells['st_closing_balance']?.value.round(),
                'subscriptionPaid':
                    row.cells['subscriptionPaid']?.value.round(),
                'longTermInstalmentPaid':
                    row.cells['longTermInstalmentPaid']?.value.round(),
                'shortTermInstalmentPaid':
                    row.cells['shortTermInstalmentPaid']?.value.round(),
                'longTermInterestPaid':
                    row.cells['longTermInterestPaid']?.value.round(),
                'shortTermInterestPaid':
                    row.cells['shortTermInterestPaid']?.value.round(),
                'penaltyPaid': row.cells['penaltyPaid']?.value.round(),
                'totalReceived': row.cells['totalReceived']?.value.round(),
                'isPaid': false,
                'status': ((row.cells['total']?.value).round() ==
                        (row.cells['installment_received']?.value).round())
                    ? 'Paid'
                    : ((row.cells['installment_received']?.value).round() > 0
                        ? 'Partially Paid'
                        : 'Unpaid'),
                'societySubsPayout': null,
                'societySharesPayout': null,
              });

              // }
            }
          }

          if (fbSocietyYearlyDataFetch?.docs.isEmpty ?? true) {
            batch1.set(FBFireStore.societyYearly.doc(), {
              // 'doId': selectedoffice,
              'createdAt': Timestamp.now(),
              'selectedyear': selectedYear,
              'updatedAt': null,
              'OB': totalObLt.round() + totalObSt.round(),
              'CB': totalStCb.round() + totalLtCb.round(),
              'totalSubscription': totalSubs.round(),
              'intOnSubscription': totalInterest.round(),
              'subscriptionInterestRate':
                  num.tryParse(ctrl.settings?.subscriptionInterest ?? "0"),
              'totalLoanGiven':
                  totalLoanPaidLt.round() + totalLoanPaidSt.round(),
              'totalLoanReceived': totalLoanTotal.round(),
              'ltLoanReceived': totalLtInstallment.round(),
              'stLoanReceived': totalStInstallment.round(),
              'ltLoanGiven': totalLongTermInstalmentPaid.round(),
              'stLoanGiven': totalShortTermInstalmentPaid.round(),
              'ltIntAmt': totalLongTermInterestPaid.round(),
              'stIntAmt': totalShortTermInterestPaid.round(),
              'totalIntAmt': totalInterest.round(),
              'loanIntRate':
                  num.tryParse(ctrl.settings!.ltloanInterest.toString()),
              'totalPendingLoan': totalLoanTotal.round() -
                  totalLoanPaidLt.round() -
                  totalLoanPaidSt.round(),
              'ltPendingLoan': totalObLt.round() - totalLtInstallment.round(),
              'stPendingLoan': totalStCb.round() - totalStInstallment.round(),
              'totalExpenses': 0,
              'expensesIds': [],
              'totalDividend': totalSubs.round() *
                  (num.tryParse(
                          ctrl.settings?.dividentRate.toString() ?? "0") ??
                      0),
              'totalMonthlyShareGiven': totalSubscriptionPaid.round(),
              'totalShareGiven': totalSubscriptionPaid.round(),
              'monthlyDividend': totalSubs.round() *
                  (num.tryParse(ctrl.settings?.dividentRate ?? "0") ?? 0),
              'dividendRate': num.tryParse(ctrl.settings?.dividentRate ?? "0"),
              'cashBalance': num.tryParse(0.toString()),
            });
          }
        } else if ((fbRecoveryMonthlyDataFetch?.size ?? 0) > 0) {
          setState(() {
            savingData = true;
          });

          if (fbRecoveryMonthlyDataFetch?.docs.isEmpty ?? true) {
            batch1.set(FBFireStore.recoverymonthly.doc(), {
              'obLt': totalObLt.round(),
              'obSt': totalObSt.round(),
              'loanPaidLt': totalLoanPaidLt.round(),
              'loanPaidst': totalLoanPaidSt.round(),
              'loanTotal': totalLoanTotal.round(),
              'subs': totalSubs.round(),
              'ltInstallment': totalLtInstallment.round(),
              'stInstallment': totalStInstallment.round(),
              'interest': totalInterest.round(),
              'total': totalReceived.round(),
              'installmentRec': totalInstallmentRec.round(),
              'installmentRecDate': date,
              'ltCb': totalLtCb.round(),
              'stCb': totalStCb.round(),
              'subscriptionPaid': totalSubscriptionPaid.round(),
              'longTermInstalmentPaid': totalLongTermInstalmentPaid.round(),
              'shortTermInstalmentPaid': totalShortTermInstalmentPaid.round(),
              'longTermInterestPaid': totalLongTermInterestPaid.round(),
              'shortTermInterestPaid': totalShortTermInterestPaid.round(),
              'totalReceived': totalReceived.round(),
              'selectedyear': selectedYear,
              'selectedmonth': selectedMonth,
              'doId': selectedoffice,
            });
          } else {
            batch1.update(
                FBFireStore.recoverymonthly
                    .doc(fbRecoveryMonthlyDataFetch?.docs.first.id),
                {
                  'obLt': totalObLt.round(),
                  'obSt': totalObSt.round(),
                  'loanPaidLt': totalLoanPaidLt.round(),
                  'loanPaidst': totalLoanPaidSt.round(),
                  'loanTotal': totalLoanTotal.round(),
                  'subs': totalSubs.round(),
                  'ltInstallment': totalLtInstallment.round(),
                  'stInstallment': totalStInstallment.round(),
                  'interest': totalInterest.round(),
                  'total': totalReceived.round(),
                  'installmentRec': totalInstallmentRec.round(),
                  'installmentRecDate': date,
                  'ltCb': totalLtCb.round(),
                  'stCb': totalStCb.round(),
                  'subscriptionPaid': totalSubscriptionPaid.round(),
                  'longTermInstalmentPaid': totalLongTermInstalmentPaid.round(),
                  'shortTermInstalmentPaid':
                      totalShortTermInstalmentPaid.round(),
                  'longTermInterestPaid': totalLongTermInterestPaid.round(),
                  'shortTermInterestPaid': totalShortTermInterestPaid.round(),
                  'totalReceived': totalReceived.round(),
                  'selectedyear': selectedYear,
                  'selectedmonth': selectedMonth,
                  'doId': selectedoffice,
                });
          }
        }

        if (fbSocietyYearlyDataFetch?.docs.isNotEmpty ?? true) {
          setState(() {
            savingData = true;
          });

          final docSnapshot = await FBFireStore.societyYearly
              .doc(fbSocietyYearlyDataFetch!.docs.first.id)
              .get();

          final existingData = docSnapshot.data() as Map<String, dynamic>;

          // print("total Subs : $totalSubs");

          // print("yearly data : ${fbSocietyYearlyDataFetch?.docs.first.data()}");

          print("existing Data : $existingData");

          batch1.update(
              FBFireStore.societyYearly
                  .doc(fbSocietyYearlyDataFetch!.docs.first.id),
              {
                'updatedAt': Timestamp.now(),
                'OB':
                    existingData['OB'] + totalObLt.round() + totalObSt.round(),
                'CB':
                    existingData['CB'] + totalStCb.round() + totalLtCb.round(),
                'totalSubscription': existingData['totalSubscription'] +
                    totalSubscriptionPaid.round(),
                'intOnSubscription': existingData['intOnSubscription'],
                'subscriptionInterestRate':
                    num.tryParse(ctrl.settings?.subscriptionInterest ?? "0"),
                'totalLoanGiven': existingData['totalLoanGiven'] +
                    totalLoanPaidLt.round() +
                    totalLoanPaidSt.round(),
                'totalLoanReceived':
                    existingData['totalLoanReceived'] + totalLoanTotal.round(),
                'ltLoanReceived':
                    existingData['ltLoanReceived'] + totalLtInstallment.round(),
                'stLoanReceived':
                    existingData['stLoanReceived'] + totalStInstallment.round(),
                'ltLoanGiven': existingData['ltLoanGiven'] +
                    totalLongTermInstalmentPaid.round(),
                'stLoanGiven': existingData['stLoanGiven'] +
                    totalShortTermInstalmentPaid.round(),
                'ltIntAmt': existingData['ltIntAmt'] +
                    totalLongTermInterestPaid.round(),
                'stIntAmt': existingData['stIntAmt'] +
                    totalShortTermInterestPaid.round(),
                'totalIntAmt':
                    existingData['totalIntAmt'] + totalInterest.round(),
                'loanIntRate':
                    num.tryParse(ctrl.settings!.ltloanInterest.toString()),
                'totalPendingLoan': existingData['totalPendingLoan'] +
                    (totalLoanTotal.round() -
                        totalLoanPaidLt.round() -
                        totalLoanPaidSt.round()),
                'ltPendingLoan': existingData['ltPendingLoan'] +
                    (totalObLt.round() - totalLtInstallment.round()),
                'stPendingLoan': existingData['stPendingLoan'] +
                    (totalStCb.round() - totalStInstallment.round()),
                'totalExpenses': existingData['totalExpenses'] + 0,
                'expensesIds': existingData['expensesIds'] ?? [],
                'totalDividend': existingData['totalDividend'] +
                    (totalSubs.round() *
                        (num.tryParse(ctrl.settings?.dividentRate.toString() ??
                                "0") ??
                            0)),
                'totalMonthlyShareGiven':
                    existingData['totalMonthlyShareGiven'] +
                        totalSubscriptionPaid.round(),
                'totalShareGiven': existingData['totalShareGiven'] +
                    totalSubscriptionPaid.round(),
                'monthlyDividend': existingData['monthlyDividend'] +
                    (totalSubs.round() *
                        (num.tryParse(ctrl.settings?.dividentRate ?? "0") ??
                            0)),
                'cashBalance': existingData['cashBalance'] + 0,
              });
        }

        if (fbUserMonthlyDataFetch!.docs.isNotEmpty) {
          setState(() {
            savingData = true;
          });

          // final rId = (fbRecoveryMonthlyDataFetch?.docs.isNotEmpty ?? false)
          //     ? fbRecoveryMonthlyDataFetch?.docs.first.id
          //     : null;

          for (var row in rows) {
            if (row.cells['name']?.value == 'TOTAL') continue;
            final cpfDocId = fbUserMonthlyDataFetch?.docs.firstWhereOrNull(
                (element) =>
                    element.data()['cpfNo'] == row.cells['cpf_no']?.value);

            num total =
                num.tryParse(row.cells['total']?.value.toString() ?? "") ?? 0;

            num installmentRec = num.tryParse(
                    row.cells['installment_received']?.value.toString() ??
                        "") ??
                0;

            fbUserDataFetch = await FBFireStore.users
                .where("approved", isEqualTo: true)
                .where("districtoffice", isEqualTo: selectedoffice)
                .get();

            final userdocId = hctrl.users.firstWhereOrNull(
              (element) =>
                  element.cpfNo ==
                  num.tryParse(row.cells['cpf_no']?.value.toString() ?? ""),
            );

            if (userdocId != null) {
              final userDocSnapshot =
                  await FBFireStore.users.doc(userdocId.docId).get();
              num currentTotalSubs =
                  (userDocSnapshot.data()?['totalSubs'] ?? 0) as num;

              num newSub =
                  num.tryParse(row.cells['sub']?.value.toString() ?? "0") ?? 0;

              num updatedTotalSubs = currentTotalSubs + newSub;

              batch1.update(
                FBFireStore.users.doc(userdocId.docId),
                {
                  "totalSubs": updatedTotalSubs,
                },
              );
            }

            num userDues = total - installmentRec;
            num userPenalty = 0;

            if (userDues > 0) {
              final ltPaid = num.tryParse(
                      row.cells['longTermInterestPaid']?.value.toString() ??
                          "0") ??
                  0;
              final stPaid = num.tryParse(
                      row.cells['shortTermInterestPaid']?.value.toString() ??
                          "0") ??
                  0;

              final ltInterestRate = num.tryParse(
                      ctrl.settings?.ltloanInterest.toString() ?? "0") ??
                  0;
              final stInterestRate = num.tryParse(
                      ctrl.settings?.stloanInterest.toString() ?? "0") ??
                  0;

              final obLt =
                  num.tryParse(row.cells['ob_lt']?.value.toString() ?? "0") ??
                      0;
              final obSt =
                  num.tryParse(row.cells['ob_st']?.value.toString() ?? "0") ??
                      0;

              final expectedLtInterest = (obLt * ltInterestRate) / 1200;
              final expectedStInterest = (obSt * stInterestRate) / 1200;

              final unpaidLt = (ltPaid < expectedLtInterest)
                  ? (expectedLtInterest - ltPaid)
                  : 0;
              final unpaidSt = (stPaid < expectedStInterest)
                  ? (expectedStInterest - stPaid)
                  : 0;

              userPenalty = (unpaidLt.round() * ltInterestRate / 1200) +
                  (unpaidSt.round() * stInterestRate / 1200);
            }

            if (cpfDocId != null) {
              Map<String, dynamic> updateData = {
                'selectedyear': selectedYear,
                'selectedmonth': selectedMonth,
                'districtoffice': selectedoffice,
                'cpfNo':
                    num.tryParse(row.cells['cpf_no']?.value.toString() ?? ""),
                'name': row.cells['name']?.value,
                'obLt':
                    num.tryParse(row.cells['ob_lt']?.value.toString() ?? ""),
                'obSt':
                    num.tryParse(row.cells['ob_st']?.value.toString() ?? ""),
                'loanPaidLt': num.tryParse(
                    row.cells['loan_paid_lt']?.value.toString() ?? ""),
                'loanPaidst': num.tryParse(
                    row.cells['loan_paid_st']?.value.toString() ?? ""),
                'loanTotal': num.tryParse(
                    row.cells['loan_total']?.value.toString() ?? ""),
                'subs': num.tryParse(row.cells['sub']!.value.toString()),
                'ltInstallment': num.tryParse(
                    row.cells['lt_installment']?.value.toString() ?? ""),
                'stInstallment': num.tryParse(
                    row.cells['st_installment']?.value.toString() ?? ""),
                'interest':
                    num.tryParse(row.cells['interest']?.value.toString() ?? ""),
                'total':
                    num.tryParse(row.cells['total']?.value.toString() ?? ""),
                'installmentRec': num.tryParse(
                        row.cells['installment_received']?.value.toString() ??
                            "") ??
                    0,
                'installmentRecDate': date,
                'ltCb': num.tryParse(
                    row.cells['lt_closing_balance']?.value.toString() ?? ""),
                'stCb': num.tryParse(
                    row.cells['st_closing_balance']?.value.toString() ?? ""),
                'subscriptionPaid': num.tryParse(
                    row.cells['subscriptionPaid']?.value.toString() ?? ""),
                'longTermInstalmentPaid': num.tryParse(
                    row.cells['longTermInstalmentPaid']?.value.toString() ??
                        ""),
                'shortTermInstalmentPaid': num.tryParse(
                    row.cells['shortTermInstalmentPaid']?.value.toString() ??
                        ""),
                'longTermInterestPaid': num.tryParse(
                    row.cells['longTermInterestPaid']?.value.toString() ?? ""),
                'shortTermInterestPaid': num.tryParse(
                    row.cells['shortTermInterestPaid']?.value.toString() ?? ""),
                'penaltyPaid': num.tryParse(
                    row.cells['penaltyPaid']?.value.toString() ?? ""),
                'totalReceived': num.tryParse(
                    row.cells['totalReceived']?.value.toString() ?? ""),
                'isPaid': total.round() == installmentRec.round(),
                'status': total.round() == installmentRec.round()
                    ? 'Paid'
                    : (installmentRec.round() > 0
                        ? 'Partially Paid'
                        : 'Unpaid'),
                'societySubsPayout': null,
                'societySharesPayout': null,
              };

              if (total.round() > installmentRec.round()) {
                num userDues = total.round() - installmentRec.round();

                updateData.addAll({
                  'dues': userDues.round(),
                  'penalty': userPenalty.round(),
                });
              } else {
                updateData.addAll({
                  'dues': 0,
                  'penalty': 0,
                });
              }

              // print("cpfDocId.id : ${cpfDocId.id}");
              // print("updateData : $updateData");

              batch1.update(
                  FBFireStore.usermonthly.doc(cpfDocId.id), updateData);

              Map<String, dynamic> userUpdatePreviousMonthlyData = {
                "userPrevoiusMonthlyRecord": {
                  'selectedyear': selectedYear,
                  'selectedmonth': selectedMonth,
                  'districtoffice': selectedoffice,
                  'cpfNo':
                      num.tryParse(row.cells['cpf_no']?.value.toString() ?? ""),
                  'name': row.cells['name']?.value,
                  'obLt':
                      num.tryParse(row.cells['ob_lt']?.value.toString() ?? ""),
                  'obSt':
                      num.tryParse(row.cells['ob_st']?.value.toString() ?? ""),
                  'loanPaidLt': num.tryParse(
                      row.cells['loan_paid_lt']?.value.toString() ?? ""),
                  'loanPaidst': num.tryParse(
                      row.cells['loan_paid_st']?.value.toString() ?? ""),
                  'loanTotal': num.tryParse(
                      row.cells['loan_total']?.value.toString() ?? ""),
                  'subs':
                      num.tryParse(row.cells['sub']!.value.toString())?.round(),
                  'ltInstallment': num.tryParse(
                          row.cells['lt_installment']?.value.toString() ?? "")
                      ?.round(),
                  'stInstallment': num.tryParse(
                          row.cells['st_installment']?.value.toString() ?? "")
                      ?.round(),
                  'interest': num.tryParse(
                          row.cells['interest']?.value.toString() ?? "")
                      ?.round(),
                  'total':
                      num.tryParse(row.cells['total']?.value.toString() ?? "")
                          ?.round(),
                  'installmentRec': num.tryParse(
                          row.cells['installment_received']?.value.toString() ??
                              "")
                      ?.round(),
                  'installmentRecDate':
                      // row.cells['installment_received']?.value != 0 ||
                      //         row.cells['installment_received']?.value != null
                      //     ? date = DateTime.now()
                      //     :
                      date,
                  'ltCb': num.tryParse(
                          row.cells['lt_closing_balance']?.value.toString() ??
                              "")
                      ?.round(),
                  'stCb': num.tryParse(
                          row.cells['st_closing_balance']?.value.toString() ??
                              "")
                      ?.round(),
                  'subscriptionPaid': num.tryParse(
                          row.cells['subscriptionPaid']?.value.toString() ?? "")
                      ?.round(),
                  'longTermInstalmentPaid': num.tryParse(row
                              .cells['longTermInstalmentPaid']?.value
                              .toString() ??
                          "")
                      ?.round(),
                  'shortTermInstalmentPaid': num.tryParse(row
                              .cells['shortTermInstalmentPaid']?.value
                              .toString() ??
                          "")
                      ?.round(),
                  'longTermInterestPaid': num.tryParse(
                          row.cells['longTermInterestPaid']?.value.toString() ??
                              "")
                      ?.round(),
                  'shortTermInterestPaid': num.tryParse(row
                              .cells['shortTermInterestPaid']?.value
                              .toString() ??
                          "")
                      ?.round(),
                  'penaltyPaid': num.tryParse(
                          row.cells['penaltyPaid']?.value.toString() ?? "")
                      ?.round(),
                  'totalReceived': num.tryParse(
                          row.cells['totalReceived']?.value.toString() ?? "")
                      ?.round(),
                  'isPaid': total.round() == installmentRec.round(),
                  'status': total.round() == installmentRec.round()
                      ? 'Paid'
                      : (installmentRec.round() > 0
                          ? 'Partially Paid'
                          : 'Unpaid'),
                }
              };

              if (total.round() > installmentRec.round()) {
                userUpdatePreviousMonthlyData["userPrevoiusMonthlyRecord"]
                    ['dues'] = total.round() - installmentRec.round();
              } else {
                userUpdatePreviousMonthlyData["userPrevoiusMonthlyRecord"]
                    ['dues'] = 0;
              }

              batch1.update(FBFireStore.users.doc(userdocId?.docId),
                  userUpdatePreviousMonthlyData);

              if (selectedMonth == 3 || DateTime.now().month == 3) {
                // March month: Update opening balances for next financial year
                num obSubsPrev = 0;
                num obltPrev = 0;
                num obstPrev = 0;
                num obsharesPrev = 0;

                // Calculate closing balances from current month data
                final currentTotalSubs = (userdocId?.totalSubs ?? 0) +
                    (row.cells['sub']?.value ?? 0);
                final currentTotalShares = userdocId?.totalShares ?? 0;
                final ltClosingBalance = row.cells['lt_cb']?.value ?? 0;
                final stClosingBalance = row.cells['st_cb']?.value ?? 0;

                obSubsPrev =
                    currentTotalSubs; // Current total subs becomes next year's opening
                obltPrev =
                    ltClosingBalance; // Current LT closing becomes next year's opening
                obstPrev =
                    stClosingBalance; // Current ST closing becomes next year's opening
                obsharesPrev =
                    currentTotalShares; // Current total shares becomes next year's opening

                // Update both previous yearly record AND direct UserModel fields
                Map<String, dynamic> userUpdateData = {
                  // Update previous yearly record (existing logic)
                  "userPrevoiusYearlyRecord": {
                    "ob_subscription": obSubsPrev,
                    "ob_lt": obltPrev,
                    "ob_st": obstPrev,
                    "ob_shares": obsharesPrev,
                  },
                  // Update direct UserModel opening balance fields (NEW LOGIC)
                  "obSubs": obSubsPrev,
                  "obShares": obsharesPrev,
                  "obLt": obltPrev,
                  "obSt": obstPrev,
                };

                batch1.update(
                    FBFireStore.users.doc(userdocId?.docId), userUpdateData);

                print(
                    '🔄 March Processing: Updated opening balances for user ${userdocId?.name}');
                print(
                    '   📊 obSubs: $obSubsPrev, obShares: $obsharesPrev, obLt: $obltPrev, obSt: $obstPrev');
              } else {}

              totalSubs +=
                  num.tryParse(row.cells['subs']?.value.toString() ?? "") ?? 0;

              // final totalLoans =
              //     row.cells['ob_lt']?.value + row.cells['ob_st']?.value;

              // final totalRemainingLoan =
              //     row.cells['lt_closing_balance']?.value +
              //         row.cells['st_closing_balance']?.value;

              // final totalMonthlyPaidAmt = totalLoans - totalRemainingLoan;

              final loanSnapshot = await FBFireStore.loan
                  .where("uid", isEqualTo: userdocId?.docId)
                  .get();

              if (loanSnapshot.docs.isNotEmpty) {
                for (var loanDoc in loanSnapshot.docs) {
                  final loanId = loanDoc.id;
                  final loanType = loanDoc.data()['loanType'];

                  if (loanType == LoanTypes.longTerm) {
                    final ltinstallPaid =
                        row.cells['longTermInstalmentPaid']?.value ?? 0;
                    final ltClosing =
                        row.cells['lt_closing_balance']?.value ?? 0;
                    final ltInterestPaid =
                        row.cells['longTermInterestPaid']?.value ?? 0;

                    batch1.update(FBFireStore.loan.doc(loanId), {
                      'totalLoanPaid': ltinstallPaid.round(),
                      'totalLoanDue': ltClosing.round(),
                      'totalInterestPaid': ltInterestPaid.round(),
                    });

                    if (ltClosing.ceil() == 0) {
                      batch1.update(FBFireStore.loan.doc(loanId), {
                        'isSettled': true,
                        'settledOn': DateTime.now(),
                      });

                      // batch1.set(FBFireStore.transactions.doc(), {
                      //   "createdAt": Timestamp.now(),
                      //   "title": "Loan Settled",
                      //   "amount": 0,
                      //   "inn": false,
                      //   "loanId": loanId,
                      //   "uId": userdocId?.docId,
                      // });

                      // // Add Loan Settled Notification
                      // batch1.set(FBFireStore.notifications.doc(), {
                      //   'uId': userdocId?.docId,
                      //   'title': "Loan Settled",
                      //   'desc': "Your short term loan has been settled.",
                      //   'type': "loan_settled",
                      //   'districtOffice': userdocId?.districtoffice,
                      //   'createdAt': Timestamp.now(),
                      // });
                    }
                  } else if (loanType == LoanTypes.emergencyLoan) {
                    final stinstallPaid =
                        row.cells['shortTermInstalmentPaid']?.value ?? 0;
                    final stClosing =
                        row.cells['st_closing_balance']?.value ?? 0;
                    final stInterestPaid =
                        row.cells['shortTermInterestPaid']?.value ?? 0;

                    batch1.update(FBFireStore.loan.doc(loanId), {
                      'totalLoanPaid': stinstallPaid.round(),
                      'totalLoanDue': stClosing.round(),
                      'totalInterestPaid': stInterestPaid.round(),
                    });

                    if (stClosing.ceil() == 0) {
                      batch1.update(FBFireStore.loan.doc(loanId), {
                        'isSettled': true,
                        'settledOn': DateTime.now(),
                      });

                      // batch1.set(FBFireStore.transactions.doc(), {
                      //   "createdAt": Timestamp.now(),
                      //   "title": "Loan Settled",
                      //   "amount": 0,
                      //   "inn": false,
                      //   "loanId": loanId,
                      //   "uId": userdocId?.docId,
                      // });

                      // // Add Loan Settled Notification
                      // batch1.set(FBFireStore.notifications.doc(), {
                      //   'uId': userdocId?.docId,
                      //   'title': "Loan Settled",
                      //   'desc': "Your long term loan has been settled.",
                      //   'type': "loan_settled",
                      //   'districtOffice': userdocId?.districtoffice,
                      //   'createdAt': Timestamp.now(),
                      // });
                    }
                  }

                  // num ltinstallPaid = 0;
                  // num stinstallPaid = 0;

                  // ltinstallPaid += row.cells['longTermInstalmentPaid']?.value;
                  // stinstallPaid += row.cells['shortTermInstalmentPaid']?.value;

                  // batch1.update(FBFireStore.loan.doc(loanId), {
                  //   'totalLoanPaid':
                  //       ltinstallPaid.round() + stinstallPaid.round(),
                  //   'totalLoanDue':
                  //       row.cells['lt_closing_balance']?.value.round() +
                  //           row.cells['st_closing_balance']?.value.round(),
                  //   'totalInterestPaid':
                  //       row.cells['longTermInterestPaid']?.value.round() +
                  //           row.cells['shortTermInterestPaid']?.value.round(),
                  // });

                  // batch1.update(FBFireStore.users.doc(userdocId?.docId), {
                  //   "ltLoansDue": totalObLt.round() - ltinstallPaid.round(),
                  //   "stLoansDue": totalObSt.round() - stinstallPaid.round(),
                  //   "totalLtLoans": totalObLt.round() - ltinstallPaid.round(),
                  //   "totalStLoans": totalObSt.round() - stinstallPaid.round(),
                  //   "totalSubs": totalSubs.round(),
                  //   "totalLtIntPaid": totalLongTermInterestPaid.round(),
                  //   "totalStIntPaid": totalShortTermInterestPaid.round(),
                  // });
                  batch1.update(FBFireStore.users.doc(userdocId?.docId), {
                    "ltLoansDue": (num.tryParse(
                                row.cells['ob_lt']?.value.toString() ?? "0") ??
                            0) -
                        (num.tryParse(row.cells['longTermInstalmentPaid']?.value
                                    .toString() ??
                                "0") ??
                            0),
                    "stLoansDue": (num.tryParse(
                                row.cells['ob_st']?.value.toString() ?? "0") ??
                            0) -
                        (num.tryParse(row
                                    .cells['shortTermInstalmentPaid']?.value
                                    .toString() ??
                                "0") ??
                            0),
                    "totalLtLoans": (num.tryParse(
                                row.cells['ob_lt']?.value.toString() ?? "0") ??
                            0) -
                        (num.tryParse(row.cells['longTermInstalmentPaid']?.value
                                    .toString() ??
                                "0") ??
                            0),
                    "totalStLoans": (num.tryParse(
                                row.cells['ob_st']?.value.toString() ?? "0") ??
                            0) -
                        (num.tryParse(row
                                    .cells['shortTermInstalmentPaid']?.value
                                    .toString() ??
                                "0") ??
                            0),
                    "totalSubs": (num.tryParse(
                            row.cells['sub']?.value.toString() ?? "0") ??
                        0),
                    "totalLtIntPaid": (num.tryParse(row
                                .cells['longTermInterestPaid']?.value
                                .toString() ??
                            "0") ??
                        0),
                    "totalStIntPaid": (num.tryParse(row
                                .cells['shortTermInterestPaid']?.value
                                .toString() ??
                            "0") ??
                        0),
                  });
                }
              } else {
                // print("No loans found for user: ${userdocId?.docId}");
              }

              // Transactions

              List<Map<String, dynamic>> transactions = [
                {
                  "title": "Subscription",
                  "amount": num.tryParse(
                      row.cells['subscriptionPaid']?.value.toString() ?? "0"),
                },
                {
                  "title": "Long Term Instalment",
                  "amount": num.tryParse(
                      row.cells['longTermInstalmentPaid']?.value.toString() ??
                          "0"),
                },
                {
                  "title": "Short Term Instalment",
                  "amount": num.tryParse(
                      row.cells['shortTermInstalmentPaid']?.value.toString() ??
                          "0"),
                },
                {
                  "title": "Long Term Interest",
                  "amount": num.tryParse(
                          row.cells['longTermInterestPaid']?.value.toString() ??
                              "0")
                      ?.round(),
                },
                {
                  "title": "Short Term Interest",
                  "amount": num.tryParse(row
                              .cells['shortTermInterestPaid']?.value
                              .toString() ??
                          "0")
                      ?.round(),
                }
              ];

              final rId = (fbRecoveryMonthlyDataFetch?.docs.isNotEmpty ?? false)
                  ? fbRecoveryMonthlyDataFetch?.docs.first.id
                  : null;

              final uId = hctrl.users.firstWhereOrNull(
                (element) =>
                    element.cpfNo ==
                    num.tryParse(row.cells['cpf_no']?.value.toString() ?? ""),
              );

              if (userDues.round() + userPenalty.round() > 0) {
                batch1.set(FBFireStore.transactions.doc(), {
                  "createdAt": Timestamp.fromDate(DateTime.now()),
                  "title": "Total Dues",
                  "amount": userDues + userPenalty,
                  "inn": false,
                  "userMonthlyId": cpfDocId.id,
                  "uId": userdocId?.docId,
                  if (rId != null) "recoveryId": rId,
                });
              }
              if (userPenalty.round() > 0) {
                batch1.set(FBFireStore.transactions.doc(), {
                  "createdAt": Timestamp.fromDate(
                      DateTime.now().add(Duration(seconds: 15))),
                  "title": "Penalty",
                  "amount": userPenalty,
                  "inn": false,
                  "userMonthlyId": cpfDocId.id,
                  "uId": userdocId?.docId,
                  if (rId != null) "recoveryId": rId,
                });
              }
              if (userPenalty.round() + userDues.round() > 0) {
                batch1.set(FBFireStore.notifications.doc(), {
                  'uId': uId?.docId,
                  'title': "Dues",
                  'desc': "₹${userDues.round() + userPenalty.round()}",
                  'type': "dues",
                  'districtOffice': uId?.districtoffice,
                  'createdAt': Timestamp.fromDate(DateTime.now()),
                });
              }
              if (userPenalty.round() > 0) {
                batch1.set(FBFireStore.notifications.doc(), {
                  'uId': uId?.docId,
                  'title': "Penalty",
                  'desc': "₹${userPenalty.round()}",
                  'type': "penalty",
                  'districtOffice': uId?.districtoffice,
                  'createdAt': Timestamp.fromDate(
                      DateTime.now().add(Duration(seconds: 10))),
                });
              }

              for (var transaction in transactions) {
                final amount = transaction['amount'];
                if (amount == null || amount == 0) continue;
                if (transaction['amount'] == 0 ||
                    transaction['amount'] == null) {
                  continue;
                }

                final existingTransactionQuery = await FBFireStore.transactions
                    .where('userMonthlyId', isEqualTo: cpfDocId.id)
                    .where('uId', isEqualTo: uId?.docId)
                    .where('title', isEqualTo: transaction['title'])
                    .limit(1)
                    .get();

                if (existingTransactionQuery.docs.isNotEmpty) {
                  final existingDoc = existingTransactionQuery.docs.first;
                  final existingData = existingDoc.data();
                  final existingAmount =
                      num.tryParse(existingData['amount'].toString()) ?? 0;

                  if (existingAmount < amount) {
                    batch1.update(existingDoc.reference, {
                      "amount": num.tryParse(amount.toString()) ?? 0,
                      "updatedAt": Timestamp.now(),
                    });
                  }

                  continue;
                }

                final newTransactionDoc = FBFireStore.transactions.doc();
                final newNotificationDoc = FBFireStore.notifications.doc();

                batch1.set(newTransactionDoc, {
                  "createdAt": Timestamp.now(),
                  "title": "${transaction['title']} Deducted",
                  "amount": num.tryParse(transaction['amount'].toString()) ?? 0,
                  "inn": false,
                  "userMonthlyId": cpfDocId.id,
                  "uId": uId?.docId,
                  if (rId != null) "recoveryId": rId,
                });

                batch1.set(newNotificationDoc, {
                  'uId': uId?.docId,
                  'title': "${transaction['title']} Deducted",
                  'desc': "₹${transaction['amount']}",
                  'type': "global",
                  'districtOffice': uId?.districtoffice,
                  'createdAt': Timestamp.now(),
                });
              }
            }
          }
        }

        await batch1.commit();

        showCtcAppSnackBar(context, "Data Added Successfully");

        setState(() {
          savingData = false;
        });
      } catch (e) {
        setState(() {
          savingData = false;
        });
        // print("Error adding data to Firestore: $e");
        debugPrint("Error adding data to Firestore: ${e.toString()}");
        showCtcAppSnackBar(context, "Error Saving Data");
      }
    } else {
      showCtcAppSnackBar(context, "Select DO");
    }
  }

  Future<void> calcRowsData(bool reset) async {
    fbData.clear();

    if (selectedoffice == 'ALL') {
      await _calcRowsDataAllDO(reset);
    } else {
      await _calcRowsDataForSelectedDO(reset);
    }
    setState(() {});
  }

  Future<void> _calcRowsDataAllDO(bool reset) async {
    // print("Fetching data for all offices");
    fbUserMonthlyDataFetch = await FBFireStore.usermonthly
        .where('selectedyear', isEqualTo: selectedYear)
        .where('selectedmonth', isEqualTo: selectedMonth)
        .get();

    if (fbUserMonthlyDataFetch != null &&
        fbUserMonthlyDataFetch!.docs.isNotEmpty) {
      // print("Fetched users: ${fbUserMonthlyDataFetch!.docs.length}");
      fbData = fbUserMonthlyDataFetch!.docs
          .map((e) => UserMonthlyRecordModel.fromSnap(e))
          .toList();

      rows = List.generate(
        fbData.length,
        (fbindex) {
          final user = fbData[fbindex];
          final officeName = hctrl.districtoffice
              .firstWhereOrNull((e) => e.docId == user.districtoffice)
              ?.name;
          return PlutoRow(cells: {
            'sr_no': PlutoCell(value: "${fbindex + 1}"),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name),
            'district_office': PlutoCell(value: officeName),
            'ob_lt': PlutoCell(value: user.obLt ?? 0),
            'ob_st': PlutoCell(value: user.obSt ?? 0),
            'loan_paid_lt': PlutoCell(value: user.loanPaidLt ?? 0),
            'loan_paid_st': PlutoCell(value: user.loanPaidst ?? 0),
            'loan_total': PlutoCell(
                value: (user.obLt ?? 0) +
                    (user.obSt ?? 0) +
                    (user.loanPaidLt ?? 0) +
                    (user.loanPaidst ?? 0)),
            'sub': PlutoCell(value: hctrl.settings?.defaultSubsinstallAmt ?? 0),
            'lt_installment': PlutoCell(value: user.ltInstallment ?? 0),
            'st_installment': PlutoCell(value: user.stInstallment ?? 0),
            'interest': PlutoCell(
                value: ((user.obLt ?? 0) +
                        (user.obSt ?? 0) +
                        (user.loanPaidLt ?? 0) +
                        (user.loanPaidst ?? 0)) *
                    8.5 /
                    1200),
            'dues': PlutoCell(value: user.dues ?? 0),
            'penalty': PlutoCell(value: user.penalty ?? 0),
            'total': PlutoCell(
                value: (((user.ltInstallment ?? 0) > 0
                        ? user.ltInstallment ?? 0
                        : 0) +
                    ((user.stInstallment ?? 0) > 0
                        ? user.stInstallment ?? 0
                        : 0) +
                    (user.subs ?? 0) +
                    (user.interest ?? 0) +
                    (user.dues ?? 0) +
                    (user.penalty ?? 0))),
            'installment_received': PlutoCell(value: user.installmentRec ?? 0),
            'installmentRecDate': PlutoCell(
              value: (user.installmentRecDate == null)
                  ? null
                  : user.installmentRecDate,
            ),
            'lt_closing_balance': PlutoCell(
                value: (user.obLt ?? 0) +
                    (user.loanPaidLt ?? 0) -
                    (user.ltInstallment ?? 0)),
            'st_closing_balance': PlutoCell(
                value: (user.obSt ?? 0) +
                    (user.loanPaidst ?? 0) -
                    (user.stInstallment ?? 0)),
            'subscriptionPaid': PlutoCell(value: user.subscriptionPaid),
            'longTermInstalmentPaid':
                PlutoCell(value: user.longTermInstalmentPaid),
            'shortTermInstalmentPaid':
                PlutoCell(value: user.shortTermInstalmentPaid),
            'longTermInterestPaid': PlutoCell(value: user.longTermInterestPaid),
            'shortTermInterestPaid':
                PlutoCell(value: user.shortTermInterestPaid),
            'penaltyPaid': PlutoCell(value: user.penaltyPaid),
            // 'totalReceived': PlutoCell(value: user.totalReceived ?? 0),
            'status': PlutoCell(value: user.status ?? "unpaid"),
          });
        },
      );
    } else {
      // print("No Firestore data found, generating rows locally for all users.");
      rows = [
        ...List.generate(hctrl.users.length, (index) {
          final user = hctrl.users[index];
          final officeName = hctrl.districtoffice
              .firstWhereOrNull((e) => e.docId == user.districtoffice)
              ?.name;

          num ltinstallment =
              num.tryParse(hctrl.settings?.defaultLtinstallAmt ?? "0") ?? 0;
          num stinstallment =
              num.tryParse(hctrl.settings?.defaultStinstallAmt ?? "0") ?? 0;
          num ltinterestcalc = ((user.ltLoansDue ?? 0) *
                  (num.tryParse('${hctrl.settings?.ltloanInterest}') ?? 0)) /
              1200;
          num stinterestcalc = ((user.stLoansDue ?? 0) *
                  (num.tryParse('${hctrl.settings?.stloanInterest}') ?? 0)) /
              1200;

          return PlutoRow(cells: {
            'sr_no': PlutoCell(value: "${index + 1}"),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name),
            'district_office': PlutoCell(value: officeName),
            'ob_lt': PlutoCell(value: user.ltLoansDue ?? 0),
            'ob_st': PlutoCell(value: user.stLoansDue ?? 0),
            'loan_paid_lt': PlutoCell(value: 0.0),
            'loan_paid_st': PlutoCell(value: 0.0),
            'loan_total': PlutoCell(
                value: (user.ltLoansDue ?? 0) + (user.stLoansDue ?? 0)),
            'sub': PlutoCell(value: hctrl.settings?.defaultSubsinstallAmt ?? 0),
            'lt_installment': PlutoCell(
                value: (user.ltLoansDue ?? 0) != 0 ? ltinstallment : 0),
            'st_installment': PlutoCell(
                value: (user.stLoansDue ?? 0) != 0 ? stinstallment : 0),
            'interest': PlutoCell(value: ltinterestcalc + stinterestcalc),
            'dues': PlutoCell(
              value: user.userPrevoiusMonthlyRecord?['dues'] ?? 0,
            ),
            'penalty': PlutoCell(
                value: user.userPrevoiusMonthlyRecord?['penalty'] ?? 0),
            'total': PlutoCell(
                value: (((user.ltLoansDue ?? 0) != 0 ? ltinstallment : 0) +
                    ((user.stLoansDue ?? 0) != 0 ? stinstallment : 0) +
                    (num.tryParse(
                            hctrl.settings?.defaultSubsinstallAmt ?? "0") ??
                        0) +
                    (ltinterestcalc + stinterestcalc) +
                    (user.userPrevoiusMonthlyRecord?['dues'] ?? 0) +
                    (user.userPrevoiusMonthlyRecord?['penalty'] ?? 0))),
            'installment_received': PlutoCell(value: 0.0),
            'installmentRecDate': PlutoCell(
              value: DateFormat('yyyy-MM-dd').format(DateTime.now()),
            ),
            'lt_closing_balance': PlutoCell(
                value: (user.ltLoansDue ?? 0) -
                    ((user.ltLoansDue ?? 0) != 0 ? ltinstallment : 0)),
            'st_closing_balance': PlutoCell(
                value: (user.stLoansDue ?? 0) -
                    ((user.stLoansDue ?? 0) != 0 ? stinstallment : 0)),
            'subscriptionPaid': PlutoCell(value: 0),
            'longTermInstalmentPaid': PlutoCell(value: 0),
            'shortTermInstalmentPaid': PlutoCell(value: 0),
            'longTermInterestPaid': PlutoCell(value: 0),
            'shortTermInterestPaid': PlutoCell(value: 0),
            'penaltyPaid': PlutoCell(value: 0),
            'totalReceived': PlutoCell(value: 0),
            'status': PlutoCell(value: "unpaid"),
          });
        })
      ];
    }
    if (rows.isNotEmpty && rows.last.cells['name']?.value == 'TOTAL') {
      rows.removeLast();
    }
    // Add the total row
    rows.add(buildTotalRow());
  }

  Future<void> _calcRowsDataForSelectedDO(bool reset) async {
    // print("Fetching data for office: $selectedoffice");
    fbUserMonthlyDataFetch = await FBFireStore.usermonthly
        .where('selectedyear', isEqualTo: selectedYear)
        .where('selectedmonth', isEqualTo: selectedMonth)
        .where('districtoffice', isEqualTo: selectedoffice)
        .get();

    if (fbUserMonthlyDataFetch != null) {
      fbData = fbUserMonthlyDataFetch!.docs
          .map((e) => UserMonthlyRecordModel.fromSnap(e))
          .toList();
    }

    if (reset) {
      // Fetch latest Firestore data for the selected office/month/year
      fbUserMonthlyDataFetch = await FBFireStore.usermonthly
          .where('selectedyear', isEqualTo: selectedYear)
          .where('selectedmonth', isEqualTo: selectedMonth)
          .where('districtoffice', isEqualTo: selectedoffice)
          .get();

      if (fbUserMonthlyDataFetch != null &&
          fbUserMonthlyDataFetch!.docs.isNotEmpty) {
        fbData = fbUserMonthlyDataFetch!.docs
            .map((e) => UserMonthlyRecordModel.fromSnap(e))
            .toList();

        // Reset payment fields for all rows
        rows = List.generate(
          fbData.length,
          (fbindex) {
            final user = fbData[fbindex];
            final officeName = hctrl.districtoffice
                .firstWhereOrNull((e) => e.docId == user.districtoffice)
                ?.name;
            return PlutoRow(cells: {
              'sr_no': PlutoCell(value: "${fbindex + 1}"),
              'cpf_no': PlutoCell(value: user.cpfNo),
              'name': PlutoCell(value: user.name),
              'district_office': PlutoCell(value: officeName),
              'ob_lt': PlutoCell(value: user.obLt ?? 0),
              'ob_st': PlutoCell(value: user.obSt ?? 0),
              'loan_paid_lt': PlutoCell(value: user.loanPaidLt ?? 0),
              'loan_paid_st': PlutoCell(value: user.loanPaidst ?? 0),
              'loan_total': PlutoCell(
                  value: (user.obLt ?? 0) +
                      (user.obSt ?? 0) +
                      (user.loanPaidLt ?? 0) +
                      (user.loanPaidst ?? 0)),
              'sub':
                  PlutoCell(value: hctrl.settings?.defaultSubsinstallAmt ?? 0),
              'lt_installment': PlutoCell(value: user.ltInstallment ?? 0),
              'st_installment': PlutoCell(value: user.stInstallment ?? 0),
              'interest': PlutoCell(
                  value: ((user.obLt ?? 0) +
                          (user.obSt ?? 0) +
                          (user.loanPaidLt ?? 0) +
                          (user.loanPaidst ?? 0)) *
                      8.5 /
                      1200),
              'dues': PlutoCell(value: user.dues ?? 0),
              'penalty': PlutoCell(value: user.penalty ?? 0),
              'total': PlutoCell(
                  value: (((user.ltInstallment ?? 0) > 0
                          ? user.ltInstallment ?? 0
                          : 0) +
                      ((user.stInstallment ?? 0) > 0
                          ? user.stInstallment ?? 0
                          : 0) +
                      (user.subs ?? 0) +
                      (user.interest ?? 0) +
                      (user.dues ?? 0) +
                      (user.penalty ?? 0))),
              'installment_received': PlutoCell(value: 0.0), // Reset
              'installmentRecDate': PlutoCell(value: ""), // Reset
              'lt_closing_balance': PlutoCell(
                  value: (user.obLt ?? 0) +
                      (user.loanPaidLt ?? 0) -
                      (user.ltInstallment ?? 0)),
              'st_closing_balance': PlutoCell(
                  value: (user.obSt ?? 0) +
                      (user.loanPaidst ?? 0) -
                      (user.stInstallment ?? 0)),
              'subscriptionPaid': PlutoCell(value: 0),
              'longTermInstalmentPaid': PlutoCell(value: 0),
              'shortTermInstalmentPaid': PlutoCell(value: 0),
              'longTermInterestPaid': PlutoCell(value: 0),
              'shortTermInterestPaid': PlutoCell(value: 0),
              'penaltyPaid': PlutoCell(value: 0),
              'totalReceived': PlutoCell(value: 0),
              'status': PlutoCell(value: "unpaid"), // Reset
            });
          },
        );
        return;
      }
    }

    // Fallback to previous logic if no Firestore data or not resetting
    if (fbData.isNotEmpty) {
      rows = List.generate(
        fbData.length,
        (fbindex) {
          final user = fbData[fbindex];
          final officeName = hctrl.districtoffice
              .firstWhereOrNull((e) => e.docId == user.districtoffice)
              ?.name;
          return PlutoRow(cells: {
            'sr_no': PlutoCell(value: "${fbindex + 1}"),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name),
            'district_office': PlutoCell(value: officeName),
            'ob_lt': PlutoCell(value: user.obLt ?? 0),
            'ob_st': PlutoCell(value: user.obSt ?? 0),
            'loan_paid_lt': PlutoCell(value: user.loanPaidLt ?? 0),
            'loan_paid_st': PlutoCell(value: user.loanPaidst ?? 0),
            'loan_total': PlutoCell(
                value: (user.obLt ?? 0) +
                    (user.obSt ?? 0) +
                    (user.loanPaidLt ?? 0) +
                    (user.loanPaidst ?? 0)),
            'sub': PlutoCell(value: hctrl.settings?.defaultSubsinstallAmt ?? 0),
            'lt_installment': PlutoCell(value: user.ltInstallment ?? 0),
            'st_installment': PlutoCell(value: user.stInstallment ?? 0),
            'interest': PlutoCell(
                value: ((user.obLt ?? 0) +
                        (user.obSt ?? 0) +
                        (user.loanPaidLt ?? 0) +
                        (user.loanPaidst ?? 0)) *
                    8.5 /
                    1200),
            'dues': PlutoCell(value: user.dues ?? 0),
            'penalty': PlutoCell(value: user.penalty ?? 0),
            'total': PlutoCell(
                value: (((user.ltInstallment ?? 0) > 0
                        ? user.ltInstallment ?? 0
                        : 0) +
                    ((user.stInstallment ?? 0) > 0
                        ? user.stInstallment ?? 0
                        : 0) +
                    (user.subs ?? 0) +
                    (user.interest ?? 0) +
                    (user.dues ?? 0) +
                    (user.penalty ?? 0))),
            'installment_received': PlutoCell(value: user.installmentRec ?? 0),
            'installmentRecDate': PlutoCell(
              value: (user.installmentRecDate == null)
                  ? null
                  : user.installmentRecDate,
            ),
            'lt_closing_balance': PlutoCell(
                value: (user.obLt ?? 0) +
                    (user.loanPaidLt ?? 0) -
                    (user.ltInstallment ?? 0)),
            'st_closing_balance': PlutoCell(
                value: (user.obSt ?? 0) +
                    (user.loanPaidst ?? 0) -
                    (user.stInstallment ?? 0)),
            'subscriptionPaid': PlutoCell(value: user.subscriptionPaid),
            'longTermInstalmentPaid':
                PlutoCell(value: user.longTermInstalmentPaid),
            'shortTermInstalmentPaid':
                PlutoCell(value: user.shortTermInstalmentPaid),
            'longTermInterestPaid': PlutoCell(value: user.longTermInterestPaid),
            'shortTermInterestPaid':
                PlutoCell(value: user.shortTermInterestPaid),
            'penaltyPaid': PlutoCell(value: user.penaltyPaid),
            // 'totalReceived': PlutoCell(value: user.totalReceived ?? 0),
            'status': PlutoCell(value: user.status ?? "unpaid"),
          });
        },
      );
    } else {
      // Fallback to calculation if no Firestore data
      final data = recoveryPageModel.firstWhereOrNull((element) {
        return element.doId == selectedoffice;
      })?.doUser;

      rows = [
        ...List.generate(data?.length ?? 0, (index) {
          num ltinstallment =
              num.tryParse(hctrl.settings?.defaultLtinstallAmt ?? "0") ?? 0;
          num stinstallment =
              num.tryParse(hctrl.settings?.defaultStinstallAmt ?? "0") ?? 0;
          num ltinterestcalc = (((data?[index].user.ltLoansDue ?? 0) *
                  (num.tryParse('${hctrl.settings?.ltloanInterest}') ?? 0)) /
              1200);
          num stinterestcalc = (((data?[index].user.stLoansDue ?? 0) *
                  (num.tryParse('${hctrl.settings?.stloanInterest}') ?? 0)) /
              1200);

          return PlutoRow(cells: {
            'sr_no': PlutoCell(value: "${index + 1}"),
            'cpf_no': PlutoCell(value: data?[index].user.cpfNo),
            'name': PlutoCell(value: data?[index].user.name),
            'district_office': PlutoCell(
                value: hctrl.districtoffice
                    .firstWhereOrNull(
                        (e) => e.docId == data?[index].user.districtoffice)
                    ?.name),
            'ob_lt': PlutoCell(value: data?[index].user.ltLoansDue ?? "0.0"),
            'ob_st': PlutoCell(value: data?[index].user.stLoansDue ?? "0.0"),
            'loan_paid_lt': PlutoCell(value: 0.0),
            'loan_paid_st': PlutoCell(value: 0.0),
            'loan_total': PlutoCell(
                value: (data?[index].user.ltLoansDue ?? 0) +
                    (data?[index].user.stLoansDue ?? 0)),
            'sub': PlutoCell(value: hctrl.settings?.defaultSubsinstallAmt ?? 0),
            'lt_installment': PlutoCell(
                value: data?[index].user.ltLoansDue != 0 ? ltinstallment : 0),
            'st_installment': PlutoCell(
                value: data?[index].user.stLoansDue != 0 ? stinstallment : 0),
            'interest': PlutoCell(value: ltinterestcalc + stinterestcalc),
            'dues': PlutoCell(
              value: data?[index].user.userPrevoiusMonthlyRecord?['dues'] ?? 0,
            ),
            'penalty': PlutoCell(
                value:
                    data?[index].user.userPrevoiusMonthlyRecord?['penalty'] ??
                        0),
            'total': PlutoCell(
                value: ((data?[index].user.ltLoansDue != 0
                        ? ltinstallment
                        : 0) +
                    (data?[index].user.stLoansDue != 0 ? stinstallment : 0) +
                    (num.tryParse(
                            hctrl.settings?.defaultSubsinstallAmt ?? "0") ??
                        0) +
                    (ltinterestcalc + stinterestcalc) +
                    (data?[index].user.userPrevoiusMonthlyRecord?['dues'] ??
                        0) +
                    (data?[index].user.userPrevoiusMonthlyRecord?['penalty'] ??
                        0))),
            'installment_received': PlutoCell(value: 0.0),
            'installmentRecDate': PlutoCell(
              value: DateFormat('yyyy-MM-dd').format(DateTime.now()),
            ),
            'lt_closing_balance': PlutoCell(
                value: (data?[index].user.ltLoansDue ?? 0) -
                    (data?[index].user.ltLoansDue != 0 ? ltinstallment : 0)),
            'st_closing_balance': PlutoCell(
                value: (data?[index].user.stLoansDue ?? 0) -
                    (data?[index].user.stLoansDue != 0 ? stinstallment : 0)),
            'subscriptionPaid': PlutoCell(value: 0),
            'longTermInstalmentPaid': PlutoCell(value: 0),
            'shortTermInstalmentPaid': PlutoCell(value: 0),
            'longTermInterestPaid': PlutoCell(value: 0),
            'shortTermInterestPaid': PlutoCell(value: 0),
            'penaltyPaid': PlutoCell(value: 0),
            'totalReceived': PlutoCell(value: 0),
            'status': PlutoCell(value: "unpaid"),
          });
        })
      ];
    }
    if (rows.isNotEmpty && rows.last.cells['name']?.value == 'TOTAL') {
      rows.removeLast();
    }
    // Add the total row
    rows.add(buildTotalRow());
  }

  void dataFetchLocally() {
    doffice = hctrl.districtoffice;
    recoveryMonthlyModel.clear();
    for (DistrictOfficeModel doOffoice in doffice) {
      // print("....1");
      // print("m : $month");
      // print("y : $year");
      recoveryPageModel.add(RecoveryPageModel(
          doId: doOffoice.docId,
          month: selectedMonth ?? 0,
          year: selectedYear ?? 0,
          doUser: [
            ...List.generate(
                hctrl.users
                    .where((element) => element.archived == false)
                    .where(
                        (element) => element.districtoffice == doOffoice.docId)
                    .length, (index) {
              // print(
              //     "loan : ${hctrl.loan.where((element) => element.isSettled || element.isNew == false).where((element) => element.uid == (hctrl.inputUsers.where((element) => element.districtoffice == doOffoice.docId).toList()[index]).docId).toList().map((e) => e.applicationNo)}");
              return DoUser(
                  user: hctrl.inputUsers
                      .where((element) =>
                          element.districtoffice == doOffoice.docId)
                      .toList()[index],
                  loans: hctrl.loan
                      .where((element) =>
                          element.isSettled || element.isNew == false)
                      .where((element) =>
                          element.uid ==
                          (hctrl.inputUsers
                                  .where((element) =>
                                      element.districtoffice == doOffoice.docId)
                                  .toList()[index])
                              .docId)
                      .toList());
            })
          ]));
    }
    setState(() {});
  }

  Future onReset() async {
    if (rows.isEmpty) {
      return showCtcAppSnackBar(context, "No data to Reset");
    }
    try {
      setState(() {
        resetingData = true;
      });

      rows.clear();

      await calcRowsData(true);

      stateManager.removeAllRows();

      stateManager.insertRows(0, rows);

      showCtcAppSnackBar(context, "Data Reset Done");

      setState(() {
        resetingData = false;
      });
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, "Data Reset failed");
      setState(() {
        resetingData = false;
      });
    }

    context.pop();
  }

  Future<void> onSend(HomeCtrl ctrl) async {
    if (selectedoffice == null) {
      showCtcAppSnackBar(context, "Select DO");
      return;
    }

    if (selectedoffice == 'ALL') {
      return;
    }

    if (rows.isEmpty) {
      showCtcAppSnackBar(context, "No data to send");
      return;
    }

    String? dateString =
        rows.first.cells['installmentRecDate']?.value?.toString();

    DateTime? date;
    if (dateString != null && dateString.isNotEmpty) {
      date = DateFormat("yyyy-MM-dd").parse(dateString);
    } else {
      date = null;
    }

    fbRecoveryMonthlyDataFetch = await FBFireStore.recoverymonthly
        .where('selectedyear', isEqualTo: selectedYear)
        .where('selectedmonth', isEqualTo: selectedMonth)
        .where('doId', isEqualTo: selectedoffice)
        .get();

    final fbUsersMonthlyDataFetch = await FBFireStore.usermonthly
        .where('selectedyear', isEqualTo: selectedYear)
        .where('selectedmonth', isEqualTo: selectedMonth)
        .where('districtoffice', isEqualTo: selectedoffice)
        .get();

    if (selectedYear != null) {
      fbSocietyYearlyDataFetch = await FBFireStore.societyYearly
          // .where('doId', isEqualTo: selectedoffice)
          .where('selectedyear', isEqualTo: selectedYear)
          .get();
    } else {
      return;
    }

    double totalObLt = 0;
    double totalObSt = 0;
    double totalLoanPaidLt = 0;
    double totalLoanPaidSt = 0;
    double totalLoanTotal = 0;
    double totalSubs = 0;
    double totalInstallmentRec = 0;
    double totalInterest = 0;
    double totalReceived = 0;
    double totalLtInstallment = 0;
    double totalStInstallment = 0;
    double totalLtCb = 0;
    double totalStCb = 0;
    double totalSubscriptionPaid = 0;
    double totalLongTermInstalmentPaid = 0;
    double totalShortTermInstalmentPaid = 0;
    double totalLongTermInterestPaid = 0;
    double totalShortTermInterestPaid = 0;

    final batch3 = FBFireStore.fb.batch();

    if (fbRecoveryMonthlyDataFetch?.size == 0) {
      final rdocRef = FBFireStore.recoverymonthly.doc();
      batch3.set(rdocRef, {
        'obLt': totalObLt,
        'obSt': totalObSt,
        'loanPaidLt': totalLoanPaidLt,
        'loanPaidst': totalLoanPaidSt,
        'loanTotal': totalLoanTotal,
        'subs': totalSubs,
        'ltInstallment': totalLtInstallment,
        'stInstallment': totalStInstallment,
        'interest': totalInterest,
        'total': totalReceived,
        'installmentRec': totalInstallmentRec,
        'installmentRecDate': date,
        'ltCb': totalLtCb,
        'stCb': totalStCb,
        'subscriptionPaid': totalSubscriptionPaid,
        'longTermInstalmentPaid': totalLongTermInstalmentPaid,
        'shortTermInstalmentPaid': totalShortTermInstalmentPaid,
        'longTermInterestPaid': totalLongTermInterestPaid,
        'shortTermInterestPaid': totalShortTermInterestPaid,
        'totalReceived': totalReceived,
        'selectedyear': selectedYear,
        'selectedmonth': selectedMonth,
        'doId': selectedoffice,
      });
    } else {
      batch3.update(
          FBFireStore.recoverymonthly
              .doc(fbRecoveryMonthlyDataFetch?.docs.first.id),
          {
            'obLt': totalObLt.round(),
            'obSt': totalObSt.round(),
            'loanPaidLt': totalLoanPaidLt.round(),
            'loanPaidst': totalLoanPaidSt.round(),
            'loanTotal': totalLoanTotal.round(),
            'subs': totalSubs.round(),
            'ltInstallment': totalLtInstallment.round(),
            'stInstallment': totalStInstallment.round(),
            'interest': totalInterest.round(),
            'total': totalReceived.round(),
            'installmentRec': totalInstallmentRec.round(),
            'installmentRecDate': date,
            'ltCb': totalLtCb.round(),
            'stCb': totalStCb.round(),
            'subscriptionPaid': totalSubscriptionPaid.round(),
            'longTermInstalmentPaid': totalLongTermInstalmentPaid.round(),
            'shortTermInstalmentPaid': totalShortTermInstalmentPaid.round(),
            'longTermInterestPaid': totalLongTermInterestPaid.round(),
            'shortTermInterestPaid': totalShortTermInterestPaid.round(),
            'totalReceived': totalReceived.round(),
            'selectedyear': selectedYear,
            'selectedmonth': selectedMonth,
            'doId': selectedoffice,
          });
    }

    //creating UserMonthly

    if (fbUsersMonthlyDataFetch.size == 0) {
      for (var row in rows) {
        if (row.cells['name']?.value == 'TOTAL') continue;
        final docRef = FBFireStore.usermonthly.doc();
        batch3.set(docRef, {
          'selectedyear': selectedYear,
          'selectedmonth': selectedMonth,
          'districtoffice': selectedoffice,
          'cpfNo': row.cells['cpf_no']?.value,
          'name': row.cells['name']?.value,
          'obLt': row.cells['ob_lt']?.value,
          'obSt': row.cells['ob_st']?.value,
          'loanPaidLt': row.cells['loan_paid_lt']?.value,
          'loanPaidst': row.cells['loan_paid_st']?.value,
          'loanTotal': row.cells['loan_total']?.value,
          'subs': row.cells['sub']?.value,
          'ltInstallment': row.cells['lt_installment']?.value,
          'stInstallment': row.cells['st_installment']?.value,
          'interest': row.cells['interest']?.value,
          'total': row.cells['total']?.value,
          'installmentRec': row.cells['installment_received']?.value,
          'installmentRecDate': date,
          'ltCb': row.cells['lt_closing_balance']?.value,
          'stCb': row.cells['st_closing_balance']?.value,
          'subscriptionPaid': row.cells['subscriptionPaid']?.value,
          'longTermInstalmentPaid': row.cells['longTermInstalmentPaid']?.value,
          'shortTermInstalmentPaid':
              row.cells['shortTermInstalmentPaid']?.value,
          'longTermInterestPaid': row.cells['longTermInterestPaid']?.value,
          'shortTermInterestPaid': row.cells['shortTermInterestPaid']?.value,
          'penaltyPaid': row.cells['penaltyPaid']?.value,
          'totalReceived': row.cells['totalReceived']?.value,
          'isPaid': false,
          'status': ((row.cells['total']?.value).round() ==
                  (row.cells['installment_received']?.value).round())
              ? 'Paid'
              : ((row.cells['installment_received']?.value).round() > 0
                  ? 'Partially Paid'
                  : 'Unpaid'),
          'societySubsPayout': null,
          'societySharesPayout': null,
        });
      }
    } else {
      for (var row in rows) {
        if (row.cells['name']?.value == 'TOTAL') continue;
        final cpfDocId = fbUserMonthlyDataFetch?.docs.firstWhereOrNull(
            (element) => element.data()['cpfNo'] == row.cells['cpf_no']?.value);

        num total =
            num.tryParse(row.cells['total']?.value.toString() ?? "") ?? 0;

        num installmentRec = num.tryParse(
                row.cells['installment_received']?.value.toString() ?? "") ??
            0;

        num userDues = total - installmentRec;

        num userPenalty = 0;

        if (userDues > 0) {
          final ltPaid = num.tryParse(
                  row.cells['longTermInterestPaid']?.value.toString() ?? "0") ??
              0;
          final stPaid = num.tryParse(
                  row.cells['shortTermInterestPaid']?.value.toString() ??
                      "0") ??
              0;

          final ltInterestRate =
              num.tryParse(ctrl.settings?.ltloanInterest.toString() ?? "0") ??
                  0;
          final stInterestRate =
              num.tryParse(ctrl.settings?.stloanInterest.toString() ?? "0") ??
                  0;

          final obLt =
              num.tryParse(row.cells['ob_lt']?.value.toString() ?? "0") ?? 0;
          final obSt =
              num.tryParse(row.cells['ob_st']?.value.toString() ?? "0") ?? 0;

          final expectedLtInterest = (obLt * ltInterestRate) / 1200;
          final expectedStInterest = (obSt * stInterestRate) / 1200;

          final unpaidLt =
              (ltPaid < expectedLtInterest) ? (expectedLtInterest - ltPaid) : 0;
          final unpaidSt =
              (stPaid < expectedStInterest) ? (expectedStInterest - stPaid) : 0;

          userPenalty = (unpaidLt.round() * ltInterestRate / 1200) +
              (unpaidSt.round() * stInterestRate / 1200);
        }

        batch3.update(FBFireStore.usermonthly.doc(cpfDocId?.id), {
          'selectedyear': selectedYear,
          'selectedmonth': selectedMonth,
          'districtoffice': selectedoffice,
          'cpfNo': num.tryParse(row.cells['cpf_no']?.value.toString() ?? ""),
          'name': row.cells['name']?.value,
          'obLt': num.tryParse(row.cells['ob_lt']?.value.toString() ?? ""),
          'obSt': num.tryParse(row.cells['ob_st']?.value.toString() ?? ""),
          'loanPaidLt':
              num.tryParse(row.cells['loan_paid_lt']?.value.toString() ?? ""),
          'loanPaidst':
              num.tryParse(row.cells['loan_paid_st']?.value.toString() ?? ""),
          'loanTotal':
              num.tryParse(row.cells['loan_total']?.value.toString() ?? ""),
          'subs': num.tryParse(row.cells['sub']!.value.toString()),
          'ltInstallment':
              num.tryParse(row.cells['lt_installment']?.value.toString() ?? ""),
          'stInstallment':
              num.tryParse(row.cells['st_installment']?.value.toString() ?? ""),
          'interest':
              num.tryParse(row.cells['interest']?.value.toString() ?? ""),
          'total': num.tryParse(row.cells['total']?.value.toString() ?? ""),
          'installmentRec': num.tryParse(
                  row.cells['installment_received']?.value.toString() ?? "") ??
              0,
          'installmentRecDate': date,
          'ltCb': num.tryParse(
              row.cells['lt_closing_balance']?.value.toString() ?? ""),
          'stCb': num.tryParse(
              row.cells['st_closing_balance']?.value.toString() ?? ""),
          'subscriptionPaid': num.tryParse(
              row.cells['subscriptionPaid']?.value.toString() ?? ""),
          'longTermInstalmentPaid': num.tryParse(
              row.cells['longTermInstalmentPaid']?.value.toString() ?? ""),
          'shortTermInstalmentPaid': num.tryParse(
              row.cells['shortTermInstalmentPaid']?.value.toString() ?? ""),
          'longTermInterestPaid': num.tryParse(
              row.cells['longTermInterestPaid']?.value.toString() ?? ""),
          'shortTermInterestPaid': num.tryParse(
              row.cells['shortTermInterestPaid']?.value.toString() ?? ""),
          'penaltyPaid':
              num.tryParse(row.cells['penaltyPaid']?.value.toString() ?? ""),
          'totalReceived':
              num.tryParse(row.cells['totalReceived']?.value.toString() ?? ""),
          'isPaid': total.round() == installmentRec.round(),
          'status': total.round() == installmentRec.round()
              ? 'Paid'
              : (installmentRec.round() > 0 ? 'Partially Paid' : 'Unpaid'),
          'societySubsPayout': null,
          'societySharesPayout': null,
        });
      }
    }

    if (fbSocietyYearlyDataFetch?.docs.isEmpty ?? true) {
      batch3.set(FBFireStore.societyYearly.doc(), {
        // 'doId': selectedoffice,
        'createdAt': Timestamp.now(),
        'selectedyear': selectedYear,
        'updatedAt': null,
        'OB': totalObLt + totalObSt,
        'CB': totalStCb + totalLtCb,
        'totalSubscription': totalSubscriptionPaid,
        'intOnSubscription': 0,
        'subscriptionInterestRate':
            num.tryParse(ctrl.settings?.subscriptionInterest ?? "0"),
        'totalLoanGiven': totalLoanPaidLt + totalLoanPaidSt,
        'totalLoanReceived': totalLoanTotal,
        'ltLoanReceived': totalLtInstallment,
        'stLoanReceived': totalStInstallment,
        'ltLoanGiven': totalLongTermInstalmentPaid,
        'stLoanGiven': totalShortTermInstalmentPaid,
        'ltIntAmt': totalLongTermInterestPaid,
        'stIntAmt': totalShortTermInterestPaid,
        'totalIntAmt': totalInterest,
        'loanIntRate': num.tryParse(ctrl.settings!.ltloanInterest.toString()),
        'totalPendingLoan': totalLoanTotal - totalLoanPaidLt - totalLoanPaidSt,
        'ltPendingLoan': totalObLt - totalLtInstallment,
        'stPendingLoan': totalObSt - totalStInstallment,
        'totalExpenses': totalSubscriptionPaid,
        'expensesIds': [],
        'totalDividend': 0,
        // totalSubs *
        //     (num.tryParse(ctrl.settings!.dividentRate.toString()) ?? 0),
        'totalMonthlyShareGiven': 0,
        'totalShareGiven': 0,
        'monthlyDividend': 0,
        // totalSubs *
        //     (num.tryParse(ctrl.settings?.dividentRate ?? "0") ?? 0),
        'dividendRate': num.tryParse(ctrl.settings?.dividentRate ?? "0"),
        'cashBalance': num.tryParse(0.toString()),
      });
    } else {
      batch3.update(
          FBFireStore.societyYearly
              .doc(fbSocietyYearlyDataFetch!.docs.first.id),
          {
            'updatedAt': Timestamp.now(),
            'OB': totalObLt.round() + totalObSt.round(),
            'CB': totalStCb.round() + totalLtCb.round(),
            'totalSubscription': totalSubscriptionPaid.round(),
            'intOnSubscription': 0,
            'subscriptionInterestRate':
                num.tryParse(ctrl.settings?.subscriptionInterest ?? "0"),
            'totalLoanGiven': totalLoanPaidLt.round() + totalLoanPaidSt.round(),
            'totalLoanReceived': totalLoanTotal.round(),
            'ltLoanReceived': totalLtInstallment.round(),
            'stLoanReceived': totalStInstallment.round(),
            'ltLoanGiven': totalLoanPaidLt.round(),
            'stLoanGiven': totalLoanPaidSt.round(),
            'ltIntAmt': totalLongTermInterestPaid.round(),
            'stIntAmt': totalShortTermInterestPaid.round(),
            'totalIntAmt': totalLongTermInterestPaid.round() +
                totalShortTermInterestPaid.round(),
            'loanIntRate':
                num.tryParse(ctrl.settings!.ltloanInterest.toString()),
            'totalPendingLoan': totalLoanTotal.round() -
                totalLoanPaidLt.round() -
                totalLoanPaidSt.round(),
            'ltPendingLoan': totalObLt.round() - totalLtInstallment.round(),
            'stPendingLoan': totalObSt.round() - totalStInstallment.round(),
            'totalExpenses': totalSubscriptionPaid.round(),
            'expensesIds': [],
            'totalDividend': 0,
            'totalMonthlyShareGiven': 0,
            'totalShareGiven': 0,
            'monthlyDividend': 0,
            'cashBalance': 0,
          });
    }

    await batch3.commit();

    final doName = hctrl.districtoffice
        .where((element) => element.docId == selectedoffice);

    final readableMonth = getMonthName(selectedMonth ?? 0);

    if (selectedoffice != null) {
      final doData = ctrl.districtoffice
          .firstWhere((element) => element.docId == selectedoffice);
      String email = doData.email;
      String ccEmails = (doData.extraEmail ?? []).join(',');
      String pdfBase64 = await generatePDF();

      if (pdfBase64.isEmpty) {
        showCtcAppSnackBar(
            context, 'Failed to generate PDF. Please try again.');
        return;
      }

      List<Map<String, String?>> recoveryData = rows.map((row) {
        return {
          'cpfNo': row.cells['cpf_no']?.value.toString(),
          'memberName': row.cells['name']?.value.toString(),
          'districtOffice': row.cells['district_office']?.value.toString(),
          'total': row.cells['total']?.value.toString(),
        };
      }).toList();

      // print("Recovery Data in on send: $recoveryData");

      if (recoveryData.isEmpty) {
        showCtcAppSnackBar(
            context, 'Recovery data is invalid. Please try again.');
        return;
      }

      String tableRows = '';
      for (var data in recoveryData) {
        tableRows += '''
        <tr>
          <td>${data['cpfNo'] ?? 'N/A'}</td>
          <td>${data['name'] ?? 'N/A'}</td>
          <td>${data['districtoffice'] ?? 'N/A'}</td>
          <td>${data['total'] ?? 'N/A'}</td>
        </tr>
      ''';
      }

      // print("table rows : $tableRows");

      String htmlContent = '''
      <html>
        <body>
          <p>Dear Sir,</p>
          <p>Please find herewith the recovery statement of FCI Emp. Society Baroda for the month of $readableMonth - $selectedYear.</p>
          <br>
          <table border="1" cellspacing="0" cellpadding="5">
            <thead>
              <tr>
                <th>CPF NO.</th>
                <th>NAME OF MEMBER</th>
                <th>DISTRICT OFFICE</th>
                <th>TOTAL</th>
              </tr>
            </thead>
            <tbody>
              $tableRows
            </tbody>
          </table>
          <br>
          <p>Thanking You,</p>
          <p>FCI Emp. Co. Op. Credit Society.<br>Baroda</p>
        </body>
      </html>
    ''';

      final data = <String, dynamic>{
        'email': email,
        'cc': ccEmails,
        'htmlContent': htmlContent,
        'file': pdfBase64,
        'recoveryData': recoveryData,
        'selectedoffice': doName.first.name,
        'selectedMonth': readableMonth,
        'selectedYear': selectedYear,
      };

      try {
        // print("Sending email...");
        mailSent = true;
        setState(() {});

        final result =
            await FBFunctions.ff.httpsCallable('sendRecoveryEmail').call(data);
        // print("data: $data");
        // print("Result: $result");

        mailSent = false;
        setState(() {});

        showCtcAppSnackBar(context, "Recovery email sent successfully!");
      } catch (e) {
        debugPrint("Error sending email: $e");
        showCtcAppSnackBar(
            context, 'Failed to send recovery email. Please try again later.');
      }
    } else {
      // print("No District Office selected.");
      showCtcAppSnackBar(context, 'Please select a District Office first.');
    }
  }

  Future<void> loadPrevMonthBalances() async {
    if (selectedoffice == null ||
        selectedMonth == null ||
        selectedYear == null) {
      return;
    }

    final prevMonth = selectedMonth == 1 ? 12 : selectedMonth! - 1;
    final prevYear = selectedMonth == 1 ? selectedYear! - 1 : selectedYear!;

    try {
      final usersSnapshot = await FBFireStore.users
          .where('districtoffice', isEqualTo: selectedoffice)
          .get();

      // print("usersSnapshot : ${usersSnapshot.docs.length}");

      final prevMonthRecords = usersSnapshot.docs.where((doc) {
        final record = doc['userPrevoiusMonthlyRecord'];
        if (record == null) return false;

        // print("pmrecord : $record");
        // print("r year : ${record['selectedyear']}");
        // print("r month: ${record['selectedmonth']}");

        return record['selectedyear'] == prevYear &&
            record['selectedmonth'] == prevMonth;
      }).toList();

      // print("Filtered prevMonthRecords: ${prevMonthRecords.length}");

      if (prevMonthRecords.isEmpty) {
        // print("No previous month data found");
        return;
      }

      final prevBalances = {
        for (var doc in prevMonthRecords)
          doc['cpfNo']: () {
            final record = doc['userPrevoiusMonthlyRecord'];
            return {
              'uid': record['uid'],
              'cpfNo': record['cpfNo'] ?? 0,
              'selectedmonth': record['selectedmonth'] ?? 0,
              'selectedyear': record['selectedyear'] ?? 0,
              'ltCb': num.tryParse(record['ltCb']?.toString() ?? '0') ?? 0,
              'stCb': num.tryParse(record['stCb']?.toString() ?? '0') ?? 0,
              'loan_paid_lt':
                  num.tryParse(record['loan_paid_lt']?.toString() ?? '0') ?? 0,
              'loan_paid_st':
                  num.tryParse(record['loan_paid_st']?.toString() ?? '0') ?? 0,
              'subs': num.tryParse(record['subs']?.toString() ?? '0') ?? 0,
              'lt_installment':
                  num.tryParse(record['ltInstallment']?.toString() ?? '0') ?? 0,
              'st_installment':
                  num.tryParse(record['stInstallment']?.toString() ?? '0') ?? 0,
              'interest':
                  num.tryParse(record['interest']?.toString() ?? '0') ?? 0,
              'dues': num.tryParse(record['dues']?.toString() ?? '0') ?? 0,
              'penalty':
                  num.tryParse(record['penalty']?.toString() ?? '0') ?? 0,
            };
          }()
      };

      // print("PrevBalances Map: $prevBalances");

      for (var row in rows) {
        final cpf = row.cells['cpf_no']?.value;
        if (cpf != null && prevBalances.containsKey(cpf)) {
          final prev = prevBalances[cpf]!;
          // final uid = prev['uid'];
          final cpfNo = prev['cpfNo'];
          final uid = usersSnapshot.docs
              .firstWhere(
                (doc) => doc['cpfNo'] == cpfNo,
                // orElse: () => null,
              )
              .id;

          // print("uid : $cpfNo");

          final loanSnapshot = await FBFireStore.loan
              .where('uid', isEqualTo: uid)
              .where('isSettled', isEqualTo: false)
              .where('settledOn', isEqualTo: null)
              .get();

          // print("loanSnapshot : ${loanSnapshot.docs.length}");

          if (loanSnapshot.docs.isEmpty) {
            // print("Loan is settled for cpfNo: $cpf, skipping...");
            continue;
          }

          row.cells['ob_lt']?.value = prev['ltCb'];
          row.cells['ob_st']?.value = prev['stCb'];

          row.cells['loan_paid_lt']?.value = 0;
          row.cells['loan_paid_st']?.value = 0;

          row.cells['loan_total']?.value = (row.cells['ob_lt']?.value ?? 0) +
              (row.cells['ob_st']?.value ?? 0) +
              (row.cells['loan_paid_lt']?.value ?? 0) +
              (row.cells['loan_paid_st']?.value ?? 0);

          row.cells['ltInstallment']?.value = prev['lt_installment'];
          row.cells['stInstallment']?.value = prev['st_installment'];

          row.cells['interest']?.value =
              ((row.cells['loan_total']?.value ?? 0) * 8.5 / 1200);

          row.cells['total']?.value = (prev['subs'] ?? 0) +
              (prev['lt_installment'] ?? 0) +
              (prev['st_installment'] ?? 0) +
              (row.cells['interest']?.value ?? 0);

          row.cells['lt_closing_balance']?.value =
              (prev['ltCb'] ?? 0) - (prev['lt_installment'] ?? 0);

          row.cells['st_closing_balance']?.value =
              (prev['stCb'] ?? 0) - (prev['st_installment'] ?? 0);

          row.cells['installment_received']?.value = 0;
          row.cells['installmentRecDate']?.value = "";
          row.cells['subscriptionPaid']?.value = 0;
          row.cells['longTermInstalmentPaid']?.value = 0;
          row.cells['shortTermInstalmentPaid']?.value = 0;
          row.cells['longTermInterestPaid']?.value = 0;
          row.cells['shortTermInterestPaid']?.value = 0;
          row.cells['totalReceived']?.value = 0;

          row.cells['dues']?.value = prev['dues'] ?? 0;
          row.cells['penalty']?.value = prev['penalty'] ?? 0;

          final totalCeil = (row.cells['total']?.value ?? 0).round();
          final instRecCeil =
              (row.cells['installment_received']?.value ?? 0).round();
          row.cells['status']?.value = (totalCeil == instRecCeil)
              ? 'Paid'
              : (instRecCeil > 0 ? 'Partially Paid' : 'Unpaid');
          row.cells['dues']?.value =
              (totalCeil - instRecCeil).clamp(0, double.infinity);
        }
      }

      setState(() {});
    } catch (e, stack) {
      debugPrint("Error loading previous balances: $e");
      debugPrint("stack : $stack");
    }
  }

  Future<void> applyPrevMonthBalances() async {
    if (selectedoffice == null ||
        selectedMonth == null ||
        selectedYear == null) {
      return;
    }

    setState(() => applyPrevMonthData = true);

    final prevMonth = selectedMonth == 1 ? 12 : selectedMonth! - 1;
    final prevYear = selectedMonth == 1 ? selectedYear! - 1 : selectedYear!;

    try {
      final prevMonthData = await FBFireStore.users
          .where('districtoffice', isEqualTo: selectedoffice)
          .get();

      // print("usersSnapshot : ${prevMonthData.docs.length}");

      final prevMonthRecords = prevMonthData.docs.where((doc) {
        final record = doc['userPrevoiusMonthlyRecord'];
        return record != null &&
            record['selectedyear'] == prevYear &&
            record['selectedmonth'] == prevMonth;
      }).toList();

      rows.clear();

      for (final userDoc in prevMonthRecords) {
        final prevRecord = userDoc['userPrevoiusMonthlyRecord'];
        final cpf = prevRecord['cpfNo'];
        final name = prevRecord['name'] ?? "unknown";

        // print("prevRecord['uid'] : ${prevRecord['uid']}");

        // print("${prevRecord['cpfNo']}, ${prevRecord['uid']}");

        final loanSnapshot = await FBFireStore.loan
            .where('uid', isEqualTo: prevRecord['uid'])
            .get();

        // print(
        //     "Checking loans for CPF: $cpf, Loans found: ${loanSnapshot.docs.length}");

        bool hasActiveLoan = loanSnapshot.docs.any((loanDoc) {
          final settledOn = loanDoc['settledOn'];

          if (settledOn == null) {
            return true;
          }

          final settledDate = (settledOn as Timestamp).toDate();
          final selectedDate = DateTime(prevYear, prevMonth, 1);

          return settledDate.isAfter(selectedDate);
        });

        if (hasActiveLoan) {
          rows.add(PlutoRow(
            cells: {
              'sr_no': PlutoCell(value: rows.length + 1),
              'cpf_no': PlutoCell(value: cpf),
              'name': PlutoCell(value: name),
              'ob_lt': PlutoCell(
                  value: num.tryParse(prevRecord['ltCb'].toString()) ?? 0),
              'ob_st': PlutoCell(
                  value: num.tryParse(prevRecord['stCb'].toString()) ?? 0),
            },
          ));
        } else {
          // print(
          //     "Skipping user ${userDoc['name']} - loan already settled for selected month.");
        }
      }

      await calcRowsData(false);
    } catch (e) {
      debugPrint("Error forcing previous month balances: $e");
    } finally {
      setState(() => applyPrevMonthData = false);
    }
  }

  PlutoRow buildTotalRow() {
    num sum(String field) => rows.fold<num>(
        0,
        (prev, row) =>
            prev +
            (num.tryParse(row.cells[field]?.value.toString() ?? '0') ?? 0));

    return PlutoRow(
      cells: {
        'sr_no': PlutoCell(value: ''),
        'cpf_no': PlutoCell(value: ''),
        'name': PlutoCell(value: 'TOTAL'),
        'district_office': PlutoCell(value: ''),
        'ob_lt': PlutoCell(value: sum('ob_lt')),
        'ob_st': PlutoCell(value: sum('ob_st')),
        'loan_paid_lt': PlutoCell(value: sum('loan_paid_lt')),
        'loan_paid_st': PlutoCell(value: sum('loan_paid_st')),
        'loan_total': PlutoCell(value: sum('loan_total')),
        'sub': PlutoCell(value: sum('sub')),
        'lt_installment': PlutoCell(value: sum('lt_installment')),
        'st_installment': PlutoCell(value: sum('st_installment')),
        'interest': PlutoCell(value: sum('interest')),
        'dues': PlutoCell(value: sum('dues')),
        'penalty': PlutoCell(value: sum('penalty')),
        'total': PlutoCell(value: sum('total')),
        'lt_closing_balance': PlutoCell(value: sum('lt_closing_balance')),
        'st_closing_balance': PlutoCell(value: sum('st_closing_balance')),
        'installment_received': PlutoCell(value: sum('installment_received')),
        'installmentRecDate': PlutoCell(value: ''),
        'subscriptionPaid': PlutoCell(value: sum('subscriptionPaid')),
        'longTermInterestPaid': PlutoCell(value: sum('longTermInterestPaid')),
        'shortTermInterestPaid': PlutoCell(value: sum('shortTermInterestPaid')),
        'longTermInstalmentPaid':
            PlutoCell(value: sum('longTermInstalmentPaid')),
        'shortTermInstalmentPaid':
            PlutoCell(value: sum('shortTermInstalmentPaid')),
        'penaltyPaid': PlutoCell(value: sum('penaltyPaid')),
        'status': PlutoCell(value: ''),
      },
    );
  }
}
