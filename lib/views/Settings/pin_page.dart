import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class PinPage extends StatefulWidget {
  const PinPage({super.key});

  @override
  State<PinPage> createState() => _PinPageState();
}

class _PinPageState extends State<PinPage> {
  TextEditingController pinCtrl = TextEditingController();
  String _enteredPin = '';
  bool _pinVerified = false;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 500,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              decoration: const InputDecoration(
                filled: true,
                border: OutlineInputBorder(),
                labelText: 'Enter PIN to Access Settings',
              ),
              controller: pinCtrl,
              obscureText: true,
              // maxLength: 10,
              onChanged: (text) => setState(() => _enteredPin = text),
              onFieldSubmitted: (_) => _verifyPin(),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              style: ButtonStyle(
                padding: WidgetStatePropertyAll(EdgeInsetsDirectional.symmetric(
                    horizontal: 40, vertical: 15)),
                shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
                backgroundColor: WidgetStatePropertyAll(Colors.green),
                elevation: WidgetStatePropertyAll(0),
              ),
              onPressed: _pinVerified ? null : _verifyPin,
              child: const Text(
                'Submit PIN',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 17,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _verifyPin() {
    final String correctPin =
        Get.find<HomeCtrl>().settings?.setPin.toString() ?? "";
    if (_enteredPin.trim().isEmpty) {
      showCtcAppSnackBar(context, 'Please Enter PIN');
      return;
    }

    if (_enteredPin == correctPin) {
      setState(() => _pinVerified = true);
      showCtcAppSnackBar(context, 'Access Granted');
      context.push(Routes.settings);
    } else {
      setState(() => _pinVerified = false);
      showCtcAppSnackBar(context, 'Incorrect PIN');
      pinCtrl.clear();
    }
  }

  @override
  void dispose() {
    pinCtrl.dispose();
    super.dispose();
  }
}
