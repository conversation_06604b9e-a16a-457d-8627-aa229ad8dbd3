// ignore_for_file: avoid_print

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String generatedUpiString = "";

  TextEditingController ltloanintctrl = TextEditingController();
  TextEditingController stloanintctrl = TextEditingController();
  TextEditingController subintctrl = TextEditingController();
  TextEditingController divintctrl = TextEditingController();
  // TextEditingController shareintctrl = TextEditingController();
  TextEditingController defaultLtinstallAmtctrl = TextEditingController();
  TextEditingController defaultStinstallAmtctrl = TextEditingController();
  TextEditingController defaultSubsinstallAmtctrl = TextEditingController();
  TextEditingController upiIdCtrl = TextEditingController();
  TextEditingController payeeNameCtrl = TextEditingController();
  TextEditingController amountCtrl = TextEditingController();
  TextEditingController maxLtLoanAmtCtrl = TextEditingController();
  TextEditingController maxStLoanAmtCtrl = TextEditingController();
  TextEditingController maxShareValueCtrl = TextEditingController();
  TextEditingController shareCapitalCtrl = TextEditingController();
  TextEditingController generalReservedFundCtrl = TextEditingController();
  TextEditingController charityFundCtrl = TextEditingController();
  TextEditingController coopPublicityFundCtrl = TextEditingController();
  TextEditingController dividentEquivalisationFundCtrl =
      TextEditingController();

  void updateUpiString() async {
    final upiId = upiIdCtrl.text.trim();
    final payeeName = Uri.encodeComponent(payeeNameCtrl.text.trim());
    final amount = amountCtrl.text.trim();

    if (upiId.isNotEmpty && payeeName.isNotEmpty && amount.isNotEmpty) {
      generatedUpiString =
          "upi://pay?pa=$upiId&pn=$payeeName&am=$amount&cu=INR";

      await FBFireStore.settings.update({
        'upiString': generatedUpiString,
        'upiAmt': num.tryParse(amountCtrl.text.toString()),
        'upiId': upiIdCtrl.text,
        'upiPayeeName': payeeName,
      });

      // print("generatedUpiString : $generatedUpiString");
      // print("amountCtrl.text : ${amountCtrl.text}");
      // print("upiIdCtrl.text : ${upiIdCtrl.text}");
      // print("payeeName : $payeeName");
    } else {
      generatedUpiString = "";
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      ltloanintctrl.text = ctrl.settings?.ltloanInterest.toString() ?? "";
      stloanintctrl.text = ctrl.settings?.stloanInterest.toString() ?? "";
      // shareintctrl.text = ctrl.settings?.shareIntRate ?? "";
      divintctrl.text = ctrl.settings?.dividentRate ?? "";
      subintctrl.text = ctrl.settings?.subscriptionInterest ?? "";
      defaultLtinstallAmtctrl.text = ctrl.settings?.defaultLtinstallAmt ?? "";
      defaultStinstallAmtctrl.text = ctrl.settings?.defaultStinstallAmt ?? "";
      defaultSubsinstallAmtctrl.text =
          ctrl.settings?.defaultSubsinstallAmt ?? "";
      maxLtLoanAmtCtrl.text = ctrl.settings?.maxLtLoanAmt ?? "";
      maxStLoanAmtCtrl.text = ctrl.settings?.maxStLoanAmt ?? "";

      upiIdCtrl.text =
          ctrl.settings?.upiString?.split("pa=")[1].split("&")[0] ?? "";
      payeeNameCtrl.text = Uri.decodeComponent(
        ctrl.settings?.upiString?.split("pn=")[1].split("&")[0] ?? "",
      );
      amountCtrl.text =
          ctrl.settings?.upiString?.split("am=")[1].split("&")[0] ?? "";
      generatedUpiString = ctrl.settings?.upiString ?? "";

      shareCapitalCtrl.text = ctrl.settings?.shareCapital ?? "";
      maxShareValueCtrl.text = ctrl.settings?.maxShareValue ?? "";
      generalReservedFundCtrl.text = ctrl.settings?.generalReservedFund ?? "";
      charityFundCtrl.text = ctrl.settings?.charityFund ?? "";
      coopPublicityFundCtrl.text = ctrl.settings?.coopPublicityFund ?? "";
      dividentEquivalisationFundCtrl.text =
          ctrl.settings?.dividentEquivalisationFund ?? "";

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            StaggeredGrid.count(
              crossAxisSpacing: 50,
              crossAxisCount: 3,
              children: [
                CustomTextfield(
                  text: 'Long Term Loan Interest (%)',
                  controller: ltloanintctrl,
                  onFieldSubmitted: (ltloanintvalue) async {
                    // print("loanint : $ltloanintvalue");
                    await FBFireStore.settings.update({
                      'ltloanInterest':
                          num.tryParse(ltloanintvalue.toString()) ?? 0
                    });
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Short Term Loan Interest (%)',
                  controller: stloanintctrl,
                  onFieldSubmitted: (stloanintvalue) async {
                    // print("loanint : $stloanintvalue");
                    await FBFireStore.settings.update({
                      'stloanInterest':
                          num.tryParse(stloanintvalue.toString()) ?? 0
                    });
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Subscription Interest (%)',
                  controller: subintctrl,
                  onFieldSubmitted: (subintvalue) async {
                    // print("subint : $subintvalue");
                    await FBFireStore.settings
                        .update({'subscriptionInterest': subintvalue});

                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Dividend Rate (%)',
                  controller: divintctrl,
                  onFieldSubmitted: (divintvalue) async {
                    // print("divintvalue : $divintvalue");
                    await FBFireStore.settings
                        .update({'dividentRate': divintvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                // CustomTextfield(
                //   text: 'Share Interest Rate',
                //   controller: shareintctrl,
                //   onFieldSubmitted: (shareintvalue) async {
                //     print("shareintvalue : $shareintvalue");
                //     await FBFireStore.settings
                //         .update({'shareIntRate': shareintvalue});
                //     setState(() {});
                //   },
                //   enabled: true,
                // ),
                CustomTextfield(
                  text: 'Default Long Term installment Amount',
                  controller: defaultLtinstallAmtctrl,
                  onFieldSubmitted: (ltinstallvalue) async {
                    // print("defaultLtinstallAmt : $ltinstallvalue");
                    await FBFireStore.settings
                        .update({'defaultLtinstallAmt': ltinstallvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Default Short Term installment Amount',
                  controller: defaultStinstallAmtctrl,
                  onFieldSubmitted: (stinstallvalue) async {
                    // print("defaultStinstallAmt : $stinstallvalue");
                    await FBFireStore.settings
                        .update({'defaultStinstallAmt': stinstallvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Default Subscription Amount',
                  controller: defaultSubsinstallAmtctrl,
                  onFieldSubmitted: (subsvalue) async {
                    // print("defaultSubsinstallAmt : $subsvalue");
                    await FBFireStore.settings
                        .update({'defaultSubsinstallAmt': subsvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Max Lt Loan Amount',
                  controller: maxLtLoanAmtCtrl,
                  onFieldSubmitted: (maxltloanvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxLtLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'maxLtLoanAmt': maxltloanvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Max St Loan Amount',
                  controller: maxStLoanAmtCtrl,
                  onFieldSubmitted: (maxstloanvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'maxStLoanAmt': maxstloanvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Max Share Value',
                  controller: maxShareValueCtrl,
                  onFieldSubmitted: (maxsharevalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'maxShareValue': maxsharevalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Share Capital',
                  controller: shareCapitalCtrl,
                  onFieldSubmitted: (sharecapvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'shareCapital': sharecapvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'General Reserved Fund (%)',
                  controller: generalReservedFundCtrl,
                  onFieldSubmitted: (grfvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'generalReservedFund': grfvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Charity Fund (%)',
                  controller: charityFundCtrl,
                  onFieldSubmitted: (cfvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings.update({'charityFund': cfvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Co-operative Publicity Fund (%)',
                  controller: coopPublicityFundCtrl,
                  onFieldSubmitted: (cpfvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'coopPublicityFund': cpfvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
                CustomTextfield(
                  text: 'Dividend Equivalisation Fund (%)',
                  controller: dividentEquivalisationFundCtrl,
                  onFieldSubmitted: (defvalue) async {
                    // print("maxLtLoanAmtCtrl : $maxStLoanAmtCtrl");
                    await FBFireStore.settings
                        .update({'dividentEquivalisationFund': defvalue});
                    setState(() {});
                  },
                  enabled: true,
                ),
              ],
            ),
            SizedBox(height: 20),
            Text(
              "QR Code Generator : ",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      CustomTextfield(
                        text: 'Enter UPI ID',
                        controller: upiIdCtrl,
                        onFieldSubmitted: (_) => updateUpiString(),
                        enabled: true,
                      ),
                      CustomTextfield(
                        text: 'Payee Name',
                        controller: payeeNameCtrl,
                        onFieldSubmitted: (_) => updateUpiString(),
                        enabled: true,
                      ),
                      CustomTextfield(
                        text: 'Amount (INR)',
                        controller: amountCtrl,
                        keyboardType: TextInputType.number,
                        onFieldSubmitted: (_) => updateUpiString(),
                        enabled: true,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 40),
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      if (generatedUpiString.isNotEmpty) ...[
                        Text("Scan to Pay"),
                        SizedBox(height: 10),
                        QrImageView(
                          data: generatedUpiString,
                          version: QrVersions.auto,
                          size: 200,
                        ),
                      ]
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
