import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/districtoffice_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../../shared/methods.dart';

class AddDoOfficeForm extends StatefulWidget {
  const AddDoOfficeForm({super.key, required this.doOffice});

  final DistrictOfficeModel? doOffice;

  @override
  State<AddDoOfficeForm> createState() => _AddDoOfficeFormState();
}

TextEditingController namectrl = TextEditingController();
TextEditingController emailctrl = TextEditingController();
TextEditingController extraemailctrl = TextEditingController();
TextEditingController locationctrl = TextEditingController();

bool loading = false;

class _AddDoOfficeFormState extends State<AddDoOfficeForm> {
  @override
  void initState() {
    super.initState();
    if (widget.doOffice != null) {
      loadData();
    } else {
      namectrl.clear();
      emailctrl.clear();
      extraemailctrl.clear();
      locationctrl.clear();
    }
  }

  loadData() async {
    final jointEmail = widget.doOffice?.extraEmail?.join(',');
    namectrl.text = widget.doOffice?.name ?? "";
    emailctrl.text = widget.doOffice?.email ?? "";
    extraemailctrl.text = jointEmail ?? '';
    locationctrl.text = widget.doOffice?.location ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      constraints: const BoxConstraints(maxWidth: 700),
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'District Office',
                  style: TextStyle(
                      fontSize: 30,
                      color: Colors.black,
                      fontWeight: FontWeight.w600),
                ),
                // const Text(
                //   'Lorem ipsum dolor lorem ispsum dolor',
                // ),
                const SizedBox(height: 10),
                StaggeredGrid.extent(
                  maxCrossAxisExtent: 480,
                  mainAxisSpacing: 10,
                  crossAxisSpacing: 20,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Name',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextFormField(
                          controller: namectrl,
                          decoration: InputDecoration(
                            // fillColor: Colors.white,
                            filled: true,
                            prefixIcon: const Icon(
                              CupertinoIcons.person,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Name',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Email',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: emailctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.email_outlined,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Email',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'Extra Email',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: extraemailctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.email_outlined,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'Extra Email',
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 4.0),
                          child: Text(
                            'location',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        TextField(
                          controller: locationctrl,
                          maxLines: 1,
                          decoration: InputDecoration(
                            // fillColor: Colors.white,
                            filled: true,
                            prefixIcon: const Icon(
                              Icons.location_on_outlined,
                              size: 20,
                              color: Colors.black,
                            ),
                            border: OutlineInputBorder(
                                borderSide: BorderSide.none,
                                borderRadius: BorderRadius.circular(6)),
                            hintText: 'location',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 25.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      loading
                          ? const CircularProgressIndicator()
                          : ElevatedButton(
                              onPressed: () async {
                                if (namectrl.text.isEmpty ||
                                    emailctrl.text.isEmpty ||
                                    locationctrl.text.isEmpty) {
                                  showCtcAppSnackBar(
                                      context, "Please Enter All Details");
                                } else {
                                  setState(() {
                                    loading = true;
                                  });
                                  if (widget.doOffice == null) {
                                    await FBFireStore.districtoffice.add({
                                      'name': namectrl.text.toUpperCase(),
                                      'email': emailctrl.text.toLowerCase(),
                                      'location':
                                          locationctrl.text.toUpperCase(),
                                      'extraEmail': extraemailctrl.text
                                          .trim()
                                          .split(',')
                                          .map((e) => e.trim())
                                          .toList(),
                                    });
                                  } else {
                                    await FBFireStore.districtoffice
                                        .doc(widget.doOffice?.docId ?? "")
                                        .update({
                                      'name': namectrl.text.toUpperCase(),
                                      'email': emailctrl.text.toLowerCase(),
                                      'location':
                                          locationctrl.text.toUpperCase(),
                                      'extraEmail': extraemailctrl.text
                                          .trim()
                                          .split(',')
                                          .map((e) => e.trim())
                                          .toList(),
                                    });
                                  }
                                  namectrl.clear();
                                  emailctrl.clear();
                                  locationctrl.clear();
                                  extraemailctrl.clear();
                                  if (context.mounted) {
                                    setState(() {
                                      loading = false;
                                    });
                                    context.pop();
                                  }
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green.shade300,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8)),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Container(
                                  constraints: const BoxConstraints(
                                    minWidth: 100,
                                  ),
                                  child: const Center(
                                    child: Text(
                                      "Submit",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 16),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      }),
    );
  }
}
