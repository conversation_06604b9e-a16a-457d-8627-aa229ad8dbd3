import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/districtoffice_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/views/Do%20office/add_do_office_form.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class DoOfficePage extends StatefulWidget {
  const DoOfficePage({super.key});

  @override
  State<DoOfficePage> createState() => _DoOfficePageState();
}

SearchController searchctrl = SearchController();

class _DoOfficePageState extends State<DoOfficePage> {
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<DistrictOfficeModel> filtered = ctrl.districtoffice
          .where((element) =>
              element.location.contains(searchctrl.text.toUpperCase()))
          .toList();

      filtered.sort((a, b) => a.name.compareTo(b.name));

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // TextButton(
            //     onPressed: () async {
            //       for (var distOffice in ctrl.districtoffice) {
            //         await FBFireStore.districtoffice
            //             .doc()
            //             .set(Get.find<HomeCtrl>().settings!.toJson());
            //       }
            //       // await FBFireStore.testSettings
            //       //     .set(Get.find<HomeCtrl>().settings!.toJson());
            //     },
            //     child: Text("data")),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomSearchBarWidget(
                  searchController: searchctrl,
                  searchOnChanged: (p0) {
                    filtered = ctrl.districtoffice
                        .where((element) => element.location.contains(p0))
                        .toList();
                    setState(() {});
                  },
                ),
                CustomHeaderButton(
                    onPressed: () => showDialog(
                          context: context,
                          builder: (context) => const AlertDialog(
                            backgroundColor: Colors.white,
                            actions: [
                              AddDoOfficeForm(
                                doOffice: null,
                              )
                            ],
                          ),
                        ),
                    buttonName: "Add District Office")
              ],
            ),
            const SizedBox(height: 50),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0),
              child: Row(
                children: [
                  HeaderTxt(txt: "DO Name"),
                  HeaderTxt(txt: "DO Email"),
                  HeaderTxt(txt: "DO Location"),
                  HeaderTxt(txt: ""),
                ],
              ),
            ),
            const SizedBox(
              height: 30,
            ),
            ...List.generate(
              filtered.length,
              (index) {
                return Container(
                  height: 40,
                  color: index % 2 == 0 ? Colors.white : null,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Row(
                      children: [
                        Expanded(
                            child: Text(filtered[index].name.toUpperCase())),
                        Expanded(
                            child: Text(filtered[index].email.toLowerCase())),
                        Expanded(
                            child:
                                Text(filtered[index].location.toUpperCase())),
                        Expanded(
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            IconButton(
                                onPressed: () async {
                                  showDialog(
                                    context: context,
                                    builder: (context) => AlertDialog(
                                      backgroundColor: Colors.white,
                                      actions: [
                                        AddDoOfficeForm(
                                          doOffice: filtered[index],
                                        )
                                      ],
                                    ),
                                  );
                                },
                                icon: const Icon(
                                  Icons.edit,
                                  color: Color.fromARGB(255, 90, 203, 148),
                                )),
                            IconButton(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) => StatefulBuilder(
                                        builder: (context, setState2) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        title: const Text("Delete!!"),
                                        content: const Text(
                                            "Are you sure you want to delete?"),
                                        actions: [
                                          loading
                                              ? CircularProgressIndicator()
                                              : TextButton(
                                                  onPressed: () async {
                                                    final usersfetch =
                                                        await FBFireStore.users
                                                            .where(
                                                                'districtoffice',
                                                                isEqualTo:
                                                                    filtered[
                                                                            index]
                                                                        .docId)
                                                            .get();

                                                    if (usersfetch
                                                        .docs.isNotEmpty) {
                                                      return showCtcAppSnackBar(
                                                          context,
                                                          "Users of the particular District Office exits");
                                                    } else {
                                                      setState2(() {
                                                        loading = true;
                                                      });
                                                      await FBFireStore
                                                          .districtoffice
                                                          .doc(filtered[index]
                                                              .docId)
                                                          .delete();
                                                      if (context.mounted) {
                                                        context.pop();
                                                      }
                                                      setState2(() {
                                                        loading = false;
                                                      });
                                                    }
                                                  },
                                                  child: const Text("Yes")),
                                          TextButton(
                                              onPressed: () async {
                                                if (context.mounted) {
                                                  context.pop();
                                                }
                                              },
                                              child: const Text("No")),
                                        ],
                                      );
                                    }),
                                  );
                                },
                                icon: Icon(Icons.delete))
                          ],
                        )),
                      ],
                    ),
                  ),
                );
              },
            )
          ],
        ),
      );
    });
  }
}
