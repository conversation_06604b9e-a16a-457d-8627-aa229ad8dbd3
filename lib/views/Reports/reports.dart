import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:go_router/go_router.dart';

class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 30),
      child: StaggeredGrid.extent(
        maxCrossAxisExtent: 300,
        crossAxisSpacing: 20,
        mainAxisSpacing: 10,
        children: [
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.cashbook),
              child: CustomCard(
                dataTexxt: "CASHBOOK",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.trialbalance),
              child: CustomCard(
                dataTexxt: "TRIAL BALANCE",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.profitandloss),
              child: CustomCard(
                dataTexxt: "PROFIT & LOSS",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.balancesheet),
              child: CustomCard(
                dataTexxt: "BALANCE SHEET",
                color: Colors.red,
              )),

          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.subsidiaryledger),
              child: CustomCard(
                dataTexxt: "SUBSIDIARY LEDGER",
                color: Colors.green,
              )),

          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.yearlysubs),
              child: CustomCard(
                dataTexxt: "YEARLY SUBSCRIPTION",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.monthlysubs),
              child: CustomCard(
                dataTexxt: "MONTHLY SUBSCRIPTION",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.yearlyloan),
              child: CustomCard(
                dataTexxt: "YEARLY LOAN",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.monthlyloan),
              child: CustomCard(
                dataTexxt: "MONTHLY LOAN",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.yearlyshare),
              child: CustomCard(
                dataTexxt: "YEARLY SHARE",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.monthlyshare),
              child: CustomCard(
                dataTexxt: "MONTHLY SHARE",
                color: Colors.green,
              )),
          InkWell(
              highlightColor: Colors.transparent,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () => context.push(Routes.register),
              child: CustomCard(
                dataTexxt: "REGISTER",
                color: Colors.green,
              )),
          // InkWell(
          //     highlightColor: Colors.transparent,
          //     hoverColor: Colors.transparent,
          //     splashColor: Colors.transparent,
          //     onTap: () => context.push(Routes.cashbalance),
          //     child: CustomCard(
          //       dataTexxt: "CASH BALANCE",
          //       color: Colors.red,
          //     )),
          // InkWell(
          //     highlightColor: Colors.transparent,
          //     hoverColor: Colors.transparent,
          //     splashColor: Colors.transparent,
          //     onTap: () => context.push(Routes.budget),
          //     child: CustomCard(
          //       dataTexxt: "BUDGET",
          //       color: Colors.red,
          //     )),
          // InkWell(
          //     highlightColor: Colors.transparent,
          //     hoverColor: Colors.transparent,
          //     splashColor: Colors.transparent,
          //     // onTap: () => context.push(Routes.subsidiaryledger),
          //     child: CustomCard(
          //       dataTexxt: "INCOME / EXPENDITURE",
          //       color: Colors.red,
          //     )),
        ],
      ),
    );
  }
}
