// ignore_for_file: deprecated_member_use, avoid_web_libraries_in_flutter, use_build_context_synchronously

import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class MonthlyLoanPage extends StatefulWidget {
  const MonthlyLoanPage({super.key});

  @override
  State<MonthlyLoanPage> createState() => _MonthlyLoanPageState();
}

class _MonthlyLoanPageState extends State<MonthlyLoanPage> {
  late PlutoGridStateManager stateManager;

  bool isLoading = false;
  bool csvExportIsLoading = false;
  bool pdfExportIsLoading = false;

  String? monthlyLoanSelectedFinancialYear;
  int? monthlyLoanSelectedYear;
  int? monthlyLoanSelectedMonth;

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  // Generate last 6 months like in recovery.dart
  final last6Months = List.generate(6, (index) {
    final date = DateTime(DateTime.now().year, DateTime.now().month - index, 1);
    return date;
  }).reversed.toList();

  List<PlutoRow> rows = [];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'NAME OF MEMBER',
      field: 'name',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'DISTRICT OFFICE',
      field: 'district_office',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'OB LONGTERM',
      field: 'ob_lt',
      type: PlutoColumnType.text(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'OB SHORTTERM',
      field: 'ob_st',
      type: PlutoColumnType.text(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'LT PAID',
      field: 'lt_paid',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'ST PAID',
      field: 'st_paid',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'LT INSTALLMENT',
      field: 'lt_installment',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'ST INSTALLMENT',
      field: 'st_installment',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'LT RECOVERED',
      field: 'lt_recovered',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'ST RECOVERED',
      field: 'st_recovered',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'LT CLOSING BALANCE',
      field: 'lt_closing_balance',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'ST CLOSING BALANCE',
      field: 'st_closing_balance',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    monthlyLoanSelectedFinancialYear =
        TheFinancialYear.getCurrentFinancialYear();
    monthlyLoanSelectedYear = DateTime.now().year;
    monthlyLoanSelectedMonth = DateTime.now().month;
  }

  void loadData(HomeCtrl ctrl) {
    setState(() => isLoading = true);
    try {
      insertMonthlyLoanRows(ctrl.users);
    } catch (e) {
      showCtcAppSnackBar(context, "Error Loading Data");
      debugPrint('Error loading data: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void insertMonthlyLoanRows(List<UserModel> users) async {
    try {
      rows.clear();
      final ctrl = Get.find<HomeCtrl>();

      if (monthlyLoanSelectedMonth == null || monthlyLoanSelectedYear == null) {
        return;
      }

      // Get start and end dates for the selected month
      final monthStart =
          DateTime(monthlyLoanSelectedYear!, monthlyLoanSelectedMonth!, 1);
      final monthEnd =
          DateTime(monthlyLoanSelectedYear!, monthlyLoanSelectedMonth! + 1, 1)
              .subtract(Duration(days: 1));

      final loansSnapshot = await FBFireStore.loan
          .where("approvedOn", isGreaterThanOrEqualTo: monthStart)
          .where("approvedOn", isLessThanOrEqualTo: monthEnd)
          .get();

      final List<Loan> monthlyLoans =
          loansSnapshot.docs.map((e) => Loan.fromSnap(e)).toList();

      final sortedUsers = List<UserModel>.from(users)
        ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

      rows = sortedUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;

        final doName = ctrl.districtoffice.firstWhereOrNull(
          (element) => element.docId == user.districtoffice,
        );

        num obLt = entry.value.obLt ?? 0;
        num obSt = entry.value.obSt ?? 0;

        // Get monthly data for the selected month and year
        final monthlyData = ctrl.usermonthly.firstWhereOrNull((e) =>
            e.cpfNo == user.cpfNo &&
            e.selectedmonth == monthlyLoanSelectedMonth &&
            e.selectedyear == monthlyLoanSelectedYear);

        final num ltInst = monthlyData?.ltInstallment ?? 0;
        final num stInst = monthlyData?.stInstallment ?? 0;

        final List<Loan> userMonthLoans = monthlyLoans.where((loan) {
          return loan.uid == user.docId && !loan.isSettled;
        }).toList();

        final num ltPaid = userMonthLoans
            .where((loan) => loan.loanType == LoanTypes.longTerm)
            .fold(0, (sum, loan) => sum + loan.appliedLoanAmt);

        final num stPaid = userMonthLoans
            .where((loan) => loan.loanType == LoanTypes.emergencyLoan)
            .fold(0, (sum, loan) => sum + loan.appliedLoanAmt);

        final num ltRecovered = ltInst;
        final num stRecovered = stInst;

        final num ltClosingBalance =
            (obLt + ltPaid - ltRecovered).clamp(0, double.infinity);
        final num stClosingBalance =
            (obSt + stPaid - stRecovered).clamp(0, double.infinity);

        return PlutoRow(cells: {
          'sr_no': PlutoCell(value: (index + 1).toString()),
          'cpf_no': PlutoCell(value: user.cpfNo),
          'name': PlutoCell(value: user.name.toUpperCase()),
          'district_office': PlutoCell(value: doName?.name ?? ''),
          'ob_lt': PlutoCell(value: obLt.ceil().toString()),
          'ob_st': PlutoCell(value: obSt.ceil().toString()),
          'lt_paid': PlutoCell(value: ltPaid.ceil().toString()),
          'st_paid': PlutoCell(value: stPaid.ceil().toString()),
          'lt_installment': PlutoCell(value: ltInst.ceil().toString()),
          'st_installment': PlutoCell(value: stInst.ceil().toString()),
          'lt_recovered': PlutoCell(value: ltRecovered.ceil().toString()),
          'st_recovered': PlutoCell(value: stRecovered.ceil().toString()),
          'lt_closing_balance':
              PlutoCell(value: ltClosingBalance.ceil().toString()),
          'st_closing_balance':
              PlutoCell(value: stClosingBalance.ceil().toString()),
        });
      }).toList();

      Map<String, num> columnTotals = {};
      for (var col in columns) {
        // Only initialize numeric columns, skip text columns
        if (!['sr_no', 'cpf_no', 'name', 'district_office']
            .contains(col.field)) {
          columnTotals[col.field] = 0;
        }
      }

      for (var row in rows) {
        for (var field in columnTotals.keys) {
          final value =
              num.tryParse(row.cells[field]?.value?.toString() ?? '') ?? 0;
          columnTotals[field] = (columnTotals[field] ?? 0) + value;
        }
      }

      // Create total row
      Map<String, PlutoCell> totalRowCells = {};
      for (var col in columns) {
        if (col.field == 'name') {
          totalRowCells[col.field] = PlutoCell(value: 'TOTAL');
        } else if (['sr_no', 'cpf_no', 'district_office', 'ob_lt', 'ob_st']
            .contains(col.field)) {
          totalRowCells[col.field] = PlutoCell(value: '');
        } else {
          totalRowCells[col.field] = PlutoCell(
              value: columnTotals.containsKey(col.field)
                  ? columnTotals[col.field]?.ceil().toString() ?? ''
                  : '');
        }
      }

      // Add total row at the end
      rows.add(PlutoRow(cells: totalRowCells));

      stateManager.removeAllRows();
      stateManager.appendRows(rows);
      stateManager.notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error inserting loan rows');
    }
  }

  List<int> get availableMonths {
    if (monthlyLoanSelectedYear == null) return [];
    final currentYear = DateTime.now().year;
    if (monthlyLoanSelectedYear == currentYear) {
      // only months up to current month for the current year
      return List.generate(DateTime.now().month, (index) => index + 1);
    } else if (monthlyLoanSelectedYear! < currentYear) {
      // all 12 months for previous years
      return List.generate(12, (index) => index + 1);
    }
    return []; // no months for future years
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    // Year Dropdown
                    DropdownButtonFormField<int>(
                      value: monthlyLoanSelectedYear,
                      decoration: InputDecoration(
                        hintText: "Select Year",
                        constraints:
                            const BoxConstraints(maxWidth: 150, maxHeight: 45),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5)),
                      ),
                      items: yearList.map((year) {
                        return DropdownMenuItem<int>(
                          value: year,
                          child: Text(year.toString()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null && value != monthlyLoanSelectedYear) {
                          setState(() {
                            monthlyLoanSelectedYear = value;
                            // Reset month to January or max available month if year changed
                            final months = availableMonths;
                            if (months.isNotEmpty) {
                              monthlyLoanSelectedMonth = months.first;
                            } else {
                              monthlyLoanSelectedMonth = null;
                            }
                          });
                          loadData(ctrl);
                        }
                      },
                    ),

                    const SizedBox(width: 10),

// Month Dropdown showing only month names
                    DropdownButtonFormField<int>(
                      value: monthlyLoanSelectedMonth,
                      decoration: InputDecoration(
                        hintText: "Select Month",
                        constraints:
                            const BoxConstraints(maxWidth: 200, maxHeight: 45),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5)),
                      ),
                      items: availableMonths.map((month) {
                        return DropdownMenuItem<int>(
                          value: month,
                          child: Text(getMonthName(month)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null &&
                            value != monthlyLoanSelectedMonth) {
                          setState(() {
                            monthlyLoanSelectedMonth = value;
                          });
                          loadData(ctrl);
                        }
                      },
                    ),

                    // DropdownButtonHideUnderline(
                    //   child: DropdownButtonFormField<DateTime>(
                    //     focusColor: Colors.transparent,
                    //     dropdownColor: Colors.white,
                    //     value: last6Months.firstWhere(
                    //         (d) =>
                    //             d.month == monthlyLoanSelectedMonth &&
                    //             d.year == monthlyLoanSelectedYear,
                    //         orElse: () => DateTime.now()),
                    //     decoration: InputDecoration(
                    //       hintText: "Select Month",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 200, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5)),
                    //     ),
                    //     items: last6Months.map((date) {
                    //       final formatted =
                    //           "${getMonthName(date.month)} ${date.year}";
                    //       return DropdownMenuItem<DateTime>(
                    //         value: date,
                    //         child: Text(formatted),
                    //       );
                    //     }).toList(),
                    //     onChanged: (value) async {
                    //       if (value != null) {
                    //         setState(() {
                    //           monthlyLoanSelectedMonth = value.month;
                    //           monthlyLoanSelectedYear = value.year;
                    //         });

                    //         // Clear existing rows and reload data
                    //         stateManager.removeAllRows();
                    //         loadData(ctrl);
                    //       }
                    //     },
                    //   ),
                    // ),
                    // const SizedBox(width: 10),
                    // DropdownButtonHideUnderline(
                    //     child: DropdownButtonFormField(
                    //   focusColor: Colors.transparent,
                    //   dropdownColor: Colors.white,
                    //   value: monthlyLoanSelectedYear,
                    //   decoration: InputDecoration(
                    //       hintText: "Select Year",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 150, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5))),
                    //   items: List.generate(
                    //     yearList.length,
                    //     (index) {
                    //       return DropdownMenuItem(
                    //         value: yearList[index],
                    //         child: Text(yearList[index].toString()),
                    //       );
                    //     },
                    //   ),
                    //   onChanged: (value) async {
                    //     setState(() {
                    //       monthlyLoanSelectedYear = value;
                    //     });
                    //     // stateManager.removeAllRows();
                    //     loadData(ctrl);
                    //   },
                    // )),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    csvExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToCsv();
                            },
                            buttonName: 'Export to CSV',
                          ),
                    SizedBox(width: 5),
                    pdfExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToPdf();
                            },
                            buttonName: 'Export to PDF',
                          ),
                    SizedBox(width: 5),
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "MONTHLY LOAN REPORT FOR ${getMonthName(monthlyLoanSelectedMonth ?? 1).toUpperCase()} $monthlyLoanSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    loadData(ctrl);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {
                    try {
                      insertMonthlyLoanRows(ctrl.users);
                    } catch (e) {
                      debugPrint(e.toString());
                      showCtcAppSnackBar(context, 'Error loading data');
                    }
                  },
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> exportToCsv() async {
    try {
      setState(() => csvExportIsLoading = true);

      List<List<dynamic>> csvData = [];
      List<String> headers = [
        'Sr. No',
        'CPF No.',
        'Name',
        'District Office',
        'OB LT',
        'OB ST',
        'LT PAID',
        'ST PAID',
        'LT INSTALLMENT',
        'ST INSTALLMENT',
        'LT RECOVERED',
        'ST RECOVERED',
        'LT CLOSING BALANCE',
        'ST CLOSING BALANCE',
      ];

      csvData.add(headers);

      for (PlutoRow row in rows) {
        if (row.cells['name']?.value == 'TOTAL') csvData.add([]);

        List<dynamic> rowData = [
          row.cells['sr_no']?.value ?? '',
          row.cells['cpf_no']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['district_office']?.value ?? '',
          row.cells['ob_lt']?.value ?? '',
          row.cells['ob_st']?.value ?? '',
          row.cells['lt_paid']?.value ?? '',
          row.cells['st_paid']?.value ?? '',
          row.cells['lt_installment']?.value ?? '',
          row.cells['st_installment']?.value ?? '',
          row.cells['lt_recovered']?.value ?? '',
          row.cells['st_recovered']?.value ?? '',
          row.cells['lt_closing_balance']?.value ?? '',
          row.cells['st_closing_balance']?.value ?? '',
        ];

        csvData.add(rowData);
      }

      final csv = const ListToCsvConverter().convert(csvData);
      final csvWithBOM = '\uFEFF$csv';
      final bytes = utf8.encode(csvWithBOM);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_loan_${getMonthName(monthlyLoanSelectedMonth ?? 1)}_$monthlyLoanSelectedYear.csv';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'CSV file has been downloaded');
      setState(() => csvExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'CSV Export Failed');
      debugPrint('CSV Export Error: $e');
      setState(() => csvExportIsLoading = false);
    } finally {
      setState(() => csvExportIsLoading = false);
    }
  }

  Future<void> exportToPdf() async {
    try {
      setState(() => pdfExportIsLoading = true);

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(2000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          build: (pw.Context context) {
            pw.Widget cell(String text,
                {bool isHeader = false, double fontSize = 9}) {
              return pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: pw.Text(
                  text,
                  style: pw.TextStyle(
                    fontSize: fontSize,
                    fontWeight:
                        isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                  ),
                ),
              );
            }

            return [
              pw.Text(
                'MONTHLY LOAN REPORT FOR ${getMonthName(monthlyLoanSelectedMonth ?? 1).toUpperCase()} $monthlyLoanSelectedYear',
                style:
                    pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(width: 0.3),
                defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      cell('Sr. No', isHeader: true),
                      cell('CPF No.', isHeader: true),
                      cell('Name', isHeader: true),
                      cell('District Office', isHeader: true),
                      cell('OB LT', isHeader: true),
                      cell('OB ST', isHeader: true),
                      cell('LT PAID', isHeader: true),
                      cell('ST PAID', isHeader: true),
                      cell('LT INSTALLMENT', isHeader: true),
                      cell('ST INSTALLMENT', isHeader: true),
                      cell('LT RECOVERED', isHeader: true),
                      cell('ST RECOVERED', isHeader: true),
                      cell('LT CLOSING BAL', isHeader: true),
                      cell('ST CLOSING BAL', isHeader: true),
                    ],
                  ),
                  ...rows.map((row) {
                    getVal(String field) {
                      final val = row.cells[field]?.value;
                      if (val is num) return val.toStringAsFixed(0);
                      return val?.toString() ?? '';
                    }

                    return pw.TableRow(
                      children: [
                        cell(getVal('sr_no')),
                        cell(getVal('cpf_no')),
                        cell(getVal('name')),
                        cell(getVal('district_office')),
                        cell(getVal('ob_lt')),
                        cell(getVal('ob_st')),
                        cell(getVal('lt_paid')),
                        cell(getVal('st_paid')),
                        cell(getVal('lt_installment')),
                        cell(getVal('st_installment')),
                        cell(getVal('lt_recovered')),
                        cell(getVal('st_recovered')),
                        cell(getVal('lt_closing_balance')),
                        cell(getVal('st_closing_balance')),
                      ],
                    );
                  }),
                ],
              ),
            ];
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_loan_${getMonthName(monthlyLoanSelectedMonth ?? 1)}_${monthlyLoanSelectedYear}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'PDF file has been downloaded');
      setState(() => pdfExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'PDF Export Failed');
      debugPrint('PDF Export Error: $e');
      setState(() => pdfExportIsLoading = false);
    } finally {
      setState(() => pdfExportIsLoading = false);
    }
  }
}
