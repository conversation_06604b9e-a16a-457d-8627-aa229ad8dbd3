// ignore_for_file: use_build_context_synchronously, deprecated_member_use, avoid_web_libraries_in_flutter

import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class MonthlySubsPage extends StatefulWidget {
  const MonthlySubsPage({super.key});

  @override
  State<MonthlySubsPage> createState() => _MonthlySubsPageState();
}

class _MonthlySubsPageState extends State<MonthlySubsPage> {
  late PlutoGridStateManager stateManager;

  bool isLoading = false;
  bool csvExportIsLoading = false;
  bool pdfExportIsLoading = false;
  bool onSaveIsLoading = false;

  String? monthlySubSelectedFinancialYear;
  int? monthlySubSelectedYear;
  int? monthlySubSelectedMonth;

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  // Generate last 6 months like in recovery.dart
  final last6Months = List.generate(6, (index) {
    final date = DateTime(DateTime.now().year, DateTime.now().month - index, 1);
    return date;
  }).reversed.toList();

  List<PlutoRow> rows = [];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'NAME OF MEMBER',
      field: 'name',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'DISTRICT OFFICE',
      field: 'district_office',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'OB SUBSCRIPTION',
      field: 'ob_subscription',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'PAID',
      field: 'paid',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'REC',
      field: 'rec',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'MONTHLY TOTAL',
      field: 'monthly_total',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title:
          'INTEREST AMOUNT (${Get.find<HomeCtrl>().settings?.subscriptionInterest ?? 0}%)',
      field: 'interest_amount',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    monthlySubSelectedFinancialYear =
        TheFinancialYear.getCurrentFinancialYear();
    monthlySubSelectedYear = DateTime.now().year;
    monthlySubSelectedMonth = DateTime.now().month;
  }

  void loadData(HomeCtrl ctrl) {
    setState(() => isLoading = true);
    try {
      insertMonthlySubsRows(ctrl.users);
    } catch (e) {
      showCtcAppSnackBar(context, "Error Loading Data");
      debugPrint('Error loading data: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void insertMonthlySubsRows(List<UserModel> users) {
    try {
      // Clear existing rows
      rows.clear();

      if (monthlySubSelectedMonth == null || monthlySubSelectedYear == null) {
        return;
      }

      // Sort users by CPF number
      final sortedUsers = List<UserModel>.from(users)
        ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

      // Generate rows for each user
      rows = sortedUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;
        final obSubs = user.obSubs ?? 0;

        // Get district office name
        final doName = Get.find<HomeCtrl>().districtoffice.firstWhereOrNull(
              (element) => element.docId == user.districtoffice,
            );

        final ctrl = Get.find<HomeCtrl>();

        // Get monthly data for the selected month and year
        final monthlyData = Get.find<HomeCtrl>().usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthlySubSelectedMonth &&
                  e.selectedyear == monthlySubSelectedYear,
            );

        final num received = monthlyData?.subs ?? 0;
        final num monthlyTotal = obSubs + received;

        num subsInt =
            num.tryParse(ctrl.settings?.subscriptionInterest ?? '0') ?? 0;
        final interestAmount = monthlyTotal * subsInt / 1200;

        // Create and return PlutoRow
        return PlutoRow(
          cells: {
            'sr_no': PlutoCell(value: (index + 1).toString()),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name.toUpperCase()),
            'district_office': PlutoCell(value: doName?.name ?? ''),
            'ob_subscription': PlutoCell(value: obSubs),
            'paid': PlutoCell(value: 0),
            'rec': PlutoCell(value: received),
            'monthly_total': PlutoCell(value: monthlyTotal.toStringAsFixed(2)),
            'interest_amount':
                PlutoCell(value: interestAmount.toStringAsFixed(0)),
          },
        );
      }).toList();

      // Calculate totals
      final Map<String, double> totalMap = {};
      for (final row in rows) {
        row.cells.forEach((key, cell) {
          if (key == 'paid' ||
              key == 'rec' ||
              key == 'monthly_total' ||
              key == 'interest_amount') {
            final value = double.tryParse(cell.value.toString()) ?? 0.0;
            totalMap[key] = (totalMap[key] ?? 0.0) + value;
          }
        });
      }

      // Add total row
      final totalRow = PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: ''),
          'cpf_no': PlutoCell(value: ''),
          'name': PlutoCell(value: 'TOTAL'),
          'district_office': PlutoCell(value: ''),
          'ob_subscription': PlutoCell(value: ''),
          ...totalMap.map((key, value) => MapEntry(
                key,
                PlutoCell(value: value.toStringAsFixed(2)),
              )),
        },
      );

      rows.add(totalRow);

      stateManager.removeAllRows();
      stateManager.appendRows(rows);
      stateManager.notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error inserting rows');
    }
  }

  List<int> get availableMonths {
    if (monthlySubSelectedYear == null) return [];
    final currentYear = DateTime.now().year;
    if (monthlySubSelectedYear == currentYear) {
      return List.generate(DateTime.now().month, (index) => index + 1);
    } else if (monthlySubSelectedYear! < currentYear) {
      return List.generate(12, (index) => index + 1);
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField<int>(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: monthlySubSelectedMonth,
                        decoration: InputDecoration(
                          hintText: "Select Month",
                          constraints: const BoxConstraints(
                              maxWidth: 200, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: availableMonths.map((month) {
                          return DropdownMenuItem<int>(
                            value: month,
                            child: Text(getMonthName(month)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              monthlySubSelectedMonth = value;
                            });
                            try {
                              insertMonthlySubsRows(Get.find<HomeCtrl>().users);
                            } catch (e) {
                              debugPrint(e.toString());
                              showCtcAppSnackBar(context, 'Error loading data');
                            }
                          }
                        },
                      ),
                    ),
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField<int>(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: monthlySubSelectedYear,
                        decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: yearList.map((year) {
                          return DropdownMenuItem<int>(
                            value: year,
                            child: Text(year.toString()),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              monthlySubSelectedYear = value;
                              final months = availableMonths;
                              if (months.isNotEmpty) {
                                monthlySubSelectedMonth = months.first;
                              } else {
                                monthlySubSelectedMonth = null;
                              }
                            });
                            try {
                              insertMonthlySubsRows(Get.find<HomeCtrl>().users);
                            } catch (e) {
                              debugPrint(e.toString());
                              showCtcAppSnackBar(context, 'Error loading data');
                            }
                          }
                        },
                      ),
                    ),

                    // DropdownButtonHideUnderline(
                    //   child: DropdownButtonFormField<DateTime>(
                    //     focusColor: Colors.transparent,
                    //     dropdownColor: Colors.white,
                    //     value: last6Months.firstWhere(
                    //         (d) =>
                    //             d.month == monthlySubSelectedMonth &&
                    //             d.year == monthlySubSelectedYear,
                    //         orElse: () => DateTime.now()),
                    //     decoration: InputDecoration(
                    //       hintText: "Select Month",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 200, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5)),
                    //     ),
                    //     items: last6Months.map((date) {
                    //       final formatted =
                    //           "${getMonthName(date.month)} ${date.year}";
                    //       return DropdownMenuItem<DateTime>(
                    //         value: date,
                    //         child: Text(formatted),
                    //       );
                    //     }).toList(),
                    //     onChanged: (value) async {
                    //       if (value != null) {
                    //         setState(() {
                    //           monthlySubSelectedMonth = value.month;
                    //           monthlySubSelectedYear = value.year;
                    //         });

                    //         try {
                    //           insertMonthlySubsRows(ctrl.users);
                    //         } catch (e) {
                    //           debugPrint(e.toString());
                    //           showCtcAppSnackBar(context, 'Error loading data');
                    //         }
                    //       }
                    //     },
                    //   ),
                    // ),
                    // const SizedBox(width: 10),
                    // DropdownButtonHideUnderline(
                    //     child: DropdownButtonFormField(
                    //   focusColor: Colors.transparent,
                    //   dropdownColor: Colors.white,
                    //   value: monthlySubSelectedYear,
                    //   decoration: InputDecoration(
                    //       hintText: "Select Year",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 150, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5))),
                    //   items: List.generate(
                    //     yearList.length,
                    //     (index) {
                    //       return DropdownMenuItem(
                    //         value: yearList[index],
                    //         child: Text(yearList[index].toString()),
                    //       );
                    //     },
                    //   ),
                    //   onChanged: (value) async {
                    //     setState(() {
                    //       monthlySubSelectedYear = value;
                    //     });
                    //     try {
                    //       insertMonthlySubsRows(ctrl.users);
                    //     } catch (e) {
                    //       debugPrint(e.toString());
                    //       showCtcAppSnackBar(context, 'Error loading data');
                    //     }
                    //   },
                    // )),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    csvExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await exportCsv(),
                            buttonName: 'Export to CSV',
                          ),
                    SizedBox(width: 5),
                    pdfExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await exportPdf(),
                            buttonName: 'Export to PDF',
                          ),
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "MONTHLY SUBSCRIPTION REPORT FOR ${getMonthName(monthlySubSelectedMonth ?? 1).toUpperCase()} $monthlySubSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    loadData(ctrl);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> exportCsv() async {
    try {
      setState(() => csvExportIsLoading = true);

      // Create CSV data
      List<List<dynamic>> csvData = [];

      // Add headers
      List<String> headers = [
        'Sr. No',
        'CPF No.',
        'Name',
        'District Office',
        'OB Subscription',
        'PAID',
        'REC',
        'MONTHLY TOTAL',
        'Interest Amount',
      ];

      csvData.add(headers);

      // Add row data
      for (PlutoRow row in rows) {
        if (row.cells['name']?.value == 'TOTAL') {
          // Add empty row before total
          csvData.add([]);
        }

        List<dynamic> rowData = [];

        // Basic info
        rowData.add(row.cells['sr_no']?.value ?? '');
        rowData.add(row.cells['cpf_no']?.value ?? '');
        rowData.add(row.cells['name']?.value ?? '');
        rowData.add(row.cells['district_office']?.value ?? '');

        // OB Subscription
        var obValue = row.cells['ob_subscription']?.value ?? '';
        if (obValue is num) obValue = obValue.toStringAsFixed(2);
        rowData.add(obValue);

        // Monthly values
        var paidValue = row.cells['paid']?.value ?? '';
        var recValue = row.cells['rec']?.value ?? '';
        var monthlyTotalValue = row.cells['monthly_total']?.value ?? '';

        if (paidValue is num) paidValue = paidValue.toStringAsFixed(2);
        if (recValue is num) recValue = recValue.toStringAsFixed(2);
        if (monthlyTotalValue is num) {
          monthlyTotalValue = monthlyTotalValue.toStringAsFixed(2);
        }

        rowData.add(paidValue);
        rowData.add(recValue);
        rowData.add(monthlyTotalValue);

        // Interest amount
        var interestValue = row.cells['interest_amount']?.value ?? '';

        if (interestValue is num) {
          interestValue = interestValue.toStringAsFixed(2);
        }

        rowData.add(interestValue);

        csvData.add(rowData);
      }

      // Convert to CSV string with UTF8 BOM for Excel compatibility
      final csv = const ListToCsvConverter().convert(csvData);
      final csvWithBOM = '\uFEFF$csv';

      // Create blob for web download
      final bytes = utf8.encode(csvWithBOM);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_subscription_${getMonthName(monthlySubSelectedMonth ?? 1)}_$monthlySubSelectedYear.csv';

      html.document.body!.children.add(anchor);
      anchor.click();

      // Cleanup
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'CSV file has been downloaded');
      setState(() => csvExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'Export Failed');
      setState(() => csvExportIsLoading = false);

      debugPrint('CSV Export Error: $e');
    } finally {
      setState(() => csvExportIsLoading = false);
    }
  }

  Future<void> exportPdf() async {
    try {
      setState(() => pdfExportIsLoading = true);

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(2000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          build: (pw.Context context) {
            // Helper for consistent cell padding and style
            pw.Widget cell(String text,
                {bool isHeader = false, double fontSize = 9}) {
              return pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: pw.Text(
                  text,
                  style: pw.TextStyle(
                    fontSize: fontSize,
                    fontWeight:
                        isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                  ),
                ),
              );
            }

            return [
              pw.Text(
                'MONTHLY SUBSCRIPTION REPORT FOR ${getMonthName(monthlySubSelectedMonth ?? 1).toUpperCase()} $monthlySubSelectedYear',
                style:
                    pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(width: 0.3),
                defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
                children: [
                  // Column Headers
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      cell('Sr. No', isHeader: true),
                      cell('CPF No.', isHeader: true),
                      cell('Name', isHeader: true),
                      cell('District Office', isHeader: true),
                      cell('OB Subscription', isHeader: true),
                      cell('PAID', isHeader: true),
                      cell('REC', isHeader: true),
                      cell('MONTHLY TOTAL', isHeader: true),
                      cell('Interest Amount', isHeader: true),
                    ],
                  ),

                  // Data Rows
                  ...rows.map((row) {
                    getVal(String field) {
                      final val = row.cells[field]?.value;
                      if (val is num) return val.toStringAsFixed(0);
                      return val?.toString() ?? '';
                    }

                    return pw.TableRow(
                      children: [
                        cell(getVal('sr_no')),
                        cell(getVal('cpf_no')),
                        cell(getVal('name')),
                        cell(getVal('district_office')),
                        cell(getVal('ob_subscription')),
                        cell(getVal('paid')),
                        cell(getVal('rec')),
                        cell(getVal('monthly_total')),
                        cell(getVal('interest_amount')),
                      ],
                    );
                  }),
                ],
              ),
            ];
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_subscription_${getMonthName(monthlySubSelectedMonth ?? 1)}_${monthlySubSelectedYear}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'PDF file has been downloaded');
      setState(() => pdfExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'PDF Export Failed');
      setState(() => pdfExportIsLoading = false);

      debugPrint('PDF Export Error: $e');
    } finally {
      setState(() => pdfExportIsLoading = false);
    }
  }
}
