// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;

class PnlPage extends StatefulWidget {
  const PnlPage({super.key});

  @override
  State<PnlPage> createState() => _PnlPageState();
}

class _PnlPageState extends State<PnlPage> {
  late PlutoGridStateManager stateManager;
  String? pnlSelectedFinancialYear;
  int? pnlselectedyear; // Keep for backward compatibility
  bool dataloading = false;
  bool dataSaving = false;

  bool pdfSaving = false;
  bool csvSaving = false;

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  final hctrl = Get.find<HomeCtrl>();

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'EXPENDITURE',
      field: 'expenditure',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'AMOUNT',
      field: 'examt',
      type: PlutoColumnType.number(),
    ),
    PlutoColumn(
      title: 'INCOME',
      field: 'income',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'AMOUNT',
      field: 'incomeamt',
      type: PlutoColumnType.number(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    pnlSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    pnlselectedyear = TheFinancialYear.getCurrentFinancialYearStartYear();
    getData();
  }

  Future<void> getData() async {
    setState(() {
      dataloading = true;
    });

    List<PlutoRow> pnlRows = [];

    try {
      final startOfYear =
          TheFinancialYear.getFinancialYearStartDate(pnlselectedyear!);
      final endOfYear =
          TheFinancialYear.getFinancialYearEndDate(pnlselectedyear!);

      final expSnapshot = await FBFireStore.expense
          .where("cash", isEqualTo: false)
          .where("isIncome", isEqualTo: false)
          .where("createdAt", isGreaterThanOrEqualTo: startOfYear)
          .where("createdAt", isLessThan: endOfYear)
          .where("selectedYear", isEqualTo: pnlselectedyear.toString())
          .get();

      final incSnapshot = await FBFireStore.expense
          .where("isIncome", isEqualTo: true)
          .where("createdAt", isGreaterThanOrEqualTo: startOfYear)
          .where("createdAt", isLessThan: endOfYear)
          .where("selectedYear", isEqualTo: pnlselectedyear.toString())
          .get();

      final expDocs = expSnapshot.docs;
      final incDocs = incSnapshot.docs;

      int maxLength =
          expDocs.length > incDocs.length ? expDocs.length : incDocs.length;

      double totalExpense = 0;
      double totalIncome = 0;

      for (int i = 0; i < maxLength; i++) {
        final exp = i < expDocs.length ? expDocs[i] : null;
        final inc = i < incDocs.length ? incDocs[i] : null;

        final expAmount = exp?.get('amount') ?? 0;
        final incAmount = inc?.get('amount') ?? 0;

        totalExpense += expAmount;
        totalIncome += incAmount;

        pnlRows.add(PlutoRow(cells: {
          "expenditure": PlutoCell(value: exp?.get('name') ?? ''),
          "examt": PlutoCell(value: expAmount),
          "income": PlutoCell(value: inc?.get('name') ?? ''),
          "incomeamt": PlutoCell(value: incAmount),
        }));
      }

      // Add empty row at the end
      // pnlRows.add(PlutoRow(cells: {
      //   "expenditure": PlutoCell(value: ''),
      //   "examt": PlutoCell(value: 0),
      //   "income": PlutoCell(value: ''),
      //   "incomeamt": PlutoCell(value: 0),
      // }));

      bool hasValidData = pnlRows.any((row) {
        final expenseTitle =
            row.cells['expenditure']?.value?.toString().trim() ?? '';
        final expenseAmt = row.cells['examt']?.value ?? 0;
        final incomeTitle = row.cells['income']?.value?.toString().trim() ?? '';
        final incomeAmt = row.cells['incomeamt']?.value ?? 0;

        return (expenseTitle.isNotEmpty && expenseAmt != 0) ||
            (incomeTitle.isNotEmpty && incomeAmt != 0);
      });

      // double dividendAmount = 0.0;
      // double totalShares = 0.0;

      // for (var user in hctrl.users) {
      //   final shares = user.totalShares ?? 0.0;
      //   print("name : ${user.name}");
      //   print("cpfNo : ${user.cpfNo}");
      //   print("shares : $shares");
      //   print("-=-=-=-=-=-=-=-=-=-=-=");
      //   totalShares += shares;
      //   print("totalShares : $totalShares");
      // }

      // final dividendAmt = (totalShares *
      //         (num.tryParse(hctrl.settings?.dividentRate ?? "") ?? 0)) /
      //     1200;

      double totalProgressiveShares = 0.0;

      final int selectedYear = pnlselectedyear ?? DateTime.now().year;

// Sum user's cumulative progressive total (monthly totals) for the year
      for (var user in hctrl.users) {
        num userProgressiveTotal = 0;

        // Sum monthly share values for the user for the selected financial year
        final userMonthlyData = Get.find<HomeCtrl>().usermonthly.where(
            (e) => e.cpfNo == user.cpfNo && e.selectedyear == selectedYear);

        // Cumulative total over months
        for (var monthData in userMonthlyData) {
          userProgressiveTotal += (monthData.shareValue ?? 0);
        }

        totalProgressiveShares += userProgressiveTotal;
      }

      final dividendRate =
          num.tryParse(hctrl.settings?.dividentRate ?? '0') ?? 0;

      final dividendAmt = (totalProgressiveShares * dividendRate) / 1200;

      // print("dividendAmount : $dividendAmt");

      final profitAndDividend = (totalIncome - totalExpense) + dividendAmt;

      pnlRows.addAll([
        PlutoRow(cells: {
          "expenditure": PlutoCell(value: "PROFIT AND DIVIDENT"),
          "examt": PlutoCell(value: profitAndDividend),
          "income": PlutoCell(value: ""),
          "incomeamt": PlutoCell(value: ''),
        }),
      ]);

      if (hasValidData) {
        pnlRows.add(PlutoRow(cells: {
          "expenditure": PlutoCell(value: 'TOTAL'),
          "examt": PlutoCell(value: totalExpense),
          "income": PlutoCell(value: 'TOTAL'),
          "incomeamt": PlutoCell(value: totalIncome),
        }));
      }

      setState(() {
        stateManager.removeAllRows();
        stateManager.appendRows(pnlRows);
      });
    } catch (e) {
      debugPrint("Error fetching PnL data: $e");
    } finally {
      setState(() {
        dataloading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(children: [
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            DropdownButtonHideUnderline(
              child: DropdownButtonFormField(
                focusColor: Colors.transparent,
                dropdownColor: Colors.white,
                value: pnlselectedyear,
                decoration: InputDecoration(
                  hintText: "Select Year",
                  constraints:
                      const BoxConstraints(maxWidth: 150, maxHeight: 45),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(5)),
                ),
                items: List.generate(
                  yearList.length,
                  (index) => DropdownMenuItem(
                    value: yearList[index],
                    child: Text(yearList[index].toString()),
                  ),
                ),
                onChanged: (value) async {
                  setState(() {
                    pnlselectedyear = value;
                  });
                  await getData();
                },
              ),
            ),
            Row(
              children: [
                csvSaving
                    ? const CircularProgressIndicator()
                    : CustomHeaderButton(
                        onPressed: () async {
                          await exportToCSV();
                        },
                        buttonName: 'Export to CSV',
                      ),
                const SizedBox(width: 5),
                pdfSaving
                    ? const CircularProgressIndicator()
                    : CustomHeaderButton(
                        onPressed: () async {
                          await exportToPDF();
                        },
                        buttonName: 'Export to PDF',
                      ),
                const SizedBox(width: 5),
                dataSaving
                    ? const CircularProgressIndicator()
                    : CustomHeaderButton(
                        onPressed: () => savePnLData(setState),
                        buttonName: 'SAVE',
                      ),
              ],
            ),
          ]),
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "PROFIT AND LOSS ACCOUNT FOR THE YEAR $pnlselectedyear",
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 16))),
          Expanded(
              child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent),
                  ),
                  child: PlutoGrid(
                      columns: columns,
                      rows: [
                        PlutoRow(cells: {
                          "expenditure": PlutoCell(value: ""),
                          "examt": PlutoCell(value: 0),
                          "income": PlutoCell(value: ""),
                          "incomeamt": PlutoCell(value: 0)
                        })
                      ],
                      onLoaded: (PlutoGridOnLoadedEvent event) async {
                        stateManager = event.stateManager;
                        stateManager.setShowColumnFilter(true);
                        await getData();
                      },
                      onChanged: (PlutoGridOnChangedEvent event) {
                        bool hasEmptyRow = stateManager.rows.any((row) {
                          return row.cells.values.every((cell) {
                            final val = cell.value;
                            return val == null ||
                                val.toString().trim().isEmpty ||
                                val == 0;
                          });
                        });

                        if (!hasEmptyRow) {
                          stateManager.appendRows([
                            PlutoRow(cells: {
                              'expenditure': PlutoCell(value: ''),
                              'examt': PlutoCell(value: 0),
                              'income': PlutoCell(value: ''),
                              'incomeamt': PlutoCell(value: 0),
                            }),
                          ]);
                        }
                      },
                      configuration: const PlutoGridConfiguration(
                          columnSize: PlutoGridColumnSizeConfig(
                              autoSizeMode: PlutoAutoSizeMode.equal),
                          scrollbar: PlutoGridScrollbarConfig(
                              draggableScrollbar: false,
                              scrollbarThickness: PlutoScrollbar
                                  .defaultThicknessWhileDragging)))))
        ]));
  }

  void savePnLData(Function refresh) async {
    try {
      refresh(() {
        dataSaving = true;
      });

      await Future.delayed(Duration(milliseconds: 50));

      final hasValidDataInRows = stateManager.rows.any((row) {
        final expenseTitle = row.cells['expenditure']?.value?.toString().trim();
        final expenseAmt = row.cells['examt']?.value;
        final incomeTitle = row.cells['income']?.value?.toString().trim();
        final incomeAmt = row.cells['incomeamt']?.value;

        final isRowEmpty = (expenseTitle?.isEmpty ?? true) &&
            (expenseAmt == null || expenseAmt == 0) &&
            (incomeTitle?.isEmpty ?? true) &&
            (incomeAmt == null || incomeAmt == 0);

        return !isRowEmpty;
      });

      if (!hasValidDataInRows) {
        setState(() {
          dataSaving = false;
        });
        showCtcAppSnackBar(context, "No valid income or expense data to save.");
        return;
      }

      final pnlDataSnapshot = await FBFireStore.profitandloss
          .where("year", isEqualTo: pnlselectedyear)
          .get();

      if (pnlDataSnapshot.docs.isNotEmpty) {
        final docRef = pnlDataSnapshot.docs.first.reference;

        final entriesSnapshot = await docRef.collection("pnlEntries").get();
        for (var doc in entriesSnapshot.docs) {
          await doc.reference.delete();
        }

        await _savePnLEntries(docRef);

        setState(() {
          dataSaving = false;
        });
        showCtcAppSnackBar(context, "Profit and Loss data updated");
      } else {
        setState(() {
          dataSaving = true;
        });
        // print("PnL data does not exist in Firebase - saving new data");
        final docRef = FBFireStore.profitandloss.doc();
        await docRef.set({
          'year': pnlselectedyear,
          'updatedAt': null,
        });
        await _savePnLEntries(docRef);

        refresh(() {
          dataSaving = false;
        });

        showCtcAppSnackBar(context, "Profit and Loss data saved");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> _savePnLEntries(docRef) async {
    double totalExpense = 0;
    double totalIncome = 0;

    for (var row in stateManager.rows) {
      final expenseTitle =
          row.cells['expenditure']?.value?.toString().trim() ?? '';
      final expenseAmt = row.cells['examt']?.value;
      final incomeTitle = row.cells['income']?.value?.toString().trim() ?? '';
      final incomeAmt = row.cells['incomeamt']?.value;

      final isTotalRow = expenseTitle.toUpperCase() == 'TOTAL' ||
          incomeTitle.toUpperCase() == 'TOTAL' ||
          (expenseTitle.isEmpty &&
              incomeTitle.isEmpty &&
              (expenseAmt != null || incomeAmt != null));

      if (isTotalRow) {
        // print("Skipping TOTAL row");
        continue;
      }

      final isRowEmpty = (expenseTitle.isEmpty) &&
          (expenseAmt == null || expenseAmt == 0) &&
          (incomeTitle.isEmpty) &&
          (incomeAmt == null || incomeAmt == 0);

      if (isRowEmpty) continue;

      if (expenseTitle.isNotEmpty && expenseAmt != null && expenseAmt != 0) {
        await docRef.collection("pnlEntries").add({
          'isExpense': true,
          'title': expenseTitle,
          'amount': expenseAmt,
          'createdAt': Timestamp.now(),
        });
        totalExpense += (expenseAmt is num) ? expenseAmt : 0;
      }

      if (incomeTitle.isNotEmpty && incomeAmt != null && incomeAmt != 0) {
        await docRef.collection("pnlEntries").add({
          'isExpense': false,
          'title': incomeTitle,
          'amount': incomeAmt,
          'createdAt': Timestamp.now(),
        });
        totalIncome += (incomeAmt is num) ? incomeAmt : 0;
      }
    }

    await docRef.update({
      'totalExpense': totalExpense,
      'totalIncome': totalIncome,
      'updatedAt': Timestamp.now(),
    });

    // print('Totals saved: totalExpense=$totalExpense, totalIncome=$totalIncome');
  }

  Future<void> exportToCSV() async {
    setState(() {
      csvSaving = true;
    });
    try {
      savePnLData(setState);
      final csv = StringBuffer();
      csv.writeln('"EXPENDITURE","AMOUNT","INCOME","AMOUNT"');

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() {
          csvSaving = false;
        });
        return;
      }

      for (var row in stateManager.rows) {
        final exp = row.cells['expenditure']?.value?.toString() ?? '';
        final expAmt = row.cells['examt']?.value?.toString() ?? '';
        final inc = row.cells['income']?.value?.toString() ?? '';
        final incAmt = row.cells['incomeamt']?.value?.toString() ?? '';
        csv.writeln('"$exp","$expAmt","$inc","$incAmt"');
      }

      final bytes = html.Blob([csv.toString()]);
      final url = html.Url.createObjectUrlFromBlob(bytes);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'profit_and_loss_$pnlselectedyear.csv')
        ..click();
      html.Url.revokeObjectUrl(url);
      setState(() {
        csvSaving = false;
      });
    } catch (e, stackTrace) {
      debugPrint("Error exporting CSV: $e\n$stackTrace");
      setState(() {
        csvSaving = false;
      });
      showCtcAppSnackBar(context, "Failed to export CSV");
    }
  }

  Future<void> exportToPDF() async {
    setState(() {
      pdfSaving = true;
    });
    try {
      savePnLData(setState);

      final pdf = pw.Document();
      final headers = ['EXPENDITURE', 'AMOUNT', 'INCOME', 'AMOUNT'];

      if (stateManager.rows.isEmpty) {
        setState(() {
          pdfSaving = false;
        });
        showCtcAppSnackBar(context, "No data to export");
        return;
      }

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'PROFIT AND LOSS ACCOUNT FOR THE YEAR $pnlselectedyear',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 20),
                pw.Table.fromTextArray(
                  headers: headers,
                  cellAlignment: pw.Alignment.centerLeft,
                  headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  data: stateManager.rows.map((row) {
                    final exp =
                        row.cells['expenditure']?.value?.toString() ?? '';
                    final expAmt = row.cells['examt']?.value?.toString() ?? '';
                    final inc = row.cells['income']?.value?.toString() ?? '';
                    final incAmt =
                        row.cells['incomeamt']?.value?.toString() ?? '';
                    return [exp, expAmt, inc, incAmt];
                  }).toList(),
                ),
              ],
            );
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'profit_and_loss_$pnlselectedyear.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);

      setState(() {
        pdfSaving = false;
      });
    } catch (e, stackTrace) {
      debugPrint("Error exporting PDF: $e\n$stackTrace");
      setState(() {
        pdfSaving = false;
      });
      showCtcAppSnackBar(context, "Failed to export PDF");
    }
  }
}
