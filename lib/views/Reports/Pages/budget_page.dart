import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:pluto_grid/pluto_grid.dart';

class BudgetPage extends StatefulWidget {
  const BudgetPage({super.key});

  @override
  State<BudgetPage> createState() => _BudgetPageState();
}

class _BudgetPageState extends State<BudgetPage> {
  late PlutoGridStateManager stateManager;

  String? yearlyBudgetSelectedFinancialYear;
  int? yearlyBudgetSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'Sr.No.',
      field: 'sr_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'PARTICULARS',
      field: 'particulars',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'INCOME',
      field: 'income',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'EXPENDITURE',
      field: 'name',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    )
  ];

  @override
  void initState() {
    super.initState();
    yearlyBudgetSelectedYear = DateTime.now().year;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return
          // SizedBox();
          Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: yearlyBudgetSelectedYear,
                        decoration: InputDecoration(
                            hintText: "Select Year",
                            constraints: const BoxConstraints(
                                maxWidth: 150, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        items: List.generate(
                          yearList.length,
                          (index) {
                            return DropdownMenuItem(
                              value: yearList[index],
                              child: Text(yearList[index].toString()),
                            );
                          },
                        ),
                        onChanged: (value) async {
                          setState(() => yearlyBudgetSelectedYear = value);

                          stateManager.removeAllRows();

                          // stateManager.appendRows(rows);

                          // setState(() {
                          //   slIsLoading = false;
                          // });
                        })),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomHeaderButton(
                      onPressed: () async {
                        // exportToCSV();
                      },
                      buttonName: 'Export to CSV',
                      // child: Text(''),
                    ),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                      onPressed: () async {
                        // await exportToPDF();
                      },
                      buttonName: 'Export to PDF',
                      // child: Text(''),
                    ),
                    SizedBox(width: 5),
                    // slIsLoading
                    //     ? CircularProgressIndicator()
                    //     :
                    CustomHeaderButton(
                        onPressed: () async {
                          // await slOnSave(ctrl);
                        },
                        buttonName: 'SAVE')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text("BUDGET FOR THE YEAR $yearlyBudgetSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                // height: size.height, //  - 30,
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      columnSize: PlutoGridColumnSizeConfig(
                          autoSizeMode: PlutoAutoSizeMode.equal),
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
