// ignore_for_file: use_build_context_synchronously, deprecated_member_use, avoid_web_libraries_in_flutter

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/subsidiary_ledger_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:typed_data';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:intl/intl.dart';

class SubsidiaryLedgerPage extends StatefulWidget {
  const SubsidiaryLedgerPage({super.key});

  @override
  State<SubsidiaryLedgerPage> createState() => _SubsidiaryLedgerPageState();
}

final num startMonth = 4;

final List<PlutoColumnGroup> columnGroups = [
  PlutoColumnGroup(
    title: 'PLACE OF POSTING',
    fields: ['district_office'],
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumnGroup(
    title: 'OPENING BALANCE OF LOAN',
    fields: ['ob_subscription', 'ob_lt', 'ob_st', 'ob_shares'],
  ),
  ...getDynamicMonthNumbers().map((monthNum) {
    final prefix = monthShortName(monthNum);
    final isColored = monthNum % 2 == 0;
    return PlutoColumnGroup(
      title: prefix,
      fields: [
        'subs_$monthNum',
        'lt_installment_$monthNum',
        'st_installment_$monthNum',
        'interest_$monthNum',
        'penalty_$monthNum',
      ],
      backgroundColor: isColored ? Color(0xffC9E9D2) : null,
    );
  }),
];

final List<PlutoColumn> columns = [
  PlutoColumn(
    title: 'SR. NO',
    field: 'sr_no',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'CPF NO.',
    field: 'cpf_no',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'NAME OF MEMBER',
    field: 'name',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'DISTRICT OFFICE',
    field: 'district_office',
    type: PlutoColumnType.text(),
    enableEditingMode: false,
    backgroundColor: Color(0xffC9E9D2),
  ),
  PlutoColumn(
    title: 'OB SUBSCRIPTION',
    field: 'ob_subscription',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB LONG TERM',
    field: 'ob_lt',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB SHORT TERM',
    field: 'ob_st',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  PlutoColumn(
    title: 'OB SHARES',
    field: 'ob_shares',
    type: PlutoColumnType.text(),
    enableEditingMode: true,
  ),
  ...getDynamicMonthNumbers().expand((monthNum) {
    final prefix = monthShortName(monthNum);
    final isColored = monthNum % 2 == 0;
    return [
      PlutoColumn(
        title: '$prefix SUBSCRIPTION',
        field: 'subs_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix LT INSTALLMENT',
        field: 'lt_installment_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix ST INSTALLMENT',
        field: 'st_installment_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix INTEREST',
        field: 'interest_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
      PlutoColumn(
        title: '$prefix PENALTY',
        field: 'penalty_$monthNum',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      ),
    ];
  })
];

class _SubsidiaryLedgerPageState extends State<SubsidiaryLedgerPage> {
  List<MonthlyLedgerModel> ledgerList = [];

  String? subLedgerSelectedFinancialYear;
  int? subLedgerSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  bool slIsLoading = false;
  bool slCSVLoading = false;
  bool slPDFLoading = false;

  List<PlutoRow> rows = [];

  late PlutoGridStateManager stateManager;

  // User selection for individual subsidiary ledger
  UserModel? selectedUser;
  bool userLedgerLoading = false;

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    subLedgerSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    subLedgerSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    DropdownButtonHideUnderline(
                        child: DropdownButtonFormField(
                            focusColor: Colors.transparent,
                            dropdownColor: Colors.white,
                            value: subLedgerSelectedYear,
                            decoration: InputDecoration(
                                hintText: "Select Year",
                                constraints: const BoxConstraints(
                                    maxWidth: 150, maxHeight: 45),
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(5))),
                            items: List.generate(
                              yearList.length,
                              (index) {
                                return DropdownMenuItem(
                                  value: yearList[index],
                                  child: Text(yearList[index].toString()),
                                );
                              },
                            ),
                            onChanged: (value) async {
                              setState(() => subLedgerSelectedYear = value);

                              stateManager.removeAllRows();

                              await checkAndApplyData(ctrl);

                              final List<UserModel> sortedUsers =
                                  List<UserModel>.from(ctrl.users)
                                    ..sort(
                                        (a, b) => a.cpfNo.compareTo(b.cpfNo));

                              List<PlutoRow> newRows = [];

                              for (final entry in sortedUsers.asMap().entries) {
                                final index = entry.key;
                                final user = entry.value;

                                final doName =
                                    ctrl.districtoffice.firstWhereOrNull(
                                  (element) =>
                                      element.docId == user.districtoffice,
                                );

                                final Map<int, dynamic> monthlyData = {};
                                for (int i = 0; i < 12; i++) {
                                  final monthNum = getMonthNum(i);
                                  final match =
                                      ctrl.usermonthly.firstWhereOrNull(
                                    (e) =>
                                        e.cpfNo == user.cpfNo &&
                                        e.selectedmonth == monthNum &&
                                        e.selectedyear == subLedgerSelectedYear,
                                  );
                                  monthlyData[monthNum] = match;
                                }

                                if (monthlyData.values
                                    .every((val) => val == null)) {
                                  continue;
                                }

                                final Map<String, PlutoCell> monthCells = {};
                                for (int i = 0; i < 12; i++) {
                                  final monthNum = getMonthNum(i);
                                  final data = monthlyData[monthNum];

                                  monthCells.addAll({
                                    'subs_$monthNum':
                                        PlutoCell(value: data?.subs ?? ''),
                                    'lt_installment_$monthNum': PlutoCell(
                                        value: data?.ltInstallment ?? ''),
                                    'st_installment_$monthNum': PlutoCell(
                                        value: data?.stInstallment ?? ''),
                                    'interest_$monthNum':
                                        PlutoCell(value: data?.interest ?? ''),
                                    'penalty_$monthNum':
                                        PlutoCell(value: data?.penalty ?? ''),
                                  });
                                }

                                newRows.add(PlutoRow(cells: {
                                  'sr_no': PlutoCell(value: index + 1),
                                  'cpf_no': PlutoCell(value: user.cpfNo),
                                  'name':
                                      PlutoCell(value: user.name.toUpperCase()),
                                  'district_office':
                                      PlutoCell(value: doName?.name ?? ""),
                                  'ob_subscription': PlutoCell(value: ''),
                                  'ob_lt': PlutoCell(value: ''),
                                  'ob_st': PlutoCell(value: ''),
                                  'ob_shares': PlutoCell(value: ''),
                                  ...monthCells,
                                }));
                              }

                              if (newRows.isNotEmpty) {
                                final Map<String, double> totalMap = {};

                                for (final row in newRows) {
                                  row.cells.forEach((key, cell) {
                                    if (key.startsWith('subs_') ||
                                        key.startsWith('lt_installment_') ||
                                        key.startsWith('st_installment_') ||
                                        key.startsWith('interest_') ||
                                        key.startsWith('penalty_')) {
                                      final val = double.tryParse(
                                              cell.value.toString()) ??
                                          0.0;
                                      totalMap[key] =
                                          (totalMap[key] ?? 0.0) + val;
                                    }
                                  });
                                }

                                final totalRow = PlutoRow(cells: {
                                  'sr_no': PlutoCell(value: ''),
                                  'cpf_no': PlutoCell(value: ''),
                                  'name': PlutoCell(value: 'TOTAL'),
                                  'district_office': PlutoCell(value: ''),
                                  'ob_subscription': PlutoCell(value: ''),
                                  'ob_lt': PlutoCell(value: ''),
                                  'ob_st': PlutoCell(value: ''),
                                  'ob_shares': PlutoCell(value: ''),
                                  ...generateTotalCells(totalMap),
                                });

                                newRows.add(totalRow);
                              }

                              rows = newRows;
                              stateManager.appendRows(rows);

                              setState(() {
                                slIsLoading = false;
                              });
                            })),
                    SizedBox(width: 10),
                    SizedBox(
                      width: 380,
                      child: DropdownSearch<UserModel>(
                        selectedItem: selectedUser,
                        onChanged: (value) {
                          setState(() {
                            selectedUser = value;
                          });
                        },
                        decoratorProps: DropDownDecoratorProps(
                          decoration: InputDecoration(
                            labelText: "Select Member for Individual Ledger",
                            hintText: "Choose a member",
                            prefixIcon: Icon(Icons.person_search, size: 20),
                            constraints: const BoxConstraints(maxHeight: 45),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade400),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: Colors.grey.shade400),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide:
                                  BorderSide(color: Colors.blue, width: 2),
                            ),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                        ),
                        popupProps: PopupProps.menu(
                          showSearchBox: true,
                          searchFieldProps: TextFieldProps(
                            decoration: InputDecoration(
                              hintText: 'Search User',
                              prefixIcon: Icon(Icons.search, size: 20),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                          ),
                          fit: FlexFit.loose,
                          constraints: BoxConstraints(maxHeight: 400),
                        ),
                        itemAsString: (item) =>
                            "${item.name.toUpperCase()} (CPF: ${item.cpfNo})",
                        items: (filter, loadProps) {
                          if (filter.isEmpty) {
                            return ctrl.users
                                .where((user) => !user.archived)
                                .toList()
                              ..sort((a, b) => a.name.compareTo(b.name));
                          }
                          final lowerFilter = filter.toLowerCase();
                          return ctrl.users
                              .where((user) =>
                                  !user.archived &&
                                  (user.cpfNo
                                          .toString()
                                          .contains(lowerFilter) ||
                                      user.name
                                          .toLowerCase()
                                          .contains(lowerFilter) ||
                                      user.employeeNo
                                          .toString()
                                          .contains(lowerFilter) ||
                                      (user.email
                                              ?.toLowerCase()
                                              .contains(lowerFilter) ??
                                          false)))
                              .toList()
                            ..sort((a, b) => a.name.compareTo(b.name));
                        },
                        compareFn: (item1, item2) => item1.docId == item2.docId,
                      ),
                    ),
                    if (selectedUser != null) ...[
                      SizedBox(width: 5),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            selectedUser = null;
                          });
                        },
                        icon: const Icon(Icons.clear, size: 20),
                      ),
                      SizedBox(width: 5),
                      userLedgerLoading
                          ? Container(
                              padding: EdgeInsets.all(8),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                  SizedBox(width: 8),
                                  Text("Generating...",
                                      style: TextStyle(fontSize: 12)),
                                ],
                              ),
                            )
                          : ElevatedButton.icon(
                              onPressed: () => exportUserSubsidiaryLedger(ctrl),
                              icon: Icon(Icons.file_download, size: 18),
                              label: Text("Download Ledger"),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green.shade600,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                    ],
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomHeaderButton(
                      onPressed: () async => exportToCSV(),
                      buttonName: 'Export to CSV',
                    ),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                      onPressed: () async => await exportToPDF(),
                      buttonName: 'Export to PDF',
                    ),
                    SizedBox(width: 5),
                    slIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => await slOnSave(ctrl),
                            buttonName: 'SAVE')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "SUBSIDIARY LEDGER FOR THE YEAR $subLedgerSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columnGroups: columnGroups,
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);

                    final ctrl = Get.find<HomeCtrl>();
                    final newRows = generateSubsidiaryLedgerRows(
                      ctrl.users,
                      ctrl,
                      subLedgerSelectedYear,
                    );

                    rows = newRows;
                    stateManager.appendRows(rows);
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {
                    final field = event.column.field;

                    if (field == 'ob_subscription' ||
                        field == 'ob_lt' ||
                        field == 'ob_st' ||
                        field == 'ob_shares') {
                      // Editable opening balance fields logic here if needed
                    }
                  },
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> slOnSave(HomeCtrl ctrl) async {
    setState(() => slIsLoading = true);
    if (stateManager.rows.isEmpty) {
      showCtcAppSnackBar(context, "No Data to Save");
      setState(() => slIsLoading = false);
      return;
    }
    try {
      final fbSubsidiaryLedgerSnap = await FBFireStore.subsidiaryledger
          .where("year", isEqualTo: subLedgerSelectedYear)
          .get();

      final snapshot = fbSubsidiaryLedgerSnap;

      if (snapshot.docs.isNotEmpty) {
        final existingDoc = snapshot.docs.first;
        final mainSubLedRef = FBFireStore.subsidiaryledger.doc(existingDoc.id);

        await mainSubLedRef.update({'updatedAt': DateTime.now()});

        final userList = ctrl.users;
        for (var user in userList) {
          final monthlyEntriesMap = List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            final monthlyData = ctrl.usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthNum &&
                  e.selectedyear == subLedgerSelectedYear,
            );

            final userRows = rows.where(
              (r) => r.cells['cpf_no']?.value == user.cpfNo,
            );

            double subsTotal = 0;
            double ltTotal = 0;
            double stTotal = 0;
            double interestTotal = 0;
            double penaltyTotal = 0;

            for (final row in userRows) {
              final subs = double.tryParse(
                      row.cells['subs_$monthNum']?.value.toString() ?? '0') ??
                  0;
              final ltInst = double.tryParse(
                      row.cells['lt_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final stInst = double.tryParse(
                      row.cells['st_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final interest = double.tryParse(
                      row.cells['interest_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final penalty = double.tryParse(
                      row.cells['penalty_$monthNum']?.value.toString() ??
                          '0') ??
                  0;

              subsTotal += subs;
              ltTotal += ltInst;
              stTotal += stInst;
              interestTotal += interest;
              penaltyTotal += penalty;
            }

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: monthlyData?.subs ?? 0,
              longTermInstallment: monthlyData?.ltInstallment ?? 0,
              shortTermInstallment: monthlyData?.stInstallment ?? 0,
              interest: monthlyData?.interest ?? 0,
              penalty: monthlyData?.penalty ?? 0,
              totalinterest: interestTotal,
              totallongTermInstallment: ltTotal,
              totalshortTermInstallment: stTotal,
              totalsubscription: subsTotal,
              totalPenalty: penaltyTotal,
              obSubs: user.obSubs ?? 0,

              // userObRow != null
              //     ? num.tryParse(userObRow.cells['ob_subscription']?.value
              //                 .toString() ??
              //             "0") ??
              //         0
              //     : 0,
              obShares: user.obShares ?? 0,

              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_shares']?.value.toString() ??
              //                 "0") ??
              //         0
              //     : 0,
              obst: user.obSt ?? 0,
              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_st']?.value.toString() ?? "0") ??
              //         0
              //     : 0,
              oblt: user.obLt ?? 0,
              // userObRow != null
              //     ? num.tryParse(
              //             userObRow.cells['ob_lt']?.value.toString() ?? "0") ??
              //         0
              //     : 0,
            ).toJson();
          });

          await mainSubLedRef.collection("userData").doc(user.docId).set({
            'uId': user.docId,
            'cpfNo': user.cpfNo,
            'name': user.name,
            'districtOffice': user.districtoffice,
            'monthlyEntries': monthlyEntriesMap,
          }, SetOptions(merge: true));
        }

        showCtcAppSnackBar(context, "Subsidiary Ledger Updated Successfully");
      } else {
        final userList = ctrl.users;
        final mainSubLedRef = FBFireStore.subsidiaryledger.doc();

        await mainSubLedRef.set({
          'year': subLedgerSelectedYear ??
              TheFinancialYear.getCurrentYearForDatabase(),
          'updatedAt': null,
        });

        for (var user in userList) {
          final monthlyEntriesMap = List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            final monthlyData = ctrl.usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthNum &&
                  e.selectedyear == subLedgerSelectedYear,
            );

            final userRows =
                rows.where((r) => r.cells['cpf_no']?.value == user.cpfNo);
            final userObRow = rows.firstWhereOrNull(
                (r) => r.cells['cpf_no']?.value == user.cpfNo);

            double subsTotal = 0;
            double ltTotal = 0;
            double stTotal = 0;
            double interestTotal = 0;
            double penaltyTotal = 0;

            for (final row in userRows) {
              final subs = double.tryParse(
                      row.cells['subs_$monthNum']?.value.toString() ?? '0') ??
                  0;
              final ltInst = double.tryParse(
                      row.cells['lt_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final stInst = double.tryParse(
                      row.cells['st_installment_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final interest = double.tryParse(
                      row.cells['interest_$monthNum']?.value.toString() ??
                          '0') ??
                  0;
              final penalty = double.tryParse(
                      row.cells['penalty_$monthNum']?.value.toString() ??
                          '0') ??
                  0;

              subsTotal += subs;
              ltTotal += ltInst;
              stTotal += stInst;
              interestTotal += interest;
              penaltyTotal += penalty;
            }

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: monthlyData?.subs ?? 0,
              longTermInstallment: monthlyData?.ltInstallment ?? 0,
              shortTermInstallment: monthlyData?.stInstallment ?? 0,
              interest: monthlyData?.interest ?? 0,
              penalty: monthlyData?.penalty ?? 0,
              totalinterest: interestTotal,
              totallongTermInstallment: ltTotal,
              totalshortTermInstallment: stTotal,
              totalsubscription: subsTotal,
              totalPenalty: penaltyTotal,
              obSubs: userObRow != null
                  ? num.tryParse(userObRow.cells['ob_subscription']?.value
                              .toString() ??
                          "0") ??
                      0
                  : 0,
              obShares: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_shares']?.value.toString() ??
                              "0") ??
                      0
                  : 0,
              obst: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_st']?.value.toString() ?? "0") ??
                      0
                  : 0,
              oblt: userObRow != null
                  ? num.tryParse(
                          userObRow.cells['ob_lt']?.value.toString() ?? "0") ??
                      0
                  : 0,
            ).toJson();
          });

          await FBFireStore.subsidiaryledger
              .doc(mainSubLedRef.id)
              .collection("userData")
              .doc(user.docId)
              .set({
            'uId': user.docId,
            'cpfNo': user.cpfNo,
            'name': user.name,
            'districtOffice': user.districtoffice,
            'monthlyEntries': monthlyEntriesMap,
          });
        }

        showCtcAppSnackBar(context, "Subsidiary Ledger Saved Successfuly");
      }
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(
          context, "Error occurred while saving subsidiary ledger.");
    }

    setState(() => slIsLoading = false);
  }

  Future<void> checkAndApplyData(HomeCtrl ctrl) async {
    try {
      setState(() {
        ledgerList = [];
      });

      final fbSubsidiaryLedgerSnap = await FBFireStore.subsidiaryledger
          .where("year", isEqualTo: subLedgerSelectedYear)
          .get();

      if (fbSubsidiaryLedgerSnap.size == 1) {
        final userDataSnap = await FBFireStore.subsidiaryledger
            .doc(fbSubsidiaryLedgerSnap.docs.first.id)
            .collection("userData")
            .get();

        List<MonthlyLedgerModel> templedgerList = userDataSnap.docs
            .map((doc) {
              final uId = doc.id;
              final data = doc.data();

              final matchedUser =
                  ctrl.users.firstWhereOrNull((user) => user.docId == uId);
              if (matchedUser == null) {
                return <MonthlyLedgerModel>[];
              }

              return List.generate(12, (i) {
                final monthNum = getMonthNum(i);
                final List monthlyEntries = data['monthlyEntries'] ?? [];

                final entryForMonth =
                    (monthlyEntries).cast<Map<String, dynamic>>().firstWhere(
                          (entry) => entry["month"] == monthNum.toString(),
                          orElse: () => <String, dynamic>{},
                        );

                if (entryForMonth.isEmpty) {
                  return MonthlyLedgerModel(
                    month: monthNum.toString(),
                    subscription: 0,
                    longTermInstallment: 0,
                    shortTermInstallment: 0,
                    interest: 0,
                    totalinterest: 0,
                    totallongTermInstallment: 0,
                    totalshortTermInstallment: 0,
                    totalsubscription: 0,
                    penalty: 0,
                    totalPenalty: 0,
                    obSubs: rows.isNotEmpty
                        ? num.tryParse(
                                rows.first.cells['ob_subscription']?.value) ??
                            0
                        : 0,
                    obShares: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_shares']?.value) ??
                            0
                        : 0,
                    obst: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                        : 0,
                    oblt: rows.isNotEmpty
                        ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                        : 0,
                  );
                }

                return MonthlyLedgerModel(
                  month: monthNum.toString(),
                  subscription: (entryForMonth["subscription"] ?? 0),
                  longTermInstallment:
                      (entryForMonth["longTermInstallment"] ?? 0),
                  shortTermInstallment:
                      (entryForMonth["shortTermInstallment"] ?? 0),
                  interest: (entryForMonth["interest"] ?? 0),
                  penalty: (entryForMonth["penalty"] ?? 0),
                  totalinterest: (entryForMonth["totalinterest"] ?? 0),
                  totallongTermInstallment:
                      (entryForMonth["totallongTermInstallment"] ?? 0),
                  totalshortTermInstallment:
                      (entryForMonth["totalshortTermInstallment"] ?? 0),
                  totalsubscription: (entryForMonth["totalsubscription"] ?? 0),
                  totalPenalty: (entryForMonth["totalPenalty"] ?? 0),
                  obSubs: rows.isNotEmpty
                      ? num.tryParse(
                              rows.first.cells['ob_subscription']?.value) ??
                          0
                      : 0,
                  obShares: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_shares']?.value) ?? 0
                      : 0,
                  obst: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                      : 0,
                  oblt: rows.isNotEmpty
                      ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                      : 0,
                );
              });
            })
            .expand((e) => e)
            .toList();

        setState(() {
          ledgerList = templedgerList;
        });
      } else if (fbSubsidiaryLedgerSnap.docs.isEmpty) {
        rows.clear();

        final sortedUsers = List<UserModel>.from(ctrl.users)
          ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

        final emptyLedgerList = sortedUsers.expand((user) {
          return List.generate(12, (i) {
            final monthNum = getMonthNum(i);

            return MonthlyLedgerModel(
              month: monthNum.toString(),
              subscription: 0,
              longTermInstallment: 0,
              shortTermInstallment: 0,
              interest: 0,
              totalinterest: 0,
              totallongTermInstallment: 0,
              totalshortTermInstallment: 0,
              totalsubscription: 0,
              penalty: 0,
              totalPenalty: 0,
              obSubs: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_subscription']?.value) ??
                      0
                  : 0,
              obShares: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_shares']?.value) ?? 0
                  : 0,
              obst: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_st']?.value) ?? 0
                  : 0,
              oblt: rows.isNotEmpty
                  ? num.tryParse(rows.first.cells['ob_lt']?.value) ?? 0
                  : 0,
            );
          });
        }).toList();

        setState(() {
          ledgerList = emptyLedgerList;
        });
      }

      if (mounted) setState(() {});
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> exportToCSV() async {
    setState(() => slCSVLoading = true);

    try {
      final csv = StringBuffer();

      String monthShortName(int month) {
        const names = [
          '',
          'JAN',
          'FEB',
          'MAR',
          'APR',
          'MAY',
          'JUN',
          'JUL',
          'AUG',
          'SEP',
          'OCT',
          'NOV',
          'DEC'
        ];
        return names[month];
      }

      final headers = [
        'SR. NO',
        'CPF NO.',
        'NAME OF MEMBER',
        'DISTRICT OFFICE',
        'OB SUBSCRIPTION',
        'OB LONG TERM',
        'OB SHORT TERM',
        'OB SHARES',
        ...List.generate(12, (i) {
          final monthNum = getMonthNum(i);
          final m = monthShortName(monthNum);
          return [
            '$m Subscription',
            '$m LT Installment',
            '$m ST Installment',
            '$m Interest',
            '$m Penalty'
          ];
        }).expand((x) => x),
      ];

      csv.writeln(headers.map((e) => '"$e"').join(','));

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() => slCSVLoading = false);
        return;
      }

      for (var row in rows) {
        final rowData = [
          row.cells['sr_no']?.value ?? '',
          row.cells['cpf_no']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['district_office']?.value ?? '',
          row.cells['ob_subscription']?.value ?? '',
          row.cells['ob_lt']?.value ?? '',
          row.cells['ob_st']?.value ?? '',
          row.cells['ob_shares']?.value ?? '',
          ...List.generate(12, (i) {
            final monthNum = getMonthNum(i);
            return [
              row.cells['subs_$monthNum']?.value ?? '',
              row.cells['lt_installment_$monthNum']?.value ?? '',
              row.cells['st_installment_$monthNum']?.value ?? '',
              row.cells['interest_$monthNum']?.value ?? '',
              row.cells['penalty_$monthNum']?.value ?? '',
            ];
          }).expand((x) => x),
        ].map((e) => '"$e"');

        csv.writeln(rowData.join(','));
      }

      final csvBytes = html.Blob([csv.toString()]);
      final csvUrl = html.Url.createObjectUrlFromBlob(csvBytes);

      html.AnchorElement(href: csvUrl)
        ..setAttribute('download', 'subsidiary_ledger.csv')
        ..click();

      html.Url.revokeObjectUrl(csvUrl);
      setState(() => slCSVLoading = false);
    } catch (e) {
      setState(() => slCSVLoading = false);
      showCtcAppSnackBar(context, "Failed to export CSV: $e");
    } finally {
      setState(() => slCSVLoading = false);
    }
  }

  Future<void> exportToPDF() async {
    setState(() => slPDFLoading = true);

    try {
      final pdf = pw.Document();

      String monthShortName(int month) {
        const names = [
          '',
          'JAN',
          'FEB',
          'MAR',
          'APR',
          'MAY',
          'JUN',
          'JUL',
          'AUG',
          'SEP',
          'OCT',
          'NOV',
          'DEC'
        ];
        return names[month];
      }

      final headers = [
        'SR.NO',
        'CPF NO.',
        'NAME',
        'DISTRICT OFFICE',
        'OB SUB',
        'OB LONG TERM',
        'OB SHORT TERM',
        'OB SHARES',
        ...List.generate(12, (i) {
          final monthNum = getMonthNum(i);
          final prefix = monthShortName(monthNum);
          return [
            '$prefix SUB',
            '$prefix LT INSTALLMENT',
            '$prefix ST INSTALLMENT',
            '$prefix INT',
            '$prefix PENALTY',
          ];
        }).expand((e) => e)
      ];

      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        setState(() => slPDFLoading = false);
        return;
      }

      final data = <List<String>>[
        headers,
        for (var row in rows)
          [
            row.cells['sr_no']?.value.toString() ?? '',
            row.cells['cpf_no']?.value.toString() ?? '',
            row.cells['name']?.value.toString() ?? '',
            row.cells['district_office']?.value.toString() ?? '',
            row.cells['ob_subscription']?.value.toString() ?? '',
            row.cells['ob_lt']?.value.toString() ?? '',
            row.cells['ob_st']?.value.toString() ?? '',
            row.cells['ob_shares']?.value.toString() ?? '',
            ...List.generate(12, (i) {
              final monthNum = getMonthNum(i);
              return [
                row.cells['subs_$monthNum']?.value.toString() ?? '',
                row.cells['lt_installment_$monthNum']?.value.toString() ?? '',
                row.cells['st_installment_$monthNum']?.value.toString() ?? '',
                row.cells['interest_$monthNum']?.value.toString() ?? '',
                row.cells['penalty_$monthNum']?.value.toString() ?? '',
              ];
            }).expand((e) => e),
          ]
      ];

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(4000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          margin: pw.EdgeInsets.all(10),
          build: (context) => [
            pw.Text(
              textAlign: pw.TextAlign.center,
              'Subsidiary Ledger Report',
              style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
              children: [
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColors.grey300),
                  children: headers.map((header) {
                    return pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text(
                        header,
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 7,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    );
                  }).toList(),
                ),
                ...data.sublist(1).map((row) {
                  return pw.TableRow(
                    children: row.map((cell) {
                      return pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(
                          cell,
                          style: pw.TextStyle(fontSize: 7),
                          textAlign: pw.TextAlign.center,
                          softWrap: false,
                          overflow: pw.TextOverflow.clip,
                        ),
                      );
                    }).toList(),
                  );
                }),
              ],
            ),
          ],
        ),
      );

      final pdfBytes = await pdf.save();
      final blob = html.Blob([Uint8List.fromList(pdfBytes)]);
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download', 'subsidiary_ledger.pdf')
        ..click();

      html.Url.revokeObjectUrl(url);
      setState(() => slPDFLoading = false);
    } catch (e) {
      setState(() => slPDFLoading = false);
      showCtcAppSnackBar(context, "Failed to export PDF: $e");
    } finally {
      setState(() => slPDFLoading = false);
    }
  }

  Future<void> exportUserSubsidiaryLedger(HomeCtrl ctrl) async {
    debugPrint("=== Starting exportUserSubsidiaryLedger ===");

    if (selectedUser == null || subLedgerSelectedYear == null) {
      debugPrint(
          "ERROR: Missing selection - User: $selectedUser, Year: $subLedgerSelectedYear");
      showCtcAppSnackBar(context, "Please select both year and member");
      return;
    }

    setState(() => userLedgerLoading = true);
    debugPrint("Loading state set to true");

    try {
      final user = selectedUser!;
      final year = subLedgerSelectedYear!;
      debugPrint(
          "Processing user: ${user.name} (CPF: ${user.cpfNo}) for year: $year");

      // Get district office name
      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );
      debugPrint("District Office: ${doName?.name ?? 'Not Found'}");

      // Get user monthly records for the selected year
      final userMonthlyRecords = ctrl.usermonthly
          .where((record) =>
              record.cpfNo == user.cpfNo && record.selectedyear == year)
          .toList()
        ..sort(
            (a, b) => (a.selectedmonth ?? 0).compareTo(b.selectedmonth ?? 0));

      debugPrint("Found ${userMonthlyRecords.length} monthly records for user");

      // Calculate opening balances based on user registration and financial year logic
      final openingBalances = calculateOpeningBalances(user, year);
      final obSubs = openingBalances['obSubs']!;
      final obShares = openingBalances['obShares']!;
      final obLtLoan = openingBalances['obLt']!;
      final obStLoan = openingBalances['obSt']!;

      debugPrint("User registration date: ${user.registrationDate}");
      debugPrint("Financial year: ${year - 1}-04-01 to $year-03-31");
      debugPrint(
          "Final Opening Balances - Subs: $obSubs, Shares: $obShares, LT: $obLtLoan, ST: $obStLoan");

      // Calculate totals
      final totals = <String, num>{
        'loanPaidLtTotal': 0,
        'loanPaidStTotal': 0,
        'subscriptionTotal': 0,
        'ltInstallmentTotal': 0,
        'stInstallmentTotal': 0,
        'interestTotal': 0,
        'totalAmtPaidTotal': 0,
      };

      num recShares = 0;

      for (final record in userMonthlyRecords) {
        totals['loanPaidLtTotal'] =
            totals['loanPaidLtTotal']! + (record.loanPaidLt ?? 0);
        totals['loanPaidStTotal'] =
            totals['loanPaidStTotal']! + (record.loanPaidst ?? 0);
        totals['subscriptionTotal'] =
            totals['subscriptionTotal']! + record.subscriptionPaid;
        totals['ltInstallmentTotal'] =
            totals['ltInstallmentTotal']! + record.longTermInstalmentPaid;
        totals['stInstallmentTotal'] =
            totals['stInstallmentTotal']! + record.shortTermInstalmentPaid;
        totals['interestTotal'] =
            totals['interestTotal']! + (record.interest ?? 0);
        totals['totalAmtPaidTotal'] =
            totals['totalAmtPaidTotal']! + (record.installmentRec ?? 0);
        recShares += (user.totalShares ?? 0) -
            obShares; // Using shareValue instead of sharesPaid
      }

      debugPrint("Calculated totals: $totals");
      debugPrint("Received shares during year: $recShares");

      // Generate PDF content
      final pdf = pw.Document();

      // String getMonthName(int monthNum) {
      //   const monthNames = [
      //     '', // index 0 (not used)
      //     'JANUARY', // 1
      //     'FEBRUARY', // 2
      //     'MARCH', // 3
      //     'APRIL', // 4
      //     'MAY', // 5
      //     'JUNE', // 6
      //     'JULY', // 7
      //     'AUGUST', // 8
      //     'SEPTEMBER', // 9
      //     'OCTOBER', // 10
      //     'NOVEMBER', // 11
      //     'DECEMBER' // 12
      //   ];
      //   return monthNames.length > monthNum ? monthNames[monthNum] : 'UNKNOWN';
      // }

      String getBalanceAsOnDate() {
        final now = DateTime.now();
        final months = [
          '',
          'JAN',
          'FEB',
          'MAR',
          'APR',
          'MAY',
          'JUN',
          'JUL',
          'AUG',
          'SEP',
          'OCT',
          'NOV',
          'DEC'
        ];
        return 'BALANCE AS ON ${now.day}-${months[now.month]}-${now.year}';
      }

      final dateFormatter = DateFormat('dd-MM-yyyy');

      debugPrint("Starting PDF generation");

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: pw.EdgeInsets.all(20),
          build: (context) => pw.Container(
            padding: const pw.EdgeInsets.all(12),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Centered Header Lines
                pw.Center(
                  child: pw.Text(
                    'FCI EMPLOYEES CO OPERATIVE CREDIT SOCIETY : BARODA',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                pw.Center(
                  child: pw.Text(
                    'MEMBER SUBSIDIARY LEDGER FOR THE FINANCIAL YEAR $year-${year + 1}',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 11,
                    ),
                  ),
                ),
                pw.SizedBox(height: 8),

                // Name of Society Member - Single Row, Centered
                pw.Center(
                  child: pw.Text(
                    'NAME OF SOCIETY MEMBER : ${user.name.toUpperCase()}',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 13,
                      color: PdfColors.black,
                    ),
                  ),
                ),
                pw.SizedBox(height: 12),

                // OBs Container Box centered horizontally
                pw.Align(
                  alignment: pw.Alignment.center,
                  child: pw.Container(
                    width: 200,
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey),
                    ),
                    padding: const pw.EdgeInsets.symmetric(
                        vertical: 8, horizontal: 8),
                    child: pw.Column(
                      children: [
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text('OB SUBS :',
                                style: pw.TextStyle(fontSize: 10)),
                            pw.Text(obSubs.toString(),
                                style: pw.TextStyle(
                                    fontWeight: pw.FontWeight.bold,
                                    fontSize: 10)),
                          ],
                        ),
                        pw.SizedBox(height: 4),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text('OB SHARES :',
                                style: pw.TextStyle(fontSize: 10)),
                            pw.Text(obShares.toString(),
                                style: pw.TextStyle(
                                    fontWeight: pw.FontWeight.bold,
                                    fontSize: 10)),
                          ],
                        ),
                        pw.SizedBox(height: 4),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text('OBLT :',
                                style: pw.TextStyle(fontSize: 10)),
                            pw.Text(obLtLoan.toString(),
                                style: pw.TextStyle(
                                    fontWeight: pw.FontWeight.bold,
                                    fontSize: 10)),
                          ],
                        ),
                        pw.SizedBox(height: 4),
                        pw.Row(
                          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                          children: [
                            pw.Text('OBST :',
                                style: pw.TextStyle(fontSize: 10)),
                            pw.Text(obStLoan.toString(),
                                style: pw.TextStyle(
                                    fontWeight: pw.FontWeight.bold,
                                    fontSize: 10)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                pw.SizedBox(height: 15),

                // Ledger Table (with all columns & totals)
                pw.Table.fromTextArray(
                  headers: [
                    'MONTH',
                    'DATE',
                    'OBLT',
                    'OBST',
                    'LOAN PAID LT',
                    'LOAN PAID ST',
                    'SUBSCRIPTION',
                    'LT INSTALLMENT',
                    'ST INSTALLMENT',
                    'INTEREST',
                    'TOTAL AMT PAID',
                    'LTCB',
                    'STCB'
                  ],
                  data: [
                    for (final record in userMonthlyRecords)
                      [
                        getMonthName(record.installmentRecDate?.month ?? 0),
                        record.installmentRecDate != null
                            ? dateFormatter.format(record.installmentRecDate!)
                            : '-',
                        (record.obLt ?? 0).toString(),
                        (record.obSt ?? 0).toString(),
                        (record.loanPaidLt ?? 0).toString(),
                        (record.loanPaidst ?? 0).toString(),
                        record.subscriptionPaid.toString(),
                        record.longTermInstalmentPaid.toString(),
                        record.shortTermInstalmentPaid.toString(),
                        (record.interest ?? 0).toString(),
                        (record.installmentRec ?? 0).toString(),
                        (record.ltCb ?? 0).toString(),
                        (record.stCb ?? 0).toString(),
                      ],
                    [
                      'TOTAL',
                      '',
                      '',
                      '',
                      totals['loanPaidLtTotal'].toString(),
                      totals['loanPaidStTotal'].toString(),
                      totals['subscriptionTotal'].toString(),
                      totals['ltInstallmentTotal'].toString(),
                      totals['stInstallmentTotal'].toString(),
                      totals['interestTotal'].toString(),
                      totals['totalAmtPaidTotal'].toString(),
                      '',
                      '',
                    ]
                  ],
                  cellAlignment: pw.Alignment.center,
                  headerStyle:
                      pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 8),
                  cellStyle: pw.TextStyle(fontSize: 8),
                  border: pw.TableBorder.all(color: PdfColors.grey300),
                ),

                pw.SizedBox(height: 15),

                // Summary Table
                pw.Container(
                  width: double.infinity,
                  child: pw.Column(
                    children: [
                      pw.Text('SUMMARY',
                          style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold, fontSize: 10)),
                      pw.Table.fromTextArray(
                        headers: [
                          'PARTICULAR',
                          'OB',
                          'RECEIVED DURING THE YEAR',
                          'PAID DURING THE YEAR',
                          getBalanceAsOnDate()
                        ],
                        data: [
                          [
                            'SUBSCRIPTION',
                            obSubs.toString(),
                            totals['subscriptionTotal'].toString(),
                            '0',
                            (obSubs + (totals['subscriptionTotal'] ?? 0))
                                .toString()
                          ],
                          [
                            'SHARE',
                            obShares.toString(),
                            recShares.toString(),
                            '0',
                            (obShares + recShares).toString()
                          ],
                          [
                            'LT LOAN',
                            obLtLoan.toString(),
                            totals['ltInstallmentTotal'].toString(),
                            totals['loanPaidLtTotal'].toString(),
                            (obLtLoan -
                                    (totals['ltInstallmentTotal'] ?? 0) +
                                    (totals['loanPaidLtTotal'] ?? 0))
                                .toString()
                          ],
                          [
                            'ST LOAN',
                            obStLoan.toString(),
                            totals['stInstallmentTotal'].toString(),
                            totals['loanPaidStTotal'].toString(),
                            (obStLoan -
                                    (totals['stInstallmentTotal'] ?? 0) +
                                    (totals['loanPaidStTotal'] ?? 0))
                                .toString()
                          ],
                        ],
                        cellAlignment: pw.Alignment.center,
                        headerStyle: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, fontSize: 8),
                        cellStyle: pw.TextStyle(fontSize: 8),
                        border: pw.TableBorder.all(color: PdfColors.grey300),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      debugPrint("PDF generation completed, saving file");

      // Save and download PDF
      final pdfBytes = await pdf.save();
      final blob = html.Blob([Uint8List.fromList(pdfBytes)]);
      final url = html.Url.createObjectUrlFromBlob(blob);

      html.AnchorElement(href: url)
        ..setAttribute('download',
            'Member_Subsidiary_Ledger_${user.cpfNo}_${user.name.replaceAll(' ', '_')}_$year-${year + 1}.pdf')
        ..click();

      html.Url.revokeObjectUrl(url);

      debugPrint("PDF download initiated successfully");
      showCtcAppSnackBar(
          context, "Member subsidiary ledger downloaded successfully");

      // Close dropdown and clear selection
      setState(() {
        selectedUser = null;
      });
      debugPrint("Dropdown closed and selection cleared");
    } catch (e, stackTrace) {
      debugPrint("ERROR in exportUserSubsidiaryLedger: $e");
      debugPrint("Stack trace: $stackTrace");
      showCtcAppSnackBar(
          context, "Failed to export member ledger: ${e.toString()}");
    } finally {
      setState(() => userLedgerLoading = false);
      debugPrint("Loading state set to false");
      debugPrint("=== exportUserSubsidiaryLedger completed ===");
    }
  }

  List<PlutoRow> generateSubsidiaryLedgerRows(
      List<UserModel> users, HomeCtrl ctrl, int? selectedYear) {
    final sortedUsers = List<UserModel>.from(users)
      ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

    final rows = sortedUsers.asMap().entries.map((entry) {
      final index = entry.key;
      final user = entry.value;

      final doName = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == user.districtoffice,
      );

      // Calculate correct opening balances for this user and year
      final openingBalances =
          calculateOpeningBalances(user, selectedYear ?? 2025);

      final Map<int, dynamic> monthlyData = {};

      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final match = ctrl.usermonthly.firstWhereOrNull(
          (e) =>
              e.cpfNo == user.cpfNo &&
              e.selectedmonth == monthNum &&
              e.selectedyear == selectedYear,
        );
        monthlyData[monthNum] = match;
      }

      final Map<String, PlutoCell> monthCells = {};
      for (int i = 0; i < 12; i++) {
        final monthNum = getMonthNum(i);
        final data = monthlyData[monthNum];

        monthCells.addAll({
          'subs_$monthNum': PlutoCell(value: formatCeil(data?.subs)),
          'lt_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.ltInstallment)),
          'st_installment_$monthNum':
              PlutoCell(value: formatCeil(data?.stInstallment)),
          'interest_$monthNum': PlutoCell(value: formatCeil(data?.interest)),
          'penalty_$monthNum': PlutoCell(value: formatCeil(data?.penalty)),
        });
      }

      return PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: index + 1),
          'cpf_no': PlutoCell(value: user.cpfNo),
          'name': PlutoCell(value: user.name.toUpperCase()),
          'district_office': PlutoCell(value: doName?.name ?? ""),
          'ob_subscription': PlutoCell(value: openingBalances['obSubs']),
          'ob_lt': PlutoCell(value: openingBalances['obLt']),
          'ob_st': PlutoCell(value: openingBalances['obSt']),
          'ob_shares': PlutoCell(value: openingBalances['obShares']),
          ...monthCells,
        },
      );
    }).toList();

    final Map<String, double> totalMap = {};
    for (final row in rows) {
      row.cells.forEach((key, cell) {
        if (key.startsWith('subs_') ||
            key.startsWith('lt_installment_') ||
            key.startsWith('st_installment_') ||
            key.startsWith('interest_') ||
            key.startsWith('penalty_')) {
          final val = double.tryParse(cell.value.toString()) ?? 0.0;
          totalMap[key] = (totalMap[key] ?? 0.0) + val;
        }
      });
    }

    final totalRow = PlutoRow(
      cells: {
        'sr_no': PlutoCell(value: ''),
        'cpf_no': PlutoCell(value: ''),
        'name': PlutoCell(value: 'TOTAL'),
        'district_office': PlutoCell(value: ''),
        'ob_subscription': PlutoCell(value: ''),
        'ob_lt': PlutoCell(value: ''),
        'ob_st': PlutoCell(value: ''),
        'ob_shares': PlutoCell(value: ''),
        ...generateTotalCells(totalMap),
      },
    );

    rows.add(totalRow);
    return rows;
  }
}

String formatCeil(dynamic val) {
  if (val == null) return '';
  final parsed = num.tryParse(val.toString());
  return parsed != null ? parsed.ceil().toString() : '';
}

Map<String, PlutoCell> generateTotalCells(Map<String, double> totalMap) {
  final Map<String, PlutoCell> cells = {};
  for (int i = 0; i < 12; i++) {
    final monthNum = getMonthNum(i);
    cells['subs_$monthNum'] =
        PlutoCell(value: totalMap['subs_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['lt_installment_$monthNum'] = PlutoCell(
        value: totalMap['lt_installment_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['st_installment_$monthNum'] = PlutoCell(
        value: totalMap['st_installment_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['interest_$monthNum'] = PlutoCell(
        value: totalMap['interest_$monthNum']?.toStringAsFixed(2) ?? '');
    cells['penalty_$monthNum'] = PlutoCell(
        value: totalMap['penalty_$monthNum']?.toStringAsFixed(2) ?? '');
  }
  return cells;
}

int getMonthNum(int index) => (index + 4) > 12 ? (index - 8) : (index + 4);

/// Calculate correct opening balances based on user registration date and financial year
Map<String, num> calculateOpeningBalances(UserModel user, int year) {
  num obSubs = 0;
  num obShares = 0;
  num obLtLoan = 0;
  num obStLoan = 0;

  // Determine if user should have opening balances for this year
  if (user.registrationDate != null) {
    final regDate = user.registrationDate!;
    final financialYearStart =
        DateTime(year - 1, 4, 1); // April 1st of previous year

    if (regDate.isBefore(financialYearStart)) {
      // User registered before this financial year started
      // Use stored opening balances (these should be March closing balances from previous year)
      obSubs = user.obSubs ?? 0;
      obShares = user.obShares ?? 0;
      obLtLoan = user.obLt ?? 0;
      obStLoan = user.obSt ?? 0;
    } else {
      // User registered during this financial year - opening balances are 0
      obSubs = 0;
      obShares = 0;
      obLtLoan = 0;
      obStLoan = 0;
    }
  } else {
    // No registration date available - use stored values as fallback
    obSubs = user.obSubs ?? 0;
    obShares = user.obShares ?? 0;
    obLtLoan = user.obLt ?? 0;
    obStLoan = user.obSt ?? 0;
  }

  return {
    'obSubs': obSubs,
    'obShares': obShares,
    'obLt': obLtLoan,
    'obSt': obStLoan,
  };
}
