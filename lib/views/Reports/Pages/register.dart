import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  late PlutoGridStateManager stateManager;

  bool csvExport = false;
  bool pdfExport = false;

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR No.',
      field: 'srno',
      type: PlutoColumnType.number(),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpfno',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'EMP NO.',
      field: 'empno',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'NAME',
      field: 'name',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'District Office',
      field: 'do',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'EMAIL',
      field: 'email',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'PHONE NO.',
      field: 'phoneno',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'ADDRESS',
      field: 'address',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'BANK ACCOUNT NAME',
      field: 'bankaccname',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'NAME OF BANK',
      field: 'bankname',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'BANK ACCOUNT NO',
      field: 'bankaccno',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'IFSC CODE',
      field: 'ifsccode',
      type: PlutoColumnType.text(),
    ),
    PlutoColumn(
      title: 'SUBS INT',
      field: 'subsint',
      type: PlutoColumnType.number(),
    ),
    PlutoColumn(
      title: 'DIVIDEND',
      field: 'dividend',
      type: PlutoColumnType.number(),
    ),
  ];

  List<PlutoRow> rows = [];

  void insertRowsFromUsers(List<UserModel> users) {
    // Sort users by district office name
    users.sort((a, b) {
      final aDoName = Get.find<HomeCtrl>()
              .districtoffice
              .firstWhereOrNull(
                (element) => element.docId == a.districtoffice,
              )
              ?.name ??
          '';

      final bDoName = Get.find<HomeCtrl>()
              .districtoffice
              .firstWhereOrNull(
                (element) => element.docId == b.districtoffice,
              )
              ?.name ??
          '';

      return aDoName.compareTo(bDoName);
    });

    rows = users.asMap().entries.map((entry) {
      int index = entry.key + 1; // For SR No., starting from 1
      UserModel user = entry.value;

      final doName = Get.find<HomeCtrl>().districtoffice.firstWhereOrNull(
            (element) => element.docId == user.districtoffice,
          );

      return PlutoRow(cells: {
        'srno': PlutoCell(value: index.toString()),
        'cpfno': PlutoCell(value: user.cpfNo.toString()),
        'empno': PlutoCell(value: user.employeeNo.toString()),
        'name': PlutoCell(value: user.name),
        'do': PlutoCell(value: doName?.name ?? ''),
        'email': PlutoCell(value: user.email ?? ''),
        'phoneno': PlutoCell(value: user.phoneNo ?? ''),
        'address': PlutoCell(value: user.currentAddress ?? ''),
        'bankaccname': PlutoCell(value: user.bankAcName ?? ''),
        'bankname': PlutoCell(value: user.bankName ?? ''),
        'bankaccno': PlutoCell(
          value: (user.bankAcNo?.toString() ?? '').replaceAll(',', ''),
        ),
        'ifsccode': PlutoCell(value: user.ifscCode ?? ''),
        'subsint': PlutoCell(value: user.totalSubsInt?.toString() ?? ''),
        'dividend': PlutoCell(value: user.totalDivident?.toString() ?? ''),
      });
    }).toList();

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void initState() {
    super.initState();
    final users = Get.find<HomeCtrl>().users;
    insertRowsFromUsers(users);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  csvExport
                      ? CircularProgressIndicator()
                      : CustomHeaderButton(
                          onPressed: () async => await exportCsv(),
                          buttonName: 'Export to CSV',
                        ),
                  SizedBox(width: 5),
                  pdfExport
                      ? CircularProgressIndicator()
                      : CustomHeaderButton(
                          onPressed: () async => await exportPdf(),
                          buttonName: 'Export to PDF',
                        ),
                ],
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [
              Padding(
                padding: EdgeInsets.symmetric(vertical: 22),
                child: Text(
                  "MEMBER'S REGISTER",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
            ],
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.transparent),
              ),
              child: PlutoGrid(
                columns: columns,
                rows: rows,
                configuration: const PlutoGridConfiguration(
                    scrollbar: PlutoGridScrollbarConfig(
                        draggableScrollbar: true,
                        isAlwaysShown: true,
                        scrollbarThickness:
                            PlutoScrollbar.defaultThicknessWhileDragging)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> exportCsv() async {
    try {
      setState(() => csvExport = true);

      List<List<dynamic>> csvData = [];

      // Headers based on your PlutoColumn titles
      csvData.add([
        'SR No.',
        'CPF No.',
        'EMP No.',
        'NAME',
        'District Office',
        'EMAIL',
        'PHONE NO.',
        'ADDRESS',
        'BANK ACCOUNT NAME',
        'NAME OF BANK',
        'BANK ACCOUNT NO',
        'IFSC CODE',
        'SUBS INT',
        'DIVIDEND',
      ]);

      for (final row in rows) {
        List<dynamic> rowData = [
          row.cells['srno']?.value ?? '',
          row.cells['cpfno']?.value ?? '',
          row.cells['empno']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['do']?.value ?? '',
          row.cells['email']?.value ?? '',
          row.cells['phoneno']?.value ?? '',
          row.cells['address']?.value ?? '',
          row.cells['bankaccname']?.value ?? '',
          row.cells['bankname']?.value ?? '',
          (row.cells['bankaccno']?.value ?? '').toString().replaceAll(',', ''),
          row.cells['ifsccode']?.value ?? '',
          row.cells['subsint']?.value ?? '',
          row.cells['dividend']?.value ?? '',
        ];

        csvData.add(rowData);
      }

      String csv = const ListToCsvConverter().convert(csvData);

      // UTF8 BOM for Excel compatibility
      final csvWithBom = '\uFEFF$csv';
      final bytes = utf8.encode(csvWithBom);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);

      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'member_register_${DateTime.now().millisecondsSinceEpoch}.csv';

      html.document.body!.children.add(anchor);
      anchor.click();

      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('CSV exported successfully')));
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text('Failed to export CSV')));
      debugPrint('CSV Export Error: $e');
    } finally {
      setState(() => csvExport = false);
    }
  }

  Future<void> exportPdf() async {
    try {
      setState(() => pdfExport = true);

      final pdf = pw.Document();

      pw.Widget cell(String text,
          {bool isHeader = false, double fontSize = 8}) {
        return pw.Container(
          padding: const pw.EdgeInsets.all(4),
          child: pw.Text(
            text,
            style: pw.TextStyle(
              fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
              fontSize: fontSize,
            ),
          ),
        );
      }

      pdf.addPage(pw.MultiPage(
        orientation: pw.PageOrientation.landscape,
        build: (context) {
          return [
            pw.Text('Member Register',
                style:
                    pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold)),
            pw.SizedBox(height: 10),
            pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
              children: [
                // Header row with grey background and bold small font
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColors.grey300),
                  children: columns.map((col) {
                    return pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text(
                        col.title,
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 7,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    );
                  }).toList(),
                ),
                // Data rows with small font and centered text
                ...rows.map((row) {
                  return pw.TableRow(
                    children: columns.map((col) {
                      final value = col.field == 'bankaccno'
                          ? (row.cells[col.field]?.value ?? '')
                              .toString()
                              .replaceAll(',', '')
                          : row.cells[col.field]?.value ?? '';
                      return pw.Padding(
                        padding: const pw.EdgeInsets.all(4),
                        child: pw.Text(
                          value.toString(),
                          style: const pw.TextStyle(fontSize: 7),
                          textAlign: pw.TextAlign.center,
                          softWrap: true,
                          overflow: pw.TextOverflow.visible,
                        ),
                      );
                    }).toList(),
                  );
                }),
              ],
            ),
          ];
        },
      ));

      final bytes = await pdf.save();

      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);

      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'member_register_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF exported successfully')));
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text('Failed to export PDF')));
      debugPrint('PDF Export Error: $e');
    } finally {
      setState(() => pdfExport = false);
    }
  }
}
