// ignore_for_file: use_build_context_synchronously, deprecated_member_use, avoid_web_libraries_in_flutter
import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class MonthlySharePage extends StatefulWidget {
  const MonthlySharePage({super.key});

  @override
  State<MonthlySharePage> createState() => _MonthlySharePageState();
}

class _MonthlySharePageState extends State<MonthlySharePage> {
  late PlutoGridStateManager stateManager;

  bool isLoading2 = false;
  bool csvExportIsLoading2 = false;
  bool pdfExportIsLoading2 = false;

  String? monthlyShareSelectedFinancialYear;
  int? monthlyShareSelectedYear;
  int? monthlyShareSelectedMonth;

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  // Generate last 6 months like in recovery.dart
  final last6Months = List.generate(6, (index) {
    final date = DateTime(DateTime.now().year, DateTime.now().month - index, 1);
    return date;
  }).reversed.toList();

  List<PlutoRow> rows = [];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'NAME OF MEMBER',
      field: 'name',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'DISTRICT OFFICE',
      field: 'district_office',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'OB SHARE',
      field: 'ob_share',
      type: PlutoColumnType.text(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'PAID',
      field: 'paid',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'REC',
      field: 'rec',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'MONTHLY TOTAL',
      field: 'monthly_total',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title:
          'DIVIDEND AMOUNT (${Get.find<HomeCtrl>().settings?.dividentRate ?? 0}%)',
      field: 'interest_amount',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
  ];

  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    monthlyShareSelectedFinancialYear =
        TheFinancialYear.getCurrentFinancialYear();
    monthlyShareSelectedYear = DateTime.now().year;
    monthlyShareSelectedMonth = DateTime.now().month;
  }

  void loadData(HomeCtrl ctrl) {
    setState(() => isLoading = true);
    try {
      insertMonthlyShareRows(ctrl.users);
    } catch (e) {
      showCtcAppSnackBar(context, "Error Loading Data");
      debugPrint('Error loading data: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void insertMonthlyShareRows(List<UserModel> users) {
    try {
      rows.clear();

      if (monthlyShareSelectedMonth == null ||
          monthlyShareSelectedYear == null) {
        return;
      }

      // Sort users by CPF number
      final sortedUsers = List<UserModel>.from(users)
        ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

      // Generate rows for each user
      rows = sortedUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;

        // Get district office name
        final doName = Get.find<HomeCtrl>().districtoffice.firstWhereOrNull(
              (element) => element.docId == user.districtoffice,
            );

        final ctrl = Get.find<HomeCtrl>();
        final num obShares = user.obShares ?? 0; // opening balance of share

        // Get monthly data for the selected month and year
        final monthlyData = Get.find<HomeCtrl>().usermonthly.firstWhereOrNull(
              (e) =>
                  e.cpfNo == user.cpfNo &&
                  e.selectedmonth == monthlyShareSelectedMonth &&
                  e.selectedyear == monthlyShareSelectedYear,
            );

        final num received = monthlyData?.shareValue ?? 0;
        final num monthlyTotal = obShares + received;

        num dividendInt = num.tryParse(ctrl.settings?.dividentRate ?? '0') ?? 0;
        final interestAmount = monthlyTotal * dividendInt / 1200;

        // Create and return PlutoRow
        return PlutoRow(
          cells: {
            'sr_no': PlutoCell(value: (index + 1).toString()),
            'cpf_no': PlutoCell(value: user.cpfNo),
            'name': PlutoCell(value: user.name.toUpperCase()),
            'district_office': PlutoCell(value: doName?.name ?? ''),
            'ob_share': PlutoCell(value: obShares),
            'paid': PlutoCell(value: 0),
            'rec': PlutoCell(value: received),
            'monthly_total': PlutoCell(value: monthlyTotal.toStringAsFixed(2)),
            'interest_amount':
                PlutoCell(value: interestAmount.toStringAsFixed(2)),
          },
        );
      }).toList();

      // Calculate totals
      final Map<String, double> totalMap = {};
      for (final row in rows) {
        row.cells.forEach((key, cell) {
          if (key == 'paid' ||
              key == 'rec' ||
              key == 'monthly_total' ||
              key == 'interest_amount') {
            final value = double.tryParse(cell.value.toString()) ?? 0.0;
            totalMap[key] = (totalMap[key] ?? 0.0) + value;
          }
        });
      }

      // Add total row
      final totalRow = PlutoRow(
        cells: {
          'sr_no': PlutoCell(value: ''),
          'cpf_no': PlutoCell(value: ''),
          'name': PlutoCell(value: 'TOTAL'),
          'district_office': PlutoCell(value: ''),
          'ob_share': PlutoCell(value: ''),
          ...totalMap.map((key, value) => MapEntry(
                key,
                PlutoCell(value: value.toStringAsFixed(2)),
              )),
        },
      );

      rows.add(totalRow);

      stateManager.removeAllRows();
      stateManager.appendRows(rows);
      stateManager.notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error inserting rows');
    }
  }

  List<int> get availableMonths {
    if (monthlyShareSelectedYear == null) return [];
    final currentYear = DateTime.now().year;
    if (monthlyShareSelectedYear == currentYear) {
      return List.generate(DateTime.now().month, (index) => index + 1);
    } else if (monthlyShareSelectedYear! < currentYear) {
      return List.generate(12, (index) => index + 1);
    }
    return [];
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField<int>(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: monthlyShareSelectedMonth,
                        decoration: InputDecoration(
                          hintText: "Select Month",
                          constraints: const BoxConstraints(
                              maxWidth: 200, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: availableMonths.map((month) {
                          return DropdownMenuItem<int>(
                            value: month,
                            child: Text(getMonthName(month)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              monthlyShareSelectedMonth = value;
                            });
                            try {
                              insertMonthlyShareRows(
                                  Get.find<HomeCtrl>().users);
                            } catch (e) {
                              debugPrint(e.toString());
                              showCtcAppSnackBar(context, 'Error loading data');
                            }
                          }
                        },
                      ),
                    ),
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField<int>(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: monthlyShareSelectedYear,
                        decoration: InputDecoration(
                          hintText: "Select Year",
                          constraints: const BoxConstraints(
                              maxWidth: 150, maxHeight: 45),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5)),
                        ),
                        items: yearList.map((year) {
                          return DropdownMenuItem<int>(
                            value: year,
                            child: Text(year.toString()),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              monthlyShareSelectedYear = value;
                              final months = availableMonths;
                              if (months.isNotEmpty) {
                                monthlyShareSelectedMonth = months.first;
                              } else {
                                monthlyShareSelectedMonth = null;
                              }
                            });
                            try {
                              insertMonthlyShareRows(
                                  Get.find<HomeCtrl>().users);
                            } catch (e) {
                              debugPrint(e.toString());
                              showCtcAppSnackBar(context, 'Error loading data');
                            }
                          }
                        },
                      ),
                    ),

                    // DropdownButtonHideUnderline(
                    //   child: DropdownButtonFormField<DateTime>(
                    //     focusColor: Colors.transparent,
                    //     dropdownColor: Colors.white,
                    //     value: last6Months.firstWhere(
                    //         (d) =>
                    //             d.month == monthlyShareSelectedMonth &&
                    //             d.year == monthlyShareSelectedYear,
                    //         orElse: () => DateTime.now()),
                    //     decoration: InputDecoration(
                    //       hintText: "Select Month",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 200, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5)),
                    //     ),
                    //     items: last6Months.map((date) {
                    //       final formatted =
                    //           "${getMonthName(date.month)} ${date.year}";
                    //       return DropdownMenuItem<DateTime>(
                    //         value: date,
                    //         child: Text(formatted),
                    //       );
                    //     }).toList(),
                    //     onChanged: (value) async {
                    //       if (value != null) {
                    //         setState(() {
                    //           monthlyShareSelectedMonth = value.month;
                    //           monthlyShareSelectedYear = value.year;
                    //         });

                    //         try {
                    //           insertMonthlyShareRows(ctrl.users);
                    //         } catch (e) {
                    //           debugPrint(e.toString());
                    //           showCtcAppSnackBar(context, 'Error loading data');
                    //         }
                    //       }
                    //     },
                    //   ),
                    // ),
                    // const SizedBox(width: 10),
                    // DropdownButtonHideUnderline(
                    //     child: DropdownButtonFormField(
                    //   focusColor: Colors.transparent,
                    //   dropdownColor: Colors.white,
                    //   value: monthlyShareSelectedYear,
                    //   decoration: InputDecoration(
                    //       hintText: "Select Year",
                    //       constraints: const BoxConstraints(
                    //           maxWidth: 150, maxHeight: 45),
                    //       border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(5))),
                    //   items: List.generate(
                    //     yearList.length,
                    //     (index) {
                    //       return DropdownMenuItem(
                    //         value: yearList[index],
                    //         child: Text(yearList[index].toString()),
                    //       );
                    //     },
                    //   ),
                    //   onChanged: (value) async {
                    //     setState(() {
                    //       monthlyShareSelectedYear = value;
                    //     });
                    //     try {
                    //       insertMonthlyShareRows(ctrl.users);
                    //     } catch (e) {
                    //       debugPrint(e.toString());
                    //       showCtcAppSnackBar(context, 'Error loading data');
                    //     }
                    //   },
                    // )),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    csvExportIsLoading2
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => exportCsv(),
                            buttonName: 'Export to CSV'),
                    SizedBox(width: 5),
                    pdfExportIsLoading2
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async => exportPdf(),
                            buttonName: 'Export to PDF'),
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "MONTHLY SHARE REPORT FOR ${getMonthName(monthlyShareSelectedMonth ?? 1).toUpperCase()} $monthlyShareSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    loadData(ctrl);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {},
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> exportCsv() async {
    try {
      setState(() => csvExportIsLoading2 = true);

      // Create CSV data
      List<List<dynamic>> csvData = [];

      // Add headers
      List<String> headers = [
        'Sr. No',
        'CPF No.',
        'Name',
        'District Office',
        'OB Share',
        'PAID',
        'REC',
        'MONTHLY TOTAL',
        'Interest Amount',
      ];

      csvData.add(headers);

      // Add row data
      for (PlutoRow row in rows) {
        if (row.cells['name']?.value == 'TOTAL') {
          // Add empty row before total
          csvData.add([]);
        }

        List<dynamic> rowData = [];

        // Basic info
        rowData.add(row.cells['sr_no']?.value ?? '');
        rowData.add(row.cells['cpf_no']?.value ?? '');
        rowData.add(row.cells['name']?.value ?? '');
        rowData.add(row.cells['district_office']?.value ?? '');

        // OB Share
        var obValue = row.cells['ob_share']?.value ?? '';
        if (obValue is num) obValue = obValue.toStringAsFixed(2);
        rowData.add(obValue);

        // Monthly values
        var paidValue = row.cells['paid']?.value ?? '';
        var recValue = row.cells['rec']?.value ?? '';
        var monthlyTotalValue = row.cells['monthly_total']?.value ?? '';

        if (paidValue is num) paidValue = paidValue.toStringAsFixed(2);
        if (recValue is num) recValue = recValue.toStringAsFixed(2);
        if (monthlyTotalValue is num) {
          monthlyTotalValue = monthlyTotalValue.toStringAsFixed(2);
        }

        rowData.add(paidValue);
        rowData.add(recValue);
        rowData.add(monthlyTotalValue);

        // Interest amount
        var interestValue = row.cells['interest_amount']?.value ?? '';

        if (interestValue is num) {
          interestValue = interestValue.toStringAsFixed(2);
        }

        rowData.add(interestValue);

        csvData.add(rowData);
      }

      // Convert to CSV string with UTF8 BOM for Excel compatibility
      final csv = const ListToCsvConverter().convert(csvData);
      final csvWithBOM = '\uFEFF$csv';

      // Create blob for web download
      final bytes = utf8.encode(csvWithBOM);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_share_${getMonthName(monthlyShareSelectedMonth ?? 1)}_$monthlyShareSelectedYear.csv';

      html.document.body!.children.add(anchor);
      anchor.click();

      // Cleanup
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'CSV file has been downloaded');
      setState(() => csvExportIsLoading2 = false);
    } catch (e) {
      setState(() => csvExportIsLoading2 = false);

      showCtcAppSnackBar(context, 'Export Failed');
      debugPrint('CSV Export Error: $e');
    } finally {
      setState(() => csvExportIsLoading2 = false);
    }
  }

  Future<void> exportPdf() async {
    try {
      setState(() => pdfExportIsLoading2 = true);

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(2000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          build: (pw.Context context) {
            // Helper for consistent cell padding and style
            pw.Widget cell(String text,
                {bool isHeader = false, double fontSize = 9}) {
              return pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: pw.Text(
                  text,
                  style: pw.TextStyle(
                    fontSize: fontSize,
                    fontWeight:
                        isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                  ),
                ),
              );
            }

            return [
              pw.Text(
                'MONTHLY SHARE REPORT FOR ${getMonthName(monthlyShareSelectedMonth ?? 1).toUpperCase()} $monthlyShareSelectedYear',
                style:
                    pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(width: 0.3),
                defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
                children: [
                  // Column Headers
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      cell('Sr. No', isHeader: true),
                      cell('CPF No.', isHeader: true),
                      cell('Name', isHeader: true),
                      cell('District Office', isHeader: true),
                      cell('OB Share', isHeader: true),
                      cell('PAID', isHeader: true),
                      cell('REC', isHeader: true),
                      cell('MONTHLY TOTAL', isHeader: true),
                      cell('Interest Amount', isHeader: true),
                    ],
                  ),

                  // Data Rows
                  ...rows.map((row) {
                    getVal(String field) {
                      final val = row.cells[field]?.value;
                      if (val is num) return val.toStringAsFixed(0);
                      return val?.toString() ?? '';
                    }

                    return pw.TableRow(
                      children: [
                        cell(getVal('sr_no')),
                        cell(getVal('cpf_no')),
                        cell(getVal('name')),
                        cell(getVal('district_office')),
                        cell(getVal('ob_share')),
                        cell(getVal('paid')),
                        cell(getVal('rec')),
                        cell(getVal('monthly_total')),
                        cell(getVal('interest_amount')),
                      ],
                    );
                  }),
                ],
              ),
            ];
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'monthly_share_${getMonthName(monthlyShareSelectedMonth ?? 1)}_${monthlyShareSelectedYear}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'PDF file has been downloaded');
      setState(() => pdfExportIsLoading2 = false);
    } catch (e) {
      setState(() => pdfExportIsLoading2 = false);
      showCtcAppSnackBar(context, 'PDF Export Failed');
      debugPrint('PDF Export Error: $e');
    } finally {
      setState(() => pdfExportIsLoading2 = false);
    }
  }
}
