// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/expenses_model.dart';
import 'package:foodcorp_admin/models/society_yearlyrecord_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class TrialbalancePage extends StatefulWidget {
  const TrialbalancePage({super.key});

  @override
  State<TrialbalancePage> createState() => _TrialbalancePageState();
}

class _TrialbalancePageState extends State<TrialbalancePage> {
  late PlutoGridStateManager stateManager;

  bool addingRowLoader = false;

  bool exportingToCsv = false;
  bool exportingToPdf = false;
  bool rowsLoading = false;

  String? yearlyTBSelectedFinancialYear;
  int? yearlyTBSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();
  late List<PlutoColumn> columns;
  List<PlutoRow> rows = [];
  Set<String> addedTitles = {};

  List<PlutoColumn> buildColumns() {
    return [
      PlutoColumn(
        title: 'PARTICULARS',
        field: 'particulars',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: 'RECEIPT',
        field: 'receipt',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: 'PAYMENT',
        field: 'payment',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      // PlutoColumn(
      //   title: 'ACTIONS',
      //   field: 'action',
      //   type: PlutoColumnType.text(),
      //   enableEditingMode: false,
      //   renderer: (rendererContext) {
      //     final title = rendererContext.row.cells['particulars']?.value
      //             .toString()
      //             .toUpperCase() ??
      //         '';
      //     if ([
      //       'SUBSCRIPTION',
      //       'LONG TERM LOAN',
      //       'SHORT TERM LOAN',
      //       'SHARE',
      //       'TOTAL'
      //     ].contains(title)) {
      //       return const SizedBox.shrink();
      //     }
      //     return IconButton(
      //       highlightColor: Colors.transparent,
      //       hoverColor: Colors.transparent,
      //       icon: const Icon(Icons.delete, color: Colors.red),
      //       onPressed: () async {
      //         deleteManualEntry(rendererContext.row);
      //       },
      //     );
      //   },
      // ),
    ];
  }

  @override
  void initState() {
    super.initState();
    columns = buildColumns();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    yearlyTBSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    yearlyTBSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();
    insertTBRows();
  }

  final tbSummaryRef = FBFireStore.trialBalance;
  final tbEntryRef = FBFireStore.tbEntries;

  void insertTBRows() async {
    try {
      setState(() {
        rowsLoading = true;
      });
      rows.clear();
      addedTitles.clear();

      final int year = yearlyTBSelectedYear ??
          TheFinancialYear.getCurrentFinancialYearStartYear();
      final DateTime now = DateTime.now();

      final DateTime startOfYear =
          TheFinancialYear.getFinancialYearStartDate(year);
      final DateTime endOfYear = TheFinancialYear.getFinancialYearEndDate(year);

      final querySnap = await FBFireStore.societyYearly
          .where("selectedyear", isEqualTo: year)
          .get();

      // Grand totals
      num grandTotalSubscription = 0;
      num grandTotalIntOnSubscription = 0;
      num grandTotalLtLoan = 0;
      num grandTotalLtInt = 0;
      num grandTotalStLoan = 0;
      num grandTotalStInt = 0;
      num grandTotalShares = 0;
      num grandTotalDividend = 0;

      for (var doc in querySnap.docs) {
        // final data = doc.data();
        final record = SocietyYearlyRecordModel.fromSnap(doc);

        grandTotalSubscription += record.totalSubscription;
        grandTotalIntOnSubscription += record.intOnSubscription;
        grandTotalLtLoan += record.ltLoanGiven;
        grandTotalLtInt += record.ltIntAmt;
        grandTotalStLoan += record.stLoanGiven;
        grandTotalStInt += record.stIntAmt;
        grandTotalShares += record.totalShareGiven;
        grandTotalDividend += record.totalDividend;
      }

      // Predefined summary entries (4 main entries)
      final List<Map<String, dynamic>> tbEntryMaps = [
        {
          'title': 'SUBSCRIPTION',
          'receipt': grandTotalIntOnSubscription,
          'payment': grandTotalSubscription,
        },
        {
          'title': 'LONG TERM LOAN',
          'receipt': grandTotalLtInt,
          'payment': grandTotalLtLoan,
        },
        {
          'title': 'SHORT TERM LOAN',
          'receipt': grandTotalStInt,
          'payment': grandTotalStLoan,
        },
        {
          'title': 'SHARE',
          'receipt': grandTotalDividend,
          'payment': grandTotalShares,
        },
      ];

      // For Firestore entry saving
      List<Map<String, dynamic>> allEntriesForYear = [];

      for (final e in tbEntryMaps) {
        final title = e['title'];
        final receipt = e['receipt'] ?? 0;
        final payment = e['payment'] ?? 0;

        rows.add(PlutoRow(
          cells: {
            'particulars': PlutoCell(value: title),
            'receipt': PlutoCell(value: receipt),
            'payment': PlutoCell(value: payment),
          },
        ));

        addedTitles.add(title.toString().toUpperCase());

        allEntriesForYear.add({
          'title': title,
          'receipt': receipt,
          'payment': payment,
          'amount': receipt + payment,
          'createdAt': now,
          'year': year,
        });
      }

      /// Add Expenses
      final expenseSnapByExpenseDate = await FBFireStore.expense
          .where('expenseDate', isGreaterThanOrEqualTo: startOfYear)
          .where('expenseDate', isLessThanOrEqualTo: endOfYear)
          .get();

      // Query by paymentDate
      final expenseSnapByPaymentDate = await FBFireStore.expense
          .where('paymentDate', isGreaterThanOrEqualTo: startOfYear)
          .where('paymentDate', isLessThanOrEqualTo: endOfYear)
          .get();

      // Combine documents, avoiding duplicates
      final Map<String, DocumentSnapshot> allDocsMap = {};
      for (final doc in expenseSnapByExpenseDate.docs) {
        allDocsMap[doc.id] = doc;
      }
      for (final doc in expenseSnapByPaymentDate.docs) {
        allDocsMap[doc.id] = doc;
      }
      final combinedDocs = allDocsMap.values.toList();

      final Map<String, Map<String, num>> groupedExpenses = {};

      for (final doc in combinedDocs) {
        final ex = Expense.fromSnap(doc);
        final title = ex.name.trim().toUpperCase();

        groupedExpenses.putIfAbsent(title, () => {'receipt': 0, 'payment': 0});

        if (ex.isIncome) {
          groupedExpenses[title]!['receipt'] =
              (groupedExpenses[title]!['receipt'] ?? 0) + ex.amount;
        } else {
          groupedExpenses[title]!['payment'] =
              (groupedExpenses[title]!['payment'] ?? 0) + ex.amount;
        }
      }

      groupedExpenses.forEach((title, amounts) {
        if (addedTitles.contains(title)) return;
        addedTitles.add(title);

        final receipt = amounts['receipt'] ?? 0;
        final payment = amounts['payment'] ?? 0;

        rows.add(PlutoRow(cells: {
          'particulars': PlutoCell(value: title),
          'receipt': PlutoCell(value: receipt),
          'payment': PlutoCell(value: payment),
        }));

        allEntriesForYear.add({
          'title': title,
          'receipt': receipt,
          'payment': payment,
          'amount': receipt + payment,
          'createdAt': now,
          'year': year,
        });
      });

      // TOTAL row
      num totalReceipt = 0;
      num totalPayment = 0;

      for (final row in rows) {
        final receipt =
            num.tryParse(row.cells['receipt']?.value.toString() ?? '0') ?? 0;
        final payment =
            num.tryParse(row.cells['payment']?.value.toString() ?? '0') ?? 0;
        totalReceipt += receipt;
        totalPayment += payment;
      }

      rows.add(PlutoRow(
        cells: {
          'particulars': PlutoCell(value: 'TOTAL'),
          'receipt': PlutoCell(value: totalReceipt),
          'payment': PlutoCell(value: totalPayment),
        },
      ));

      // Save or update to Firestore
      final trialBalSnap = await FBFireStore.trialBalance
          .where('year', isEqualTo: year)
          .limit(1)
          .get();

      DocumentReference trialBalDocRef;

      if (trialBalSnap.docs.isNotEmpty) {
        trialBalDocRef =
            FBFireStore.trialBalance.doc(trialBalSnap.docs.first.id);
        await trialBalDocRef.update({
          'year': year,
          'updatedAt': now,
          'totalRec': totalReceipt,
          'totalPay': totalPayment,
        });
      } else {
        trialBalDocRef = await FBFireStore.trialBalance.add({
          'year': year,
          'updatedAt': now,
          'createdAt': now,
          'totalRec': totalReceipt,
          'totalPay': totalPayment,
        });
      }

      // Clear old entries under subcollection 'entries'
      final entriesRef = trialBalDocRef.collection('entries');
      final existingEntries = await entriesRef.get();
      for (var doc in existingEntries.docs) {
        await doc.reference.delete();
      }

      // Add current entries (subscription, loans, shares, expenses)
      for (final entry in allEntriesForYear) {
        await entriesRef.add(entry);
      }

      setState(() {
        rowsLoading = false;
      });
      debugPrint('Trial Balance: Auto + expenses → synced with Firestore');
    } catch (e) {
      debugPrint('Error in insertTBRows: $e');
      setState(() {
        rowsLoading = false;
      });
      showCtcAppSnackBar(context, 'Failed to load trial balance data');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: yearlyTBSelectedYear,
                        decoration: InputDecoration(
                            hintText: "Select Year",
                            constraints: const BoxConstraints(
                                maxWidth: 150, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        items: List.generate(
                          yearList.length,
                          (index) {
                            return DropdownMenuItem(
                              value: yearList[index],
                              child: Text(yearList[index].toString()),
                            );
                          },
                        ),
                        onChanged: (value) async {
                          setState(() => yearlyTBSelectedYear = value);
                          stateManager.removeAllRows();
                          addedTitles.clear();
                          insertTBRows();
                        })),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    exportingToCsv
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToCSV();
                            },
                            buttonName: 'Export to CSV'),
                    SizedBox(width: 5),
                    exportingToPdf
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToPDF();
                            },
                            buttonName: 'Export to PDF'),
                    SizedBox(width: 5),
                    // slIsLoading
                    //     ? CircularProgressIndicator()
                    //     :
                    // CustomHeaderButton(
                    //     onPressed: () => showAddTrialBalanceRowPopup(context),
                    //     buttonName: 'ADD'),
                    // SizedBox(width: 5),
                    // CustomHeaderButton(
                    //     onPressed: () async {
                    //       // await slOnSave(ctrl);
                    //     },
                    //     buttonName: 'SAVE'),
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text("TRIAL BALANCE FOR THE YEAR $yearlyTBSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                // height: size.height, //  - 30,
                child: rowsLoading
                    ? Center(child: CircularProgressIndicator())
                    : PlutoGrid(
                        columns: columns,
                        rows: rows,
                        onLoaded: (PlutoGridOnLoadedEvent event) {
                          stateManager = event.stateManager;
                          stateManager.setShowColumnFilter(true);
                          stateManager.notifyListeners();
                          stateManager.notifyListenersOnPostFrame();
                        },
                        onChanged: (PlutoGridOnChangedEvent event) {},
                        configuration: const PlutoGridConfiguration(
                            columnSize: PlutoGridColumnSizeConfig(
                                autoSizeMode: PlutoAutoSizeMode.equal),
                            scrollbar: PlutoGridScrollbarConfig(
                                draggableScrollbar: true,
                                isAlwaysShown: true,
                                scrollbarThickness: PlutoScrollbar
                                    .defaultThicknessWhileDragging)),
                      ),
              ),
            ),
          ],
        ),
      );
    });
  }

  void deleteManualEntry(PlutoRow row) async {
    final title =
        row.cells['particulars']?.value.toString().toUpperCase() ?? '';
    final year = yearlyTBSelectedYear ??
        TheFinancialYear.getCurrentFinancialYearStartYear();

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Entry'),
        content: Text('Are you sure you want to delete "$title"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    try {
      // Remove from Firestore
      final trialBalSnap = await FBFireStore.trialBalance
          .where('year', isEqualTo: year)
          .limit(1)
          .get();

      if (trialBalSnap.docs.isNotEmpty) {
        final docRef = FBFireStore.trialBalance.doc(trialBalSnap.docs.first.id);
        final entriesRef = docRef.collection('entries');

        final query =
            await entriesRef.where('title', isEqualTo: title).limit(1).get();

        if (query.docs.isNotEmpty) {
          await entriesRef.doc(query.docs.first.id).delete();
        }
      }

      // Remove from grid and local list
      stateManager.removeRows([row]);
      rows.remove(row);
      addedTitles.remove(title);

      // Recalculate total
      num totalReceipt = 0;
      num totalPayment = 0;

      for (final r in rows) {
        final rTitle = r.cells['particulars']?.value.toString().toUpperCase();
        if (rTitle != 'TOTAL') {
          totalReceipt +=
              num.tryParse(r.cells['receipt']?.value.toString() ?? '0') ?? 0;
          totalPayment +=
              num.tryParse(r.cells['payment']?.value.toString() ?? '0') ?? 0;
        }
      }

      // Remove and re-add TOTAL row
      rows.removeWhere((r) =>
          r.cells['particulars']?.value.toString().toUpperCase() == 'TOTAL');

      rows.add(PlutoRow(cells: {
        'particulars': PlutoCell(value: 'TOTAL'),
        'receipt': PlutoCell(value: totalReceipt),
        'payment': PlutoCell(value: totalPayment),
        'action': PlutoCell(value: ''),
      }));

      await FBFireStore.trialBalance
          .doc(trialBalSnap.docs.first.id)
          .update({'totalRec': totalReceipt, 'totalPay': totalPayment});

      setState(() {});
      showCtcAppSnackBar(context, 'Entry deleted successfully');
    } catch (e) {
      showCtcAppSnackBar(context, 'Error deleting entry: $e');
    }
  }

  void showAddTrialBalanceRowPopup(BuildContext context) async {
    final titleController = TextEditingController();
    final receiptController = TextEditingController();
    final paymentController = TextEditingController();

    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Add Trial Balance Row"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: titleController,
              decoration: const InputDecoration(
                labelText: 'Particular',
                hintText: 'Enter title (e.g., SUBSCRIPTION)',
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: receiptController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Receipt',
                hintText: 'Enter receipt amount',
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: paymentController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Payment',
                hintText: 'Enter payment amount',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                addingRowLoader = false;
              });
              Navigator.pop(context);
            },
            child: const Text("Cancel"),
          ),
          addingRowLoader
              ? CircularProgressIndicator()
              : ElevatedButton(
                  onPressed: () {
                    setState(() {
                      addingRowLoader = true;
                    });
                    final title = titleController.text.trim().toUpperCase();
                    final receipt =
                        num.tryParse(receiptController.text.trim()) ?? 0;
                    final payment =
                        num.tryParse(paymentController.text.trim()) ?? 0;

                    if (title.isEmpty) {
                      showCtcAppSnackBar(context, "Title cannot be empty");
                      return;
                    }

                    if (addedTitles.contains(title)) {
                      showCtcAppSnackBar(context, "Row already exists.");
                      return;
                    }

                    final newRow = PlutoRow(
                      cells: {
                        'particulars': PlutoCell(value: title),
                        'receipt': PlutoCell(value: receipt),
                        'payment': PlutoCell(value: payment),
                        'action': PlutoCell(value: ''),
                      },
                    );

                    stateManager.insertRows(0, [newRow]);

                    rows.insert(0, newRow);

                    addedTitles.add(title);

                    saveNewEntryToFirestore(title, receipt, payment);

                    setState(() {
                      addingRowLoader = false;
                    });

                    Navigator.pop(context);
                  },
                  child: const Text("Add"),
                ),
        ],
      ),
    );
  }

  Future<void> saveNewEntryToFirestore(
      String title, num receipt, num payment) async {
    final year = yearlyTBSelectedYear ??
        TheFinancialYear.getCurrentFinancialYearStartYear();

    final now = DateTime.now();

    final trialBalSnap = await FBFireStore.trialBalance
        .where('year', isEqualTo: year)
        .limit(1)
        .get();

    if (trialBalSnap.docs.isEmpty) return;

    final docRef = FBFireStore.trialBalance.doc(trialBalSnap.docs.first.id);
    final subColRef = docRef.collection('entries');

    await subColRef.add({
      'title': title,
      'receipt': receipt,
      'payment': payment,
      'amount': receipt + payment,
      'createdAt': now,
      'year': year,
    });
  }

  Future<void> exportToCSV() async {
    setState(() => exportingToCsv = true);

    try {
      final csv = StringBuffer();

      final headers = ['PARTICULARS', 'RECEIPT', 'PAYMENT'];
      csv.writeln(headers.map((e) => '"$e"').join(','));

      if (stateManager.rows.isEmpty) {
        setState(() => exportingToCsv = false);
        showCtcAppSnackBar(context, "No data to export");
        return;
      }

      for (int i = 0; i < rows.length; i++) {
        final row = rows[i];
        final particulars = row.cells['particulars']?.value.toString() ?? '';
        final receipt = row.cells['receipt']?.value.toString() ?? '';
        final payment = row.cells['payment']?.value.toString() ?? '';

        final rowData = [particulars, receipt, payment].map((e) => '"$e"');
        csv.writeln(rowData.join(','));
      }

      final blob = html.Blob([csv.toString()]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'trial_balance.csv')
        ..click();

      html.Url.revokeObjectUrl(url);
    } catch (e, stackTrace) {
      debugPrint("Error exporting CSV: $e\n$stackTrace");
      setState(() => exportingToCsv = false);
      showCtcAppSnackBar(context, "Failed to export CSV");
    } finally {
      setState(() => exportingToCsv = false);
    }
  }

  Future<void> exportToPDF() async {
    setState(() => exportingToPdf = true);

    try {
      final pdf = pw.Document();
      final headers = ['PARTICULARS', 'RECEIPT', 'PAYMENT'];

      if (stateManager.rows.isEmpty) {
        setState(() => exportingToPdf = false);
        showCtcAppSnackBar(context, "No data to export");
        return;
      }

      final data = <List<String>>[
        headers,
        for (int i = 0; i < rows.length; i++)
          [
            rows[i].cells['particulars']?.value.toString() ?? '',
            rows[i].cells['receipt']?.value.toString() ?? '',
            rows[i].cells['payment']?.value.toString() ?? '',
          ]
      ];

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4.landscape,
          margin: pw.EdgeInsets.all(16),
          build: (context) => [
            pw.Text(
              'Trial Balance Report',
              style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              textAlign: pw.TextAlign.center,
            ),
            pw.SizedBox(height: 10),
            pw.Table(
              border: pw.TableBorder.all(width: 0.5),
              defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
              children: [
                pw.TableRow(
                  decoration: pw.BoxDecoration(color: PdfColors.grey300),
                  children: headers.map((h) {
                    return pw.Padding(
                      padding: const pw.EdgeInsets.all(6),
                      child: pw.Text(
                        h,
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 10,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    );
                  }).toList(),
                ),
                ...data.sublist(1).map((row) {
                  return pw.TableRow(
                    children: row.map((cell) {
                      return pw.Padding(
                        padding: const pw.EdgeInsets.all(6),
                        child: pw.Text(
                          cell,
                          style: pw.TextStyle(fontSize: 9),
                          textAlign: pw.TextAlign.center,
                        ),
                      );
                    }).toList(),
                  );
                }),
              ],
            ),
          ],
        ),
      );

      final pdfBytes = await pdf.save();
      final blob = html.Blob([Uint8List.fromList(pdfBytes)]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', 'trial_balance.pdf')
        ..click();

      html.Url.revokeObjectUrl(url);
    } catch (e, stackTrace) {
      debugPrint("Error exporting PDF: $e\n$stackTrace");
      setState(() => exportingToPdf = false);
      showCtcAppSnackBar(context, "Failed to export PDF");
    } finally {
      setState(() => exportingToPdf = false);
    }
  }
}
