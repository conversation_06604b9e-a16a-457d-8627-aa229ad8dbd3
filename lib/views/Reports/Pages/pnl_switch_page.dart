// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/views/Reports/Pages/pnl_page.dart';
import 'package:foodcorp_admin/views/Reports/Pages/profit_distribution_page.dart';

enum ReportPage { pnl, profitDistribution }

class PnlSwitcherPage extends StatefulWidget {
  const PnlSwitcherPage({super.key});

  @override
  State<PnlSwitcherPage> createState() => _PnlSwitcherPageState();
}

class _PnlSwitcherPageState extends State<PnlSwitcherPage> {
  ReportPage selectedSegment = ReportPage.pnl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text("Reports - Switcher"),
      //   backgroundColor: Colors.green.shade700,
      // ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            CupertinoSlidingSegmentedControl<ReportPage>(
              padding: const EdgeInsets.all(4),
              thumbColor: Colors.green.shade300,
              groupValue: selectedSegment,
              children: const {
                ReportPage.pnl: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  child: Text("Profit and loss"),
                ),
                ReportPage.profitDistribution: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  child: Text("Distribution of Profit"),
                ),
              },
              onValueChanged: (value) {
                if (value != null) {
                  setState(() {
                    selectedSegment = value;
                  });
                }
              },
            ),
            const SizedBox(height: 20),
            Expanded(
              child: selectedSegment == ReportPage.pnl
                  ? const PnlPage()
                  : const ProfitDistributionPage(),
            ),
          ],
        ),
      ),
    );
  }
}
