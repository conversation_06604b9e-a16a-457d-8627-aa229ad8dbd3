// ignore_for_file: deprecated_member_use, avoid_web_libraries_in_flutter, use_build_context_synchronously

import 'dart:convert';
import 'package:csv/csv.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html;
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class YearlyLoanPage extends StatefulWidget {
  const YearlyLoanPage({super.key});

  @override
  State<YearlyLoanPage> createState() => _YearlyLoanPageState();
}

class _YearlyLoanPageState extends State<YearlyLoanPage> {
  late PlutoGridStateManager stateManager;

  bool isLoading = false;
  bool csvExportIsLoading = false;
  bool pdfExportIsLoading = false;

  String? yearlyLoanSelectedFinancialYear;
  int? yearlyLoanSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  final List<PlutoColumnGroup> columnGroups = [
    PlutoColumnGroup(
      title: 'PLACE OF POSTING',
      fields: ['district_office'],
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumnGroup(
      title: 'OPENING BALANCE',
      fields: ['ob_lt'],
    ),
    PlutoColumnGroup(
      title: 'OPENING BALANCE',
      fields: ['ob_st'],
    ),
    ...getDynamicMonthNumbers().map((monthNum) {
      final prefix = monthShortName(monthNum);
      final isColored = monthNum % 2 == 0;
      return PlutoColumnGroup(
        title: prefix,
        fields: [
          'lt_paid_$monthNum',
          'st_paid_$monthNum',
          'lt_installment_$monthNum',
          'st_installment_$monthNum',
        ],
        backgroundColor: isColored ? Color(0xffC9E9D2) : null,
      );
    }),
  ];

  final List<PlutoColumn> columns = [
    PlutoColumn(
      title: 'SR. NO',
      field: 'sr_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'CPF NO.',
      field: 'cpf_no',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'NAME OF MEMBER',
      field: 'name',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'DISTRICT OFFICE',
      field: 'district_office',
      type: PlutoColumnType.text(),
      enableEditingMode: false,
      backgroundColor: Color(0xffC9E9D2),
    ),
    PlutoColumn(
      title: 'OB LONGTERM',
      field: 'ob_lt',
      type: PlutoColumnType.text(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'OB SHORTTERM',
      field: 'ob_st',
      type: PlutoColumnType.text(),
      enableEditingMode: true,
    ),
    ...getDynamicMonthNumbers().expand((monthNum) {
      final prefix = monthShortName(monthNum);
      final isColored = monthNum % 2 == 0;
      return [
        PlutoColumn(
            title: '$prefix LT PAID',
            field: 'lt_paid_$monthNum',
            type: PlutoColumnType.text(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
        PlutoColumn(
            title: '$prefix ST PAID',
            field: 'st_paid_$monthNum',
            type: PlutoColumnType.text(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
        PlutoColumn(
            title: '$prefix LT INSTALLMENT',
            field: 'lt_installment_$monthNum',
            type: PlutoColumnType.text(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
        PlutoColumn(
            title: '$prefix ST INSTALLMENT',
            field: 'st_installment_$monthNum',
            type: PlutoColumnType.text(),
            enableEditingMode: false,
            backgroundColor: isColored ? Color(0xffC9E9D2) : null),
      ];
    }),
    PlutoColumn(
      title: 'TOTAL LT PAID',
      field: 'total_lt_paid',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'TOTAL ST PAID',
      field: 'total_st_paid',
      type: PlutoColumnType.number(),
      enableEditingMode: false,
    ),
    PlutoColumn(
      title: 'LT RECOVERED',
      field: 'lt_recovered',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'ST RECOVERED',
      field: 'st_recovered',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'LT CLOSING BALANCE',
      field: 'lt_closing_balance',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
    PlutoColumn(
      title: 'ST CLOSING BALANCE',
      field: 'st_closing_balance',
      type: PlutoColumnType.number(),
      enableEditingMode: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    yearlyLoanSelectedFinancialYear =
        TheFinancialYear.getCurrentFinancialYear();
    yearlyLoanSelectedYear =
        TheFinancialYear.getCurrentFinancialYearStartYear();
  }

  void loadData(HomeCtrl ctrl) {
    setState(() => isLoading = true);
    try {
      insertYearlyLoanRows(ctrl.users);
    } catch (e) {
      showCtcAppSnackBar(context, "Error Loading Data");
      debugPrint('Error loading data: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void insertYearlyLoanRows(List<UserModel> users) async {
    try {
      rows.clear();
      final ctrl = Get.find<HomeCtrl>();

      final fyStart =
          TheFinancialYear.getFinancialYearStartDate(yearlyLoanSelectedYear!);
      final fyEnd =
          TheFinancialYear.getFinancialYearEndDate(yearlyLoanSelectedYear!);

      final loansSnapshot = await FBFireStore.loan
          .where("approvedOn", isGreaterThanOrEqualTo: fyStart)
          .where("approvedOn", isLessThan: fyEnd)
          .get();

      final List<Loan> yearlyLoans =
          loansSnapshot.docs.map((e) => Loan.fromSnap(e)).toList();

      final sortedUsers = List<UserModel>.from(users)
        ..sort((a, b) => a.cpfNo.compareTo(b.cpfNo));

      rows = sortedUsers.asMap().entries.map((entry) {
        final index = entry.key;
        final user = entry.value;

        final doName = ctrl.districtoffice.firstWhereOrNull(
          (element) => element.docId == user.districtoffice,
        );

        num obLt = entry.value.obLt ?? 0;
        num obSt = entry.value.obSt ?? 0;

        num totalLtPaid = 0;
        num totalStPaid = 0;
        num totalLtInst = 0;
        num totalStInst = 0;

        final int currentFiscalIndex = (DateTime.now().month < 3)
            ? DateTime.now().month + 12 - 3
            : DateTime.now().month - 3;

        final Map<String, PlutoCell> monthCells = {};

        for (int i = 0; i < 12; i++) {
          final monthNum = (i + 3) > 12 ? (i - 9) : (i + 3);

          if (i > currentFiscalIndex) {
            monthCells.addAll({
              'lt_paid_$monthNum': PlutoCell(value: ''),
              'st_paid_$monthNum': PlutoCell(value: ''),
              'lt_installment_$monthNum': PlutoCell(value: ''),
              'st_installment_$monthNum': PlutoCell(value: ''),
            });
            continue;
          }

          final monthlyData = ctrl.usermonthly.firstWhereOrNull((e) =>
              e.cpfNo == user.cpfNo &&
              e.selectedmonth == monthNum &&
              e.selectedyear == yearlyLoanSelectedYear);

          final num ltInst = monthlyData?.ltInstallment ?? 0;
          final num stInst = monthlyData?.stInstallment ?? 0;

          totalLtInst += ltInst;
          totalStInst += stInst;

          final List<Loan> userMonthLoans = yearlyLoans.where((loan) {
            return loan.uid == user.docId &&
                loan.approvedOn != null &&
                loan.approvedOn!.month == monthNum &&
                !loan.isSettled;
          }).toList();

          final num ltPaid = userMonthLoans
              .where((loan) => loan.loanType == LoanTypes.longTerm)
              .fold(0, (sum, loan) => sum + loan.appliedLoanAmt);

          final num stPaid = userMonthLoans
              .where((loan) => loan.loanType == LoanTypes.emergencyLoan)
              .fold(0, (sum, loan) => sum + loan.appliedLoanAmt);

          totalLtPaid += ltPaid;
          totalStPaid += stPaid;

          monthCells.addAll({
            'lt_paid_$monthNum': PlutoCell(value: ltPaid.ceil().toString()),
            'st_paid_$monthNum': PlutoCell(value: stPaid.ceil().toString()),
            'lt_installment_$monthNum':
                PlutoCell(value: ltInst.ceil().toString()),
            'st_installment_$monthNum':
                PlutoCell(value: stInst.ceil().toString()),
          });
        }

        final num ltRecovered = totalLtInst;
        final num stRecovered = totalStInst;

        final num ltClosingBalance =
            (totalLtPaid - ltRecovered).clamp(0, double.infinity);
        final num stClosingBalance =
            (totalStPaid - stRecovered).clamp(0, double.infinity);

        return PlutoRow(cells: {
          'sr_no': PlutoCell(value: (index + 1).toString()),
          'cpf_no': PlutoCell(value: user.cpfNo),
          'name': PlutoCell(value: user.name.toUpperCase()),
          'district_office': PlutoCell(value: doName?.name ?? ''),
          'ob_lt': PlutoCell(value: obLt.ceil().toString()),
          'ob_st': PlutoCell(value: obSt.ceil().toString()),
          ...monthCells,
          'total_lt_paid': PlutoCell(value: totalLtPaid.ceil().toString()),
          'total_st_paid': PlutoCell(value: totalStPaid.ceil().toString()),
          'lt_recovered': PlutoCell(value: ltRecovered.ceil().toString()),
          'st_recovered': PlutoCell(value: stRecovered.ceil().toString()),
          'lt_closing_balance':
              PlutoCell(value: ltClosingBalance.ceil().toString()),
          'st_closing_balance':
              PlutoCell(value: stClosingBalance.ceil().toString()),
        });
      }).toList();

      Map<String, num> columnTotals = {};
      for (var col in columns) {
        // Only initialize numeric columns, skip text columns
        if (!['sr_no', 'cpf_no', 'name', 'district_office']
            .contains(col.field)) {
          columnTotals[col.field] = 0;
        }
      }

      for (var row in rows) {
        for (var field in columnTotals.keys) {
          final value =
              num.tryParse(row.cells[field]?.value?.toString() ?? '') ?? 0;
          columnTotals[field] = (columnTotals[field] ?? 0) + value;
        }
      }

// Create total row
      Map<String, PlutoCell> totalRowCells = {};
      for (var col in columns) {
        if (col.field == 'name') {
          totalRowCells[col.field] = PlutoCell(value: 'TOTAL');
        } else if (['sr_no', 'cpf_no', 'district_office', 'ob_lt', 'ob_st']
            .contains(col.field)) {
          totalRowCells[col.field] = PlutoCell(value: '');
        } else {
          totalRowCells[col.field] = PlutoCell(
              value: columnTotals.containsKey(col.field)
                  ? columnTotals[col.field]?.ceil().toString() ?? ''
                  : '');
        }
      }

// Add total row at the end
      rows.add(PlutoRow(cells: totalRowCells));

      stateManager.removeAllRows();
      stateManager.appendRows(rows);
      stateManager.notifyListeners();
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error inserting loan rows');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return
          // SizedBox();
          Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                        focusColor: Colors.transparent,
                        dropdownColor: Colors.white,
                        value: yearlyLoanSelectedYear,
                        decoration: InputDecoration(
                            hintText: "Select Year",
                            constraints: const BoxConstraints(
                                maxWidth: 150, maxHeight: 45),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5))),
                        items: List.generate(
                          yearList.length,
                          (index) {
                            return DropdownMenuItem(
                              value: yearList[index],
                              child: Text(yearList[index].toString()),
                            );
                          },
                        ),
                        onChanged: (value) async {
                          setState(() => yearlyLoanSelectedYear = value);

                          stateManager.removeAllRows();

                          // stateManager.appendRows(rows);

                          // setState(() {
                          //   slIsLoading = false;
                          // });
                        })),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    csvExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToCsv();
                            },
                            buttonName: 'Export to CSV',
                          ),
                    SizedBox(width: 5),
                    pdfExportIsLoading
                        ? CircularProgressIndicator()
                        : CustomHeaderButton(
                            onPressed: () async {
                              await exportToPdf();
                            },
                            buttonName: 'Export to PDF',
                          ),
                    SizedBox(width: 5),
                    // slIsLoading
                    //     ? CircularProgressIndicator()
                    //     :
                    // CustomHeaderButton(
                    //     onPressed: () async {
                    //       // await slOnSave(ctrl);
                    //     },
                    //     buttonName: 'SAVE')
                  ],
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 22),
              child: Text(
                  "YEARLY LOAN REPORT FOR THE YEAR $yearlyLoanSelectedYear",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(color: Colors.transparent)),
                child: PlutoGrid(
                  columnGroups: columnGroups,
                  columns: columns,
                  rows: rows,
                  onLoaded: (PlutoGridOnLoadedEvent event) {
                    stateManager = event.stateManager;
                    stateManager.setShowColumnFilter(true);
                    loadData(ctrl);
                    stateManager.notifyListeners();
                    stateManager.notifyListenersOnPostFrame();
                  },
                  onChanged: (PlutoGridOnChangedEvent event) {
                    try {
                      insertYearlyLoanRows(ctrl.users);
                    } catch (e) {
                      debugPrint(e.toString());
                      showCtcAppSnackBar(context, 'Error loading data');
                    }
                  },
                  configuration: const PlutoGridConfiguration(
                      scrollbar: PlutoGridScrollbarConfig(
                          draggableScrollbar: true,
                          isAlwaysShown: true,
                          scrollbarThickness:
                              PlutoScrollbar.defaultThicknessWhileDragging)),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  Future<void> exportToCsv() async {
    try {
      setState(() => csvExportIsLoading = true);

      List<List<dynamic>> csvData = [];
      List<String> headers = [
        'Sr. No',
        'CPF No.',
        'Name',
        'District Office',
        'OB LT',
        'OB ST',
      ];

      getDynamicMonthNumbers().forEach((monthNum) {
        final prefix = monthShortName(monthNum);
        headers.add('$prefix LT PAID');
        headers.add('$prefix ST PAID');
        headers.add('$prefix LT INST');
        headers.add('$prefix ST INST');
      });

      headers.add('TOTAL LT PAID');
      headers.add('TOTAL ST PAID');
      headers.add('LT RECOVERED');
      headers.add('ST RECOVERED');
      headers.add('LT CLOSING BALANCE');
      headers.add('ST CLOSING BALANCE');

      csvData.add(headers);

      for (PlutoRow row in rows) {
        if (row.cells['name']?.value == 'TOTAL') csvData.add([]);

        List<dynamic> rowData = [
          row.cells['sr_no']?.value ?? '',
          row.cells['cpf_no']?.value ?? '',
          row.cells['name']?.value ?? '',
          row.cells['district_office']?.value ?? '',
          row.cells['ob_lt']?.value ?? '',
          row.cells['ob_st']?.value ?? '',
        ];

        getDynamicMonthNumbers().forEach((monthNum) {
          rowData.add(row.cells['lt_paid_$monthNum']?.value ?? '');
          rowData.add(row.cells['st_paid_$monthNum']?.value ?? '');
          rowData.add(row.cells['lt_installment_$monthNum']?.value ?? '');
          rowData.add(row.cells['st_installment_$monthNum']?.value ?? '');
        });

        rowData.add(row.cells['total_lt_paid']?.value ?? '');
        rowData.add(row.cells['total_st_paid']?.value ?? '');
        rowData.add(row.cells['lt_recovered']?.value ?? '');
        rowData.add(row.cells['st_recovered']?.value ?? '');
        rowData.add(row.cells['lt_closing_balance']?.value ?? '');
        rowData.add(row.cells['st_closing_balance']?.value ?? '');

        csvData.add(rowData);
      }

      final csv = const ListToCsvConverter().convert(csvData);
      final csvWithBOM = '\uFEFF$csv';
      final bytes = utf8.encode(csvWithBOM);
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download = 'yearly_loan_$yearlyLoanSelectedYear.csv';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'CSV file has been downloaded');
      setState(() => csvExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'CSV Export Failed');
      debugPrint('CSV Export Error: $e');
      setState(() => csvExportIsLoading = false);
    } finally {
      setState(() => csvExportIsLoading = false);
    }
  }

  Future<void> exportToPdf() async {
    try {
      setState(() => pdfExportIsLoading = true);

      final pdf = pw.Document();

      pdf.addPage(
        pw.MultiPage(
          pageFormat:
              PdfPageFormat(4000, PdfPageFormat.a3.height, marginAll: 40),
          orientation: pw.PageOrientation.landscape,
          build: (pw.Context context) {
            final months = getDynamicMonthNumbers();

            pw.Widget cell(String text,
                {bool isHeader = false, double fontSize = 9}) {
              return pw.Container(
                padding:
                    const pw.EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: pw.Text(
                  text,
                  style: pw.TextStyle(
                    fontSize: fontSize,
                    fontWeight:
                        isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
                  ),
                ),
              );
            }

            return [
              pw.Text(
                'YEARLY LOAN REPORT FOR THE YEAR $yearlyLoanSelectedYear',
                style:
                    pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(width: 0.3),
                defaultVerticalAlignment: pw.TableCellVerticalAlignment.middle,
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(color: PdfColors.grey300),
                    children: [
                      cell('Sr. No', isHeader: true),
                      cell('CPF No.', isHeader: true),
                      cell('Name', isHeader: true),
                      cell('District Office', isHeader: true),
                      cell('OB LT', isHeader: true),
                      cell('OB ST', isHeader: true),
                      ...months.expand((monthNum) => [
                            cell('${monthShortName(monthNum)} LT PAID',
                                isHeader: true),
                            cell('${monthShortName(monthNum)} ST PAID',
                                isHeader: true),
                            cell('${monthShortName(monthNum)} LT INST',
                                isHeader: true),
                            cell('${monthShortName(monthNum)} ST INST',
                                isHeader: true),
                          ]),
                      cell('TOTAL LT PAID', isHeader: true),
                      cell('TOTAL ST PAID', isHeader: true),
                      cell('LT RECOVERED', isHeader: true),
                      cell('ST RECOVERED', isHeader: true),
                      cell('LT CLOSING BAL', isHeader: true),
                      cell('ST CLOSING BAL', isHeader: true),
                    ],
                  ),
                  ...rows.map((row) {
                    getVal(String field) {
                      final val = row.cells[field]?.value;
                      if (val is num) return val.toStringAsFixed(0);
                      return val?.toString() ?? '';
                    }

                    return pw.TableRow(
                      children: [
                        cell(getVal('sr_no')),
                        cell(getVal('cpf_no')),
                        cell(getVal('name')),
                        cell(getVal('district_office')),
                        cell(getVal('ob_lt')),
                        cell(getVal('ob_st')),
                        ...months.expand((monthNum) => [
                              cell(getVal('lt_paid_$monthNum')),
                              cell(getVal('st_paid_$monthNum')),
                              cell(getVal('lt_installment_$monthNum')),
                              cell(getVal('st_installment_$monthNum')),
                            ]),
                        cell(getVal('total_lt_paid')),
                        cell(getVal('total_st_paid')),
                        cell(getVal('lt_recovered')),
                        cell(getVal('st_recovered')),
                        cell(getVal('lt_closing_balance')),
                        cell(getVal('st_closing_balance')),
                      ],
                    );
                  }),
                ],
              ),
            ];
          },
        ),
      );

      final bytes = await pdf.save();
      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.document.createElement('a') as html.AnchorElement
        ..href = url
        ..style.display = 'none'
        ..download =
            'yearly_loan_${yearlyLoanSelectedYear}_${DateTime.now().millisecondsSinceEpoch}.pdf';

      html.document.body!.children.add(anchor);
      anchor.click();
      html.document.body!.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      showCtcAppSnackBar(context, 'PDF file has been downloaded');
      setState(() => pdfExportIsLoading = false);
    } catch (e) {
      showCtcAppSnackBar(context, 'PDF Export Failed');
      debugPrint('PDF Export Error: $e');
      setState(() => pdfExportIsLoading = false);
    } finally {
      setState(() => pdfExportIsLoading = false);
    }
  }
}
