import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:get/get.dart';
import 'package:pdf/pdf.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'dart:html' as html; // For web file download (if Flutter Web)
import 'package:pdf/widgets.dart' as pw;
import 'package:cloud_firestore/cloud_firestore.dart';

class BalanceSheetPage extends StatefulWidget {
  const BalanceSheetPage({super.key});

  @override
  State<BalanceSheetPage> createState() => _BalanceSheetPageState();
}

class _BalanceSheetPageState extends State<BalanceSheetPage> {
  late PlutoGridStateManager stateManager;

  String? yearlyBsSelectedFinancialYear;
  int? yearlyBsSelectedYear; // Keep for backward compatibility

  List<String> financialYearList =
      TheFinancialYear.generateFinancialYearsList();
  List<int> yearList = TheFinancialYear.generateFinancialYearStartYearsList();

  List<PlutoRow> rows = [];

  bool addingRow = false;

  List<PlutoColumn> get columns {
    final currentFY = TheFinancialYear.getCurrentFinancialYear();
    final previousFY = TheFinancialYear.startYearToFinancialYear(
        TheFinancialYear.getCurrentFinancialYearStartYear() - 1);

    return [
      PlutoColumn(
        title: previousFY,
        field: 'year',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: 'SHARE CAPITAL & LIABILITY',
        field: 'particulars',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: currentFY,
        field: 'year2',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: previousFY,
        field: 'year3',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: 'ASSETS AND LOAN',
        field: 'assets_and_loan',
        type: PlutoColumnType.text(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
      PlutoColumn(
        title: currentFY,
        field: 'year4',
        type: PlutoColumnType.number(),
        enableEditingMode: false,
        backgroundColor: Color(0xffC9E9D2),
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    financialYearList = TheFinancialYear.generateFinancialYearsList();
    yearList = TheFinancialYear.generateFinancialYearStartYearsList();
    yearlyBsSelectedFinancialYear = TheFinancialYear.getCurrentFinancialYear();
    yearlyBsSelectedYear = TheFinancialYear.getCurrentFinancialYearStartYear();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load data after the widget is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      addBsRows(year: yearlyBsSelectedYear);
    });
  }

  Future<void> addBsRows({int? year}) async {
    try {
      setState(() {
        rows = [];
        // Only remove rows if stateManager is initialized
        try {
          stateManager.removeAllRows();
        } catch (e) {
          // stateManager not initialized yet, ignore
        }
      });

      final selectedYear = yearlyBsSelectedYear ??
          TheFinancialYear.getCurrentFinancialYearStartYear();
      final previousYear = selectedYear - 1;

      // Fetch current year data
      final currentYearData = await _fetchYearData(selectedYear);
      final previousYearData = await _fetchYearData(previousYear);

      List<PlutoRow> newRows = [];

      // === BALANCE SHEET STRUCTURE - FIXED ROWS ONLY ===

      // Fetch cash balance from SocietyYearly for specific years
      final previousYearCashBalance =
          await _fetchCashBalanceFromSocietyYearly(previousYear);
      final currentYearCashBalance =
          await _fetchCashBalanceFromSocietyYearly(selectedYear);

      // Row 1: FIXED - Issued & Subscribed Share Capital + Cash Balance
      newRows.add(_createBalanceSheetRow(
        _safeValue(
            previousYearData['totalShareGiven']), // Previous year share capital
        'ISSUED & SUBSCRIBED SHARE CAPITAL',
        _safeValue(
            currentYearData['totalShareGiven']), // Current year share capital
        previousYearCashBalance, // Previous year cash balance from SocietyYearly
        'CASH BALANCE',
        currentYearCashBalance, // Current year cash balance from SocietyYearly
      ));

      // Row 2: FIXED - Compulsory Subscription + Empty Asset Side
      newRows.add(_createBalanceSheetRow(
        _safeValue(previousYearData[
            'totalSubscription']), // Previous year subscription
        'COMPULSORY SUBSCRIPTION',
        _safeValue(
            currentYearData['totalSubscription']), // Current year subscription
        0, // Empty asset side
        '', // Empty asset title
        0, // Empty asset current
      ));

      // Row 3: FIXED - Interest Payable on Subscription @ 6.5% + Empty Asset Side
      newRows.add(_createBalanceSheetRow(
        _safeValue(
            previousYearData['intOnSubscription']), // Previous year interest
        'INTEREST PAYABLE ON SUBSCRIPTION',
        _safeValue(
            currentYearData['intOnSubscription']), // Current year interest
        0, // Empty asset side
        '', // Empty asset title
        0, // Empty asset current
      ));

      // === DYNAMIC ROWS FROM BALANCE SHEET + PROFIT DISTRIBUTION ===

      // Fetch last year's balance sheet data
      final lastYearBalanceSheetData =
          await _fetchLastYearBalanceSheetData(previousYear);

      // Fetch last year's profit distribution data
      final lastYearProfitDistData =
          await _fetchProfitDistributionData(previousYear);

      // Fetch current year's profit distribution data
      final currentYearProfitDistData =
          await _fetchProfitDistributionData(selectedYear);

      // Find common entries and create dynamic rows
      final dynamicRows = _createDynamicRows(
        lastYearBalanceSheetData,
        lastYearProfitDistData,
        currentYearProfitDistData,
      );

      // Add dynamic rows after the fixed rows
      newRows.addAll(dynamicRows);

      // Add totals row at the end
      final totalsRow = _createTotalsRow(newRows);
      newRows.add(totalsRow);

      setState(() {
        rows = newRows;
        // Only append rows if stateManager is initialized
        try {
          stateManager.appendRows(rows);
        } catch (e) {
          // stateManager not initialized yet, ignore
        }
      });
    } catch (e) {
      if (mounted) {
        // Use post-frame callback to show snackbar after build is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            showCtcAppSnackBar(context, 'An error occurred: ${e.toString()}');
          }
        });
      }
      setState(() {
        rows = [];
        // Only remove rows if stateManager is initialized
        try {
          stateManager.removeAllRows();
        } catch (e) {
          // stateManager not initialized yet, ignore
        }
      });
    }
  }

  /// Fetch financial data for a specific year from SocietyYearlyRecord
  Future<Map<String, dynamic>> _fetchYearData(int year) async {
    try {
      final querySnap = await FBFireStore.societyYearly
          .where("selectedyear", isEqualTo: year)
          .get();

      if (querySnap.docs.isEmpty) {
        return _getEmptyYearData();
      }

      // Aggregate data from all district offices for the year
      num totalShareGiven = 0;
      num totalSubscription = 0;
      num intOnSubscription = 0;
      num cashBalance = 0;
      num ltPendingLoan = 0;
      num stPendingLoan = 0;
      num totalExpenses = 0;
      num totalDivident = 0;

      for (var doc in querySnap.docs) {
        final data = doc.data();

        totalShareGiven += _safeValue(data['totalShareGiven']);
        totalSubscription += _safeValue(data['totalSubscription']);
        intOnSubscription += _safeValue(data['intOnSubscription']);
        cashBalance += _safeValue(data['cashBalance']);
        ltPendingLoan += _safeValue(data['ltPendingLoan']);
        stPendingLoan += _safeValue(data['stPendingLoan']);
        totalExpenses += _safeValue(data['totalExpenses']);
        totalDivident += _safeValue(data['totalDivident']);
      }

      Map<String, dynamic> aggregatedData = {
        'totalShareGiven': totalShareGiven,
        'totalSubscription': totalSubscription,
        'intOnSubscription': intOnSubscription,
        'cashBalance': cashBalance,
        'ltPendingLoan': ltPendingLoan,
        'stPendingLoan': stPendingLoan,
        'totalExpenses': totalExpenses,
        'totalDividend': totalDivident,
      };

      return aggregatedData;
    } catch (e) {
      print('Error fetching year data for $year: $e');
      return _getEmptyYearData();
    }
  }

  /// Get empty year data structure
  Map<String, dynamic> _getEmptyYearData() {
    return {
      'totalShareGiven': 0,
      'totalSubscription': 0,
      'intOnSubscription': 0,
      'cashBalance': 0,
      'ltPendingLoan': 0,
      'stPendingLoan': 0,
      'totalExpenses': 0,
      'totalDividend': 0,
    };
  }

  /// Fetch cash balance from SocietyYearly collection for a specific year
  Future<num> _fetchCashBalanceFromSocietyYearly(int year) async {
    try {
      final societyYearlySnap = await FBFireStore.societyYearly
          .where('selectedyear', isEqualTo: year)
          .get();

      if (societyYearlySnap.docs.isEmpty) {
        return 0;
      }

      // Aggregate cash balance from all district offices for the year
      num totalCashBalance = 0;
      for (var doc in societyYearlySnap.docs) {
        final data = doc.data();
        totalCashBalance += _safeValue(data['cashBalance'] ?? 0);
      }

      return totalCashBalance;
    } catch (e) {
      print('Error fetching cash balance from SocietyYearly for $year: $e');
      return 0;
    }
  }

  /// Create a balance sheet row with both liability and asset sides
  /// This matches the format shown in the provided image
  PlutoRow _createBalanceSheetRow(
    num liabilityPreviousYear,
    String liabilityTitle,
    num liabilityCurrentYear,
    num assetPreviousYear,
    String assetTitle,
    num assetCurrentYear,
  ) {
    return PlutoRow(cells: {
      'year': PlutoCell(value: _safeValue(liabilityPreviousYear)),
      'particulars': PlutoCell(value: liabilityTitle),
      'year2': PlutoCell(value: _safeValue(liabilityCurrentYear)),
      'year3': PlutoCell(value: _safeValue(assetPreviousYear)),
      'assets_and_loan': PlutoCell(value: assetTitle),
      'year4': PlutoCell(value: _safeValue(assetCurrentYear)),
    });
  }

  /// Fetch last year's balance sheet data from Firebase
  Future<List<Map<String, dynamic>>> _fetchLastYearBalanceSheetData(
      int year) async {
    try {
      final balanceSheetSnap =
          await FBFireStore.balancesheet.where('year', isEqualTo: year).get();

      if (balanceSheetSnap.docs.isEmpty) {
        return [];
      }

      // Return the balance sheet entries for the year
      List<Map<String, dynamic>> entries = [];
      for (var doc in balanceSheetSnap.docs) {
        final data = doc.data();
        entries.add({
          'title': data['title'] ?? '',
          'amount': data['amount'] ?? 0,
          'year': data['year'] ?? year,
        });
      }
      return entries;
    } catch (e) {
      print('Error fetching last year balance sheet data for $year: $e');
      return [];
    }
  }

  /// Fetch profit distribution data from Firebase
  Future<List<Map<String, dynamic>>> _fetchProfitDistributionData(
      int year) async {
    try {
      final profitDistSnap = await FBFireStore.profitDistribution
          .where('year', isEqualTo: year)
          .get();

      return profitDistSnap.docs.map((doc) {
        final data = doc.data();
        return {
          'title': data['title'] ?? '',
          'amount': data['amount'] ?? 0,
          'percentage': data['percentage'] ?? 0,
          'totalAmt': data['totalAmt'] ?? 0,
          'year': data['year'] ?? year,
        };
      }).toList();
    } catch (e) {
      print('Error fetching profit distribution data for $year: $e');
      return [];
    }
  }

  /// Create dynamic rows from balance sheet and profit distribution data
  List<PlutoRow> _createDynamicRows(
    List<Map<String, dynamic>> lastYearBalanceSheet,
    List<Map<String, dynamic>> lastYearProfitDist,
    List<Map<String, dynamic>> currentYearProfitDist,
  ) {
    List<PlutoRow> dynamicRows = [];

    // Create a map of current year profit distribution for quick lookup
    Map<String, Map<String, dynamic>> currentProfitDistMap = {};
    for (var entry in currentYearProfitDist) {
      currentProfitDistMap[entry['title']] = entry;
    }

    // Create a map of last year profit distribution for quick lookup
    Map<String, Map<String, dynamic>> lastYearProfitDistMap = {};
    for (var entry in lastYearProfitDist) {
      lastYearProfitDistMap[entry['title']] = entry;
    }

    // Find common entries between last year balance sheet and profit distribution
    for (var balanceSheetEntry in lastYearBalanceSheet) {
      String title = balanceSheetEntry['title'];

      // Check if this entry exists in profit distribution (either last year or current year)
      if (lastYearProfitDistMap.containsKey(title) ||
          currentProfitDistMap.containsKey(title)) {
        // Get last year values
        num lastYearBalanceSheetAmount =
            _safeValue(balanceSheetEntry['amount']);
        num lastYearProfitDistAmount =
            _safeValue(lastYearProfitDistMap[title]?['amount'] ?? 0);
        num lastYearTotal =
            lastYearBalanceSheetAmount + lastYearProfitDistAmount;

        // Get current year profit distribution amount
        num currentYearProfitDistAmount =
            _safeValue(currentProfitDistMap[title]?['amount'] ?? 0);

        // Calculate current year total: Last year total + Current year profit distribution
        num currentYearTotal = lastYearTotal + currentYearProfitDistAmount;

        // Create row with liability side populated
        dynamicRows.add(_createBalanceSheetRow(
          lastYearTotal, // Previous year (left side)
          title.toUpperCase(), // Title
          currentYearTotal, // Current year (left side)
          0, // Empty asset side previous year
          '', // Empty asset title
          0, // Empty asset side current year
        ));
      }
    }

    // Also check for entries that exist in profit distribution but not in balance sheet
    for (var profitDistEntry in lastYearProfitDist) {
      String title = profitDistEntry['title'];

      // Skip if already processed above
      bool alreadyProcessed =
          lastYearBalanceSheet.any((bs) => bs['title'] == title);
      if (alreadyProcessed) continue;

      // Get last year profit distribution amount
      num lastYearProfitDistAmount = _safeValue(profitDistEntry['amount']);

      // Get current year profit distribution amount
      num currentYearProfitDistAmount =
          _safeValue(currentProfitDistMap[title]?['amount'] ?? 0);

      // Calculate current year total: Last year amount + Current year profit distribution
      num currentYearTotal =
          lastYearProfitDistAmount + currentYearProfitDistAmount;

      // Create row with liability side populated
      dynamicRows.add(_createBalanceSheetRow(
        lastYearProfitDistAmount, // Previous year (left side)
        title.toUpperCase(), // Title
        currentYearTotal, // Current year (left side)
        0, // Empty asset side previous year
        '', // Empty asset title
        0, // Empty asset side current year
      ));
    }

    // Handle case where there's no last year data but current year profit distribution exists
    if (lastYearBalanceSheet.isEmpty &&
        lastYearProfitDist.isEmpty &&
        currentYearProfitDist.isNotEmpty) {
      for (var currentProfitDistEntry in currentYearProfitDist) {
        String title = currentProfitDistEntry['title'];

        // Get current year profit distribution amount
        num currentYearProfitDistAmount =
            _safeValue(currentProfitDistEntry['amount']);

        // Create row with 0 for last year and current year amount for current year
        dynamicRows.add(_createBalanceSheetRow(
          0, // Previous year (0 since no last year data)
          title.toUpperCase(), // Title
          currentYearProfitDistAmount, // Current year (only current year profit distribution)
          0, // Empty asset side previous year
          '', // Empty asset title
          0, // Empty asset side current year
        ));
      }
    }

    // Handle current year profit distribution entries that don't exist in last year data
    // This covers cases where there are new entries in current year that weren't in last year
    if (currentYearProfitDist.isNotEmpty) {
      for (var currentProfitDistEntry in currentYearProfitDist) {
        String title = currentProfitDistEntry['title'];

        // Check if this entry was already processed above
        bool alreadyProcessed = dynamicRows.any((row) =>
            row.cells['particulars']?.value.toString().toUpperCase() ==
            title.toUpperCase());

        if (alreadyProcessed) continue;

        // This is a new entry that doesn't exist in last year data
        num currentYearProfitDistAmount =
            _safeValue(currentProfitDistEntry['amount']);

        // Create row with 0 for last year and current year amount for current year
        dynamicRows.add(_createBalanceSheetRow(
          0, // Previous year (0 since this entry didn't exist last year)
          title.toUpperCase(), // Title
          currentYearProfitDistAmount, // Current year (only current year profit distribution)
          0, // Empty asset side previous year
          '', // Empty asset title
          0, // Empty asset side current year
        ));
      }
    }

    return dynamicRows;
  }

  /// Create totals row for the balance sheet
  PlutoRow _createTotalsRow(List<PlutoRow> rows) {
    num totalPrevLiability = 0;
    num totalCurrentLiability = 0;
    num totalPrevAsset = 0;
    num totalCurrentAsset = 0;

    // Calculate totals from all rows (excluding the totals row itself)
    for (var row in rows) {
      totalPrevLiability += _safeValue(row.cells['year']?.value ?? 0);
      totalCurrentLiability += _safeValue(row.cells['year2']?.value ?? 0);
      totalPrevAsset += _safeValue(row.cells['year3']?.value ?? 0);
      totalCurrentAsset += _safeValue(row.cells['year4']?.value ?? 0);
    }

    return PlutoRow(cells: {
      'year': PlutoCell(value: totalPrevLiability),
      'particulars': PlutoCell(value: 'TOTAL'),
      'year2': PlutoCell(value: totalCurrentLiability),
      'year3': PlutoCell(value: totalPrevAsset),
      'assets_and_loan': PlutoCell(value: 'TOTAL'),
      'year4': PlutoCell(value: totalCurrentAsset),
    });
  }

  /// Delete a dynamic row from both UI and Firebase
  Future<void> _deleteDynamicRow(String title, int year) async {
    try {
      // Delete from Firebase profit distribution collection
      final profitDistSnap = await FBFireStore.profitDistribution
          .where('title', isEqualTo: title)
          .where('year', isEqualTo: year)
          .get();

      for (var doc in profitDistSnap.docs) {
        await doc.reference.delete();
      }

      // Delete from Firebase balance sheet collection if exists
      final balanceSheetSnap = await FBFireStore.balancesheet
          .where('title', isEqualTo: title)
          .where('year', isEqualTo: year)
          .get();

      for (var doc in balanceSheetSnap.docs) {
        await doc.reference.delete();
      }

      // Refresh the balance sheet
      await addBsRows(year: yearlyBsSelectedYear);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$title deleted successfully')),
        );
      }
    } catch (e) {
      print('Error deleting row: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting $title: $e')),
        );
      }
    }
  }

  /// Edit a dynamic row in Firebase
  Future<void> _editDynamicRow(String title, int year, num newAmount) async {
    try {
      // Update in Firebase profit distribution collection
      final profitDistSnap = await FBFireStore.profitDistribution
          .where('title', isEqualTo: title)
          .where('year', isEqualTo: year)
          .get();

      if (profitDistSnap.docs.isNotEmpty) {
        await profitDistSnap.docs.first.reference.update({
          'amount': newAmount,
          'totalAmt': newAmount,
        });
      } else {
        // Create new entry if doesn't exist
        await FBFireStore.profitDistribution.add({
          'title': title,
          'amount': newAmount,
          'percentage': 0,
          'totalAmt': newAmount,
          'year': year,
          'createdAt': Timestamp.now(),
        });
      }

      // Refresh the balance sheet
      await addBsRows(year: yearlyBsSelectedYear);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$title updated successfully')),
        );
      }
    } catch (e) {
      print('Error editing row: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating $title: $e')),
        );
      }
    }
  }

  /// Show edit dialog for dynamic rows
  void _showEditDialog(String title, num currentAmount) {
    final TextEditingController amountController =
        TextEditingController(text: currentAmount.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $title'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final newAmount = num.tryParse(amountController.text) ?? 0;
              Navigator.pop(context);
              await _editDynamicRow(
                  title,
                  yearlyBsSelectedYear ??
                      TheFinancialYear.getCurrentFinancialYearStartYear(),
                  newAmount);
            },
            child: const Text('Save'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteDynamicRow(
                  title,
                  yearlyBsSelectedYear ??
                      TheFinancialYear.getCurrentFinancialYearStartYear());
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// Handle row double tap for editing dynamic rows
  void _handleRowDoubleTap(PlutoRow row) {
    final title = row.cells['particulars']?.value?.toString() ?? '';
    final currentAmount = _safeValue(row.cells['year2']?.value ?? 0);

    // Only allow editing of dynamic rows (not fixed rows and not totals row)
    if (title.isEmpty ||
        title == 'ISSUED & SUBSCRIBED SHARE CAPITAL' ||
        title == 'COMPULSORY SUBSCRIPTION' ||
        title == 'INTEREST PAYABLE ON SUBSCRIPTION' ||
        title == 'TOTAL') {
      return;
    }

    _showEditDialog(title, currentAmount);
  }

  /// Safely convert values to numbers, ensuring no null or negative values
  num _safeValue(dynamic value) {
    if (value == null) return 0;
    final numValue = num.tryParse(value.toString()) ?? 0;
    return numValue < 0 ? 0 : numValue;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.only(top: 40, left: 15, right: 15),
        child: Column(children: [
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
                    focusColor: Colors.transparent,
                    dropdownColor: Colors.white,
                    value: yearlyBsSelectedYear,
                    decoration: InputDecoration(
                        hintText: "Select Financial Year",
                        constraints:
                            const BoxConstraints(maxWidth: 200, maxHeight: 45),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5))),
                    items: List.generate(
                      yearList.length,
                      (index) {
                        return DropdownMenuItem(
                          value: yearList[index],
                          child: Text(TheFinancialYear.startYearToFinancialYear(
                              yearList[index])),
                        );
                      },
                    ),
                    onChanged: (value) async {
                      if (value == null) return;

                      setState(() {
                        yearlyBsSelectedYear = value;
                        yearlyBsSelectedFinancialYear =
                            TheFinancialYear.startYearToFinancialYear(value);
                      });

                      await addBsRows(year: yearlyBsSelectedYear);
                    })),
            Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomHeaderButton(
                    onPressed: () async {
                      await showAddRowDialog();
                    },
                    buttonName: 'Add',
                  ),
                  SizedBox(width: 5),
                  CustomHeaderButton(
                    onPressed: () async {
                      await exportToCSV();
                    },
                    buttonName: 'Export to CSV',
                  ),
                  SizedBox(width: 5),
                  CustomHeaderButton(
                    onPressed: () async {
                      await exportToPDF();
                    },
                    buttonName: 'Export to PDF',
                    // child: Text(''),
                  ),
                  SizedBox(width: 5),
                  CustomHeaderButton(
                    onPressed: () async {
                      // await exportToPDF();
                    },
                    buttonName: 'Save',
                    // child: Text(''),
                  ),
                  // SizedBox(width: 5),
                  // slIsLoading
                  //     ? CircularProgressIndicator()
                  // //     :
                  // CustomHeaderButton(
                  //     onPressed: () async {
                  //       // await slOnSave(ctrl);
                  //     },
                  //     buttonName: 'SAVE')
                ])
          ]),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 22),
            child: Text(
                "BALANCE SHEET FOR THE YEAR ${yearlyBsSelectedFinancialYear ?? TheFinancialYear.getCurrentFinancialYear()}",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
          ),
          Expanded(
              child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.transparent)),
                  // height: size.height, //  - 30,
                  child: PlutoGrid(
                    columns: columns,
                    rows: rows,
                    onLoaded: (PlutoGridOnLoadedEvent event) {
                      stateManager = event.stateManager;
                      stateManager.setShowColumnFilter(true);
                      stateManager.notifyListeners();
                      stateManager.notifyListenersOnPostFrame();
                    },
                    onChanged: (PlutoGridOnChangedEvent event) {},
                    onRowDoubleTap: (PlutoGridOnRowDoubleTapEvent event) {
                      _handleRowDoubleTap(event.row);
                    },
                    configuration: const PlutoGridConfiguration(
                        columnSize: PlutoGridColumnSizeConfig(
                            autoSizeMode: PlutoAutoSizeMode.equal),
                        scrollbar: PlutoGridScrollbarConfig(
                            draggableScrollbar: true,
                            isAlwaysShown: true,
                            scrollbarThickness:
                                PlutoScrollbar.defaultThicknessWhileDragging)),
                  )))
        ]),
      );
    });
  }

  Future<void> showAddRowDialog() async {
    // final sectionController = TextEditingController();
    final particularsController = TextEditingController();
    final year1Controller = TextEditingController();
    final year2Controller = TextEditingController();

    String selectedSection = 'Share Capital & Liability';

    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: Text('Add Row to Balance Sheet'),
          content: StatefulBuilder(
            builder: (context, setState) => SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Theme(
                    data: Theme.of(context).copyWith(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                    ),
                    child: DropdownButton<String>(
                      dropdownColor: Colors.white,
                      focusColor: Colors.transparent,
                      value: selectedSection,
                      items: ['Share Capital & Liability', 'Assets and Loan']
                          .map(
                              (e) => DropdownMenuItem(value: e, child: Text(e)))
                          .toList(),
                      onChanged: (val) {
                        if (val != null) setState(() => selectedSection = val);
                      },
                    ),
                  ),
                  SizedBox(height: 10),
                  TextField(
                    controller: particularsController,
                    decoration: InputDecoration(
                        labelText:
                            selectedSection == 'Share Capital & Liability'
                                ? 'Particulars'
                                : 'Assets and Loan'),
                  ),
                  SizedBox(height: 10),
                  TextField(
                    controller: year1Controller,
                    keyboardType: TextInputType.number,
                    decoration:
                        InputDecoration(labelText: 'Previous FY Amount'),
                  ),
                  SizedBox(height: 10),
                  TextField(
                    controller: year2Controller,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(labelText: 'Current FY Amount'),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context), child: Text('Cancel')),
            addingRow
                ? Center(child: CircularProgressIndicator())
                : ElevatedButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(Colors.green),
                      elevation: WidgetStatePropertyAll(0),
                      foregroundColor: WidgetStatePropertyAll(Colors.white),
                      shape:
                          WidgetStatePropertyAll(ContinuousRectangleBorder()),
                      // padding: WidgetStatePropertyAll(
                      //     EdgeInsets.symmetric(horizontal: 40, vertical: 15)),
                    ),
                    onPressed: () async {
                      final particulars = particularsController.text.trim();
                      final year1Text = year1Controller.text.trim();
                      final year2Text = year2Controller.text.trim();

                      if (particulars.isEmpty ||
                          year1Text.isEmpty ||
                          year2Text.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Please fill in all fields')),
                        );
                        return;
                      }

                      setState(() {
                        addingRow = true; // show loader
                      });

                      try {
                        final year1Val = double.tryParse(year1Text);
                        final year2Val = double.tryParse(year2Text);

                        if (year1Val == null || year2Val == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content:
                                    Text('Please enter valid numeric values')),
                          );
                          setState(() {
                            addingRow = false; // hide loader
                          });
                          return;
                        }

                        PlutoRow newRow;

                        if (selectedSection == 'Share Capital & Liability') {
                          newRow = PlutoRow(cells: {
                            'year': PlutoCell(value: year1Val),
                            'particulars': PlutoCell(value: particulars),
                            'year2': PlutoCell(value: year2Val),
                            'year3': PlutoCell(value: 0),
                            'assets_and_loan': PlutoCell(value: ''),
                            'year4': PlutoCell(value: 0),
                          });
                        } else {
                          newRow = PlutoRow(cells: {
                            'year': PlutoCell(value: 0),
                            'particulars': PlutoCell(value: ''),
                            'year2': PlutoCell(value: 0),
                            'year3': PlutoCell(value: 0),
                            'assets_and_loan': PlutoCell(value: particulars),
                            'year4': PlutoCell(value: year2Val),
                          });
                        }

                        setState(() {
                          rows.add(newRow);
                          stateManager.appendRows([newRow]);
                        });

                        Navigator.pop(context);
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content:
                                  Text('Error adding row: ${e.toString()}')),
                        );
                      } finally {
                        setState(() {
                          addingRow = false; // always hide loader
                        });
                      }
                    },
                    child: Text('Add'))
          ],
        );
      },
    );
  }

// CSV EXPORT FUNCTION
  Future<void> exportToCSV() async {
    try {
      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        return;
      }

      final csv = StringBuffer();

      // Define headers according to Balance Sheet columns
      final currentFY = TheFinancialYear.getCurrentFinancialYear();
      final previousFY = TheFinancialYear.startYearToFinancialYear(
          TheFinancialYear.getCurrentFinancialYearStartYear() - 1);

      final headers = [
        previousFY,
        'SHARE CAPITAL & LIABILITY',
        currentFY,
        previousFY,
        'ASSETS AND LOAN',
        currentFY,
      ];

      // Write CSV headers
      csv.writeln(headers.map((h) => '"$h"').join(','));

      for (var row in stateManager.rows) {
        final rowData = [
          row.cells['year']?.value?.toString() ?? '',
          row.cells['particulars']?.value?.toString() ?? '',
          row.cells['year2']?.value?.toString() ?? '',
          row.cells['year3']?.value?.toString() ?? '',
          row.cells['assets_and_loan']?.value?.toString() ?? '',
          row.cells['year4']?.value?.toString() ?? '',
        ];

        final quotedRow =
            rowData.map((field) => '"${field.replaceAll('"', '""')}"');
        csv.writeln(quotedRow.join(','));
      }

      final csvBytes = html.Blob([csv.toString()], 'text/csv');
      final csvUrl = html.Url.createObjectUrlFromBlob(csvBytes);

      final anchor = html.AnchorElement(href: csvUrl)
        ..setAttribute('download',
            'balance_sheet_${yearlyBsSelectedFinancialYear ?? TheFinancialYear.getCurrentFinancialYear()}.csv')
        ..click();

      html.Url.revokeObjectUrl(csvUrl);
    } catch (e) {
      showCtcAppSnackBar(context, "Failed to export CSV: ${e.toString()}");
    }
  }

// PDF EXPORT FUNCTION (
  Future<void> exportToPDF() async {
    try {
      if (stateManager.rows.isEmpty) {
        showCtcAppSnackBar(context, "No data to export");
        return;
      }

      final pdf = pw.Document();

      final currentFY = TheFinancialYear.getCurrentFinancialYear();
      final previousFY = TheFinancialYear.startYearToFinancialYear(
          TheFinancialYear.getCurrentFinancialYearStartYear() - 1);

      final headers = [
        previousFY,
        'SHARE CAPITAL & LIABILITY',
        currentFY,
        previousFY,
        'ASSETS AND LOAN',
        currentFY,
      ];

      final data = stateManager.rows
          .map((row) => [
                row.cells['year']?.value?.toString() ?? '',
                row.cells['particulars']?.value?.toString() ?? '',
                row.cells['year2']?.value?.toString() ?? '',
                row.cells['year3']?.value?.toString() ?? '',
                row.cells['assets_and_loan']?.value?.toString() ?? '',
                row.cells['year4']?.value?.toString() ?? '',
              ])
          .toList();

      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a3.landscape,
          margin: pw.EdgeInsets.all(10),
          build: (context) => [
            pw.Text(
              'BALANCE SHEET FOR THE YEAR ${yearlyBsSelectedFinancialYear ?? TheFinancialYear.getCurrentFinancialYear()}',
              style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 10),
            pw.Table.fromTextArray(
              headers: headers,
              data: data,
              cellAlignment: pw.Alignment.centerLeft,
              headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
              border: pw.TableBorder.all(width: 0.5),
              cellPadding: const pw.EdgeInsets.all(5),
            ),
          ],
        ),
      );

      final bytes = await pdf.save();

      final blob = html.Blob([bytes], 'application/pdf');
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download',
            'balance_sheet_${yearlyBsSelectedFinancialYear ?? TheFinancialYear.getCurrentFinancialYear()}.pdf')
        ..click();
      html.Url.revokeObjectUrl(url);
    } catch (e) {
      showCtcAppSnackBar(context, "Failed to export PDF: ${e.toString()}");
    }
  }
}
