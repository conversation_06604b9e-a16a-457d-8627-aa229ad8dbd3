// ignore_for_file: avoid_print, use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/new_user_application_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher_string.dart';
import '../../common/page_header.dart';
import '../../shared/firebase.dart';
import 'buttons/newuser_accept_button.dart';
import 'buttons/newuser_edit_button.dart';
import 'buttons/newuser_reject_button.dart';

class NewUserApplication extends StatefulWidget {
  const NewUserApplication({super.key});

  @override
  State<NewUserApplication> createState() => _NewUserApplicationState();
}

SearchController sctrl = SearchController();

class _NewUserApplicationState extends State<NewUserApplication> {
  bool enabled = false;

  String? selectedoffice;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<NewUserApplicationModel> filteredList = ctrl.newusersapplication;
      if ((selectedoffice != null) && (selectedoffice != "all")) {
        filteredList = ctrl.newusersapplication
            .where((element) => element.districtoffice == selectedoffice)
            .where(
          (element) {
            bool search = element.name.contains(sctrl.text) ||
                element.email.contains(sctrl.text) ||
                element.cpfNo.toString().contains(sctrl.text);

            return search;
          },
        ).toList();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomSearchBarWidget(
                  searchController: sctrl,
                  searchOnChanged: (p0) {
                    setState(() {});
                  },
                ),
                // DropdownButtonHideUnderline(
                //     child: DropdownButtonFormField(
                //   decoration: InputDecoration(
                //       hintText: "Select DO",
                //       constraints: const BoxConstraints(maxWidth: 450),
                //       border: OutlineInputBorder(
                //           borderRadius: BorderRadius.circular(10))),
                //   value: selectedoffice,
                //   items: [
                //     const DropdownMenuItem(value: 'all', child: Text("All")),
                //     ...List.generate(
                //       ctrl.districtoffice.length,
                //       (index) {
                //         return DropdownMenuItem(
                //             value: ctrl.districtoffice[index].docId,
                //             child: Text(ctrl.districtoffice[index].name));
                //       },
                //     ),
                //   ],
                //   onChanged: (value) {
                //     setState(() {
                //       selectedoffice = value;
                //     });
                //   },
                // )),
                // const SizedBox(width: 10),
                CustomHeaderButton(
                    onPressed: () {
                      context.go(Routes.rejectedUser);
                    },
                    buttonName: "Rejected Applications"),
              ],
            ),
            const SizedBox(height: 50),
            const Row(
              children: [
                HeaderTxt(txt: "Sr.No."),
                HeaderTxt(txt: "CPF No."),
                HeaderTxt(txt: "Employee No."),
                HeaderTxt(txt: "Name"),
                HeaderTxt(
                  txt: "Email",
                  twice: true,
                ),
                HeaderTxt(txt: "DO"),
              ],
            ),
            const SizedBox(height: 20),
            ...List.generate(
              filteredList.length,
              (index) {
                final singleUserDo = ctrl.districtoffice.firstWhereOrNull(
                    (element) =>
                        element.docId == filteredList[index].districtoffice);
                return InkWell(
                  onTap: () =>
                      newuserformdialog(context, filteredList[index], enabled),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(filteredList[index].cpfNo.toString()),
                          ),
                          Expanded(
                              child: Text(
                                  filteredList[index].employeeNo.toString())),
                          Expanded(
                              child:
                                  Text(filteredList[index].name.toUpperCase())),
                          Expanded(
                              flex: 2,
                              child: Text(
                                  overflow: TextOverflow.ellipsis,
                                  filteredList[index].email)),
                          Expanded(child: Text(singleUserDo?.name ?? "")),
                        ],
                      ),
                    ),
                  ),
                );
              },
            )
          ],
        ),
      );
    });
  }
}

Future<dynamic> newuserformdialog(
    BuildContext context, NewUserApplicationModel filteredList, bool enabled) {
  bool loader = false;

  TextEditingController namectrl = TextEditingController();
  TextEditingController emailctrl = TextEditingController();
  TextEditingController addresssctrl = TextEditingController();
  TextEditingController paddressctrl = TextEditingController();
  TextEditingController empnoctrl = TextEditingController();
  TextEditingController cpfnoctrl = TextEditingController();
  TextEditingController phonenumberctrl = TextEditingController();
  TextEditingController doctrl = TextEditingController();
  TextEditingController bankacnamectrl = TextEditingController();
  TextEditingController banknamectrl = TextEditingController();
  TextEditingController ifsccodectrl = TextEditingController();
  TextEditingController bankacnoctrl = TextEditingController();
  TextEditingController sharevaluectrl = TextEditingController();
  TextEditingController nomineeNamectrl = TextEditingController();
  TextEditingController nomineeRelationctrl = TextEditingController();

  String? selectedoffice;

  loadData() {
    namectrl.text = filteredList.name.toString();
    emailctrl.text = filteredList.email.toString();
    addresssctrl.text = filteredList.currentAddress.toString();
    paddressctrl.text = filteredList.permanentAddress.toString();
    empnoctrl.text = filteredList.employeeNo.toString();
    cpfnoctrl.text = filteredList.cpfNo.toString();
    selectedoffice = filteredList.districtoffice.toString();
    phonenumberctrl.text = filteredList.phoneNo.toString();
    doctrl.text = filteredList.districtoffice.toString();
    bankacnamectrl.text = filteredList.bankAcName.toString();
    banknamectrl.text = filteredList.bankName.toString();
    ifsccodectrl.text = filteredList.ifscCode.toString();
    bankacnoctrl.text = filteredList.bankAcNo.toString();
  }

  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      backgroundColor: Colors.white,
      // scrollable: true,
      content: StatefulBuilder(
        builder:
            (BuildContext context, void Function(void Function()) setState2) {
          return ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 600),
            child: SizedBox(
              width: 800,
              child: GetBuilder<HomeCtrl>(builder: (hctrl) {
                loadData();
                return SingleChildScrollView(
                  padding: const EdgeInsets.all(25),
                  child: Stack(
                    children: [
                      Center(
                        child: Opacity(
                          opacity: 0.20,
                          child: Container(
                            width: 600,
                            decoration: const BoxDecoration(
                                image: DecorationImage(
                                    image:
                                        AssetImage("assets/foodcorpimage.png"),
                                    fit: BoxFit.fitWidth)),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          const Text(
                            "APPLICATION FOR ADMISSION AS A MEMBER",
                            style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 20),
                          TextFormField(
                              enabled: enabled,
                              decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                  label: Text('Enter Full Name'),
                                  labelStyle: TextStyle(color: Colors.black),
                                  hintStyle: TextStyle(
                                      color: Colors.black,
                                      fontSize: 17,
                                      fontWeight: FontWeight.w500)),
                              controller: namectrl),
                          const SizedBox(height: 20),
                          TextFormField(
                              enabled: enabled,
                              decoration: const InputDecoration(
                                  border: OutlineInputBorder(),
                                  label: Text('Enter Phone Number'),
                                  labelStyle: TextStyle(color: Colors.black),
                                  hintStyle: TextStyle(
                                      color: Colors.black,
                                      fontSize: 17,
                                      fontWeight: FontWeight.w500)),
                              controller: phonenumberctrl),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter Email'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    color: Colors.black,
                                    fontSize: 17,
                                    fontWeight: FontWeight.w500)),
                            controller: emailctrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter Employee Number'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    color: Colors.black,
                                    fontSize: 17,
                                    fontWeight: FontWeight.w500)),
                            controller: empnoctrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter CPF Number'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    color: Colors.black,
                                    fontSize: 17,
                                    fontWeight: FontWeight.w500)),
                            controller: cpfnoctrl,
                          ),
                          const SizedBox(height: 20),
                          filteredList.approved == false
                              ? IgnorePointer(
                                  ignoring: enabled ? false : true,
                                  child: DropdownButtonHideUnderline(
                                      child: DropdownButtonFormField(
                                    focusColor: Colors.transparent,
                                    dropdownColor: Colors.white,
                                    decoration: InputDecoration(
                                        hintText: "Select District Office",
                                        hintStyle: const TextStyle(
                                            fontSize: 19,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black),
                                        constraints:
                                            const BoxConstraints(maxWidth: 800),
                                        border: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color: enabled
                                                    ? Colors.black
                                                    : Colors.grey))),
                                    value: selectedoffice,
                                    items: [
                                      ...List.generate(
                                        hctrl.districtoffice.length,
                                        (index) {
                                          return DropdownMenuItem(
                                              value: hctrl
                                                  .districtoffice[index].docId,
                                              child: Text(
                                                hctrl
                                                    .districtoffice[index].name,
                                                style: TextStyle(
                                                    color: enabled
                                                        ? Colors.black
                                                        : Colors.grey),
                                              ));
                                        },
                                      )
                                    ],
                                    onChanged: (value) {
                                      setState2(() {
                                        selectedoffice = value;
                                      });
                                    },
                                  )),
                                )
                              : TextFormField(
                                  enabled: enabled,
                                  decoration: const InputDecoration(
                                      border: OutlineInputBorder(),
                                      label: Text('District Office'),
                                      labelStyle:
                                          TextStyle(color: Colors.black),
                                      hintStyle: TextStyle(
                                          fontSize: 17,
                                          fontWeight: FontWeight.w500)),
                                  controller: doctrl,
                                ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter Current Address'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: addresssctrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter Permanent Address'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: paddressctrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Nominee Name'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: nomineeNamectrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Nominee Relation'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: nomineeRelationctrl,
                          ),
                          const SizedBox(height: 30),

                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Bank Account Name *'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: bankacnamectrl,
                          ),
                          const SizedBox(height: 30),

                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Bank Name*'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: banknamectrl,
                          ),
                          const SizedBox(height: 30),

                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Bank Account No. *'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: bankacnoctrl,
                          ),
                          const SizedBox(height: 30),

                          TextFormField(
                            enabled: enabled,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Bank IFSC *'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: ifsccodectrl,
                          ),
                          const SizedBox(height: 30),
                          TextFormField(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return showCtcAppSnackBar(
                                    context, 'Please enter a share value');
                              }
                              return null;
                            },
                            enabled: true,
                            decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                label: Text('Enter Share Value (in ₹) *'),
                                labelStyle: TextStyle(color: Colors.black),
                                hintStyle: TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w500)),
                            controller: sharevaluectrl,
                          ),
                          const SizedBox(height: 50),
                          const Text(
                            "DOCUMENT : ",
                            style: TextStyle(
                              fontSize: 15,
                            ),
                          ),
                          const SizedBox(height: 20),
                          SizedBox(
                            width: MediaQuery.sizeOf(context).width,
                            height: 50,
                            child: ListTile(
                              title: Text(filteredList.documents),
                              trailing: IconButton(
                                onPressed: () async {
                                  launchUrlString(filteredList.documents);
                                },
                                icon: const Icon(Icons.download_outlined),
                              ),
                            ),
                          ),
                          const SizedBox(height: 50),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              (filteredList.isNew != false)
                                  ? NewUserRejectButton(
                                      onpressed: () async {
                                        context.pop();
                                        showDialog(
                                          context: context,
                                          builder: (context) => StatefulBuilder(
                                              builder: (context, refresh) {
                                            return AlertDialog(
                                              backgroundColor: Colors.white,
                                              title: const Text(
                                                  "Reject User Application?"),
                                              actions: loader
                                                  ? [
                                                      CircularProgressIndicator()
                                                    ]
                                                  : [
                                                      TextButton(
                                                          onPressed: () async {
                                                            // newuserpageloading = true;
                                                            // setState2(() {});
                                                            try {
                                                              loader = true;
                                                              refresh(
                                                                () {},
                                                              );
                                                              await FBFireStore
                                                                  .newuserapplication
                                                                  .doc(filteredList
                                                                      .docId)
                                                                  .update({
                                                                "isNew": false,
                                                                "approved":
                                                                    false
                                                              });

                                                              context.pop();
                                                              context.pop();

                                                              loader = false;
                                                              refresh(
                                                                () {},
                                                              );
                                                            } catch (e) {
                                                              debugPrint(
                                                                  e.toString());
                                                            }
                                                          },
                                                          child: const Text(
                                                              "Yes")),
                                                      TextButton(
                                                          onPressed: () async {
                                                            if (context
                                                                .mounted) {
                                                              context.pop();
                                                            }
                                                          },
                                                          child:
                                                              const Text("No")),
                                                    ],
                                            );
                                          }),
                                        );
                                      },
                                    )
                                  : SizedBox(),
                              (filteredList.isNew != false)
                                  ? NewUserAcceptButton(
                                      enabled: enabled,
                                      onPressed: () async {
                                        if (sharevaluectrl.text.isEmpty) {
                                          return showCtcAppSnackBar(
                                              context, "Enter Share Value");
                                        }
                                        context.pop();
                                        showDialog(
                                          context: context,
                                          builder: (context) => StatefulBuilder(
                                              builder: (context, refresh) {
                                            return AlertDialog(
                                              backgroundColor: Colors.white,
                                              title: const Text(
                                                  "Accept User Application?"),
                                              actions: loader
                                                  ? [
                                                      SizedBox(
                                                          height: 30,
                                                          width: 30,
                                                          child:
                                                              CircularProgressIndicator())
                                                    ]
                                                  : [
                                                      TextButton(
                                                          onPressed: () async {
                                                            try {
                                                              loader = true;
                                                              refresh(
                                                                () {},
                                                              );

                                                              final data =
                                                                  <String,
                                                                      dynamic>{
                                                                'name': namectrl
                                                                    .text
                                                                    .toUpperCase(),
                                                                'cpfNo': num
                                                                    .tryParse(
                                                                        cpfnoctrl
                                                                            .text),
                                                                'employeeNo':
                                                                    num.tryParse(
                                                                        empnoctrl
                                                                            .text),
                                                                'currentAddress':
                                                                    addresssctrl
                                                                        .text,
                                                                'permanentAddress':
                                                                    paddressctrl
                                                                        .text,
                                                                'documents':
                                                                    filteredList
                                                                        .documents,
                                                                'districtoffice':
                                                                    selectedoffice,
                                                                'email':
                                                                    emailctrl
                                                                        .text,
                                                                'phonenumber':
                                                                    phonenumberctrl
                                                                        .text,
                                                                // 'createdAt':
                                                                //     DateTime.now(),
                                                                'settlement': 0,
                                                                'totalSubs': 0,
                                                                'totalSubsInt':
                                                                    0,
                                                                'ltLoansDue': 0,
                                                                'stLoansDue': 0,
                                                                'totalLtLoans':
                                                                    0,
                                                                'totalStLoans':
                                                                    0,
                                                                'totalLtIntPaid':
                                                                    0,
                                                                'totalStIntPaid':
                                                                    0,
                                                                'totalDivident':
                                                                    0,
                                                                'totalShares':
                                                                    num.tryParse(
                                                                        sharevaluectrl
                                                                            .text),
                                                                'password':
                                                                    generateRandomId(
                                                                        8),
                                                                'approved':
                                                                    true,
                                                                'userPrevoiusMonthlyRecord':
                                                                    null,
                                                                'bankAcName':
                                                                    bankacnamectrl
                                                                        .text,
                                                                'bankName':
                                                                    banknamectrl
                                                                        .text,
                                                                'ifscCode':
                                                                    ifsccodectrl
                                                                        .text,
                                                                'bankAcNo': num
                                                                    .tryParse(
                                                                        bankacnoctrl
                                                                            .text),
                                                                'archived':
                                                                    false,
                                                                'nomineeName':
                                                                    nomineeNamectrl
                                                                        .text,
                                                                'nomineeRelation':
                                                                    nomineeRelationctrl
                                                                        .text,
                                                                'momento': null,
                                                                'obSubs': 0,
                                                                'obShares': num
                                                                    .tryParse(
                                                                        sharevaluectrl
                                                                            .text),
                                                                'obLt': 0,
                                                                'obSt': 0,
                                                              };

                                                              final functionName =
                                                                  testMode
                                                                      ? 'testCreateUser'
                                                                      : 'createUser';

                                                              final result =
                                                                  await FBFunctions
                                                                      .ff
                                                                      .httpsCallable(
                                                                          functionName)
                                                                      .call(
                                                                          data);

                                                              if (result.data[
                                                                  'success']) {
                                                                await FBFireStore
                                                                    .newuserapplication
                                                                    .doc(filteredList
                                                                        .docId)
                                                                    .update({
                                                                  "approved":
                                                                      true,
                                                                  "isNew": false
                                                                });
                                                              } else {
                                                                // context
                                                                //     .pop(); // close dialog
                                                                showCtcAppSnackBar(
                                                                    context,
                                                                    result.data[
                                                                            'msg'] ??
                                                                        "User already exists.");
                                                              }

                                                              FBFireStore
                                                                  .transactions
                                                                  .add({
                                                                "uId":
                                                                    filteredList
                                                                        .docId,
                                                                "createdAt":
                                                                    Timestamp
                                                                        .now(),
                                                                "title":
                                                                    "Share",
                                                                "amount": num.tryParse(
                                                                    sharevaluectrl
                                                                        .text),
                                                                "inn": false,
                                                                "userMonthlyId":
                                                                    null,
                                                                "recoveryId":
                                                                    null,
                                                              });

                                                              FBFireStore
                                                                  .notifications
                                                                  .add({
                                                                'uId':
                                                                    filteredList
                                                                        .docId,
                                                                'title':
                                                                    "Share",
                                                                'desc': num.tryParse(
                                                                    sharevaluectrl
                                                                        .text),
                                                                'type':
                                                                    "loanAccepted",
                                                                'districtOffice':
                                                                    filteredList
                                                                        .districtoffice,
                                                                'createdAt':
                                                                    Timestamp
                                                                        .now(),
                                                              });

                                                              // print(
                                                              //     "filteredlist.docid : ${filteredList.docId}");

                                                              context.pop();

                                                              // print(
                                                              //     'ress $result');
                                                              // print(
                                                              //     'ress ${result.data['msg']}');
                                                              loader = false;
                                                              refresh(
                                                                () {},
                                                              );
                                                            } catch (e) {
                                                              debugPrint(
                                                                  e.toString());
                                                            }
                                                          },
                                                          child: const Text(
                                                              "Yes")),
                                                      TextButton(
                                                          onPressed: () async {
                                                            if (context
                                                                .mounted) {
                                                              context.pop();
                                                            }
                                                          },
                                                          child:
                                                              const Text("No")),
                                                    ],
                                            );
                                          }),
                                        );
                                      },
                                    )
                                  : SizedBox.shrink(),
                              (filteredList.isNew != false)
                                  ? NewUserEditButton(onPressed: () {
                                      setState2(() {
                                        enabled = true;
                                      });
                                    })
                                  : SizedBox()
                            ],
                          )
                          // : SizedBox()
                        ],
                      ),
                    ],
                  ),
                );
              }),
            ),
          );
        },
      ),
    ),
  );
}
