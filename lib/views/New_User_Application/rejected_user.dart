import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/views/New_User_Application/new_user_application.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';

class RejectedUser extends StatefulWidget {
  const RejectedUser({super.key});

  @override
  State<RejectedUser> createState() => _RejectedUserState();
}

class _RejectedUserState extends State<RejectedUser> {
  String? selectedoffice;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      final filteredList = ctrl.usersapplicationrejected;
      if ((selectedoffice != null) && (selectedoffice != "all")) {
        filteredList
            .where((element) => element.districtoffice == selectedoffice)
            .toList();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DropdownButtonHideUnderline(
                child: DropdownButtonFormField(
              decoration: InputDecoration(
                  hintText: "Select DO",
                  constraints: const BoxConstraints(maxWidth: 450),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10))),
              value: selectedoffice,
              items: [
                const DropdownMenuItem(value: 'all', child: Text("All")),
                ...List.generate(
                  ctrl.districtoffice.length,
                  (index) {
                    return DropdownMenuItem(
                        value: ctrl.districtoffice[index].docId,
                        child: Text(ctrl.districtoffice[index].name));
                  },
                ),
              ],
              onChanged: (value) {
                setState(() {
                  selectedoffice = value;
                });
              },
            )),
            const SizedBox(height: 40),
            const Row(
              children: [
                HeaderTxt(txt: "Sr.No."),
                HeaderTxt(txt: "CPF No."),
                HeaderTxt(txt: "Employee No."),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "Email"),
                HeaderTxt(txt: "DO"),
              ],
            ),
            const SizedBox(height: 20),
            ...List.generate(
              filteredList.length,
              (index) {
                final singleUserDo = ctrl.districtoffice.firstWhere((element) =>
                    element.docId ==
                    ctrl.users[index].districtoffice.toString());
                return InkWell(
                  onTap: () =>
                      newuserformdialog(context, filteredList[index], false),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(filteredList[index].cpfNo.toString()),
                          ),
                          Expanded(
                              child: Text(ctrl
                                  .usersapplicationrejected[index].employeeNo
                                  .toString())),
                          Expanded(child: Text(filteredList[index].name)),
                          Expanded(
                              child: Text(
                                  overflow: TextOverflow.ellipsis,
                                  filteredList[index].email)),
                          Expanded(child: Text(singleUserDo.name)),
                        ],
                      ),
                    ),
                  ),
                );
              },
            )
          ],
        ),
      );
    });
  }
}
