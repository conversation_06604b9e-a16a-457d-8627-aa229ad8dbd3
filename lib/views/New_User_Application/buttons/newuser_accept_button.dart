import 'package:flutter/material.dart';

class NewUserAcceptButton extends StatelessWidget {
  const NewUserAcceptButton({
    super.key,
    required this.enabled,
    required this.onPressed,
  });

  final bool enabled;
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: const ButtonStyle(
          padding: WidgetStatePropertyAll(
              EdgeInsetsDirectional.symmetric(horizontal: 100, vertical: 15)),
          shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
          backgroundColor: WidgetStatePropertyAll(Colors.green),
          elevation: WidgetStatePropertyAll(0),
        ),
        onPressed: () {
          onPressed();
        },
        child: Text(
          enabled ? "SAVE" : "ACCEPT",
          style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              letterSpacing: 3,
              fontWeight: FontWeight.bold),
        ));
  }
}
