import 'package:flutter/material.dart';

class NewUserEditButton extends StatelessWidget {
  const NewUserEditButton({
    super.key,
    required this.onPressed,
  });
  final Function onPressed;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: const ButtonStyle(
          overlayColor: WidgetStatePropertyAll(Colors.transparent),
          foregroundColor: WidgetStatePropertyAll(Colors.transparent),
          padding: WidgetStatePropertyAll(
              EdgeInsetsDirectional.symmetric(horizontal: 40, vertical: 15)),
          // shape: WidgetStatePropertyAll(BeveledRectangleBorder(
          //     side: BorderSide(color: Colors.black, width: 0))),
          backgroundColor: WidgetStatePropertyAll(Colors.transparent),
          elevation: WidgetStatePropertyAll(0),
        ),
        onPressed: () {
          onPressed();
        },
        child: const Text(
          "Edit",
          style: TextStyle(
              color: Colors.black,
              fontSize: 16,
              letterSpacing: 3,
              fontWeight: FontWeight.normal),
        ));
  }
}
