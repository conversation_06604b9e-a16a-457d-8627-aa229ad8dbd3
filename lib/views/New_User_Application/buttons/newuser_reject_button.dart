import 'package:flutter/material.dart';

class NewUserRejectButton extends StatelessWidget {
  const NewUserRejectButton({
    super.key,
    required this.onpressed,
  });
  final Function onpressed;
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: const ButtonStyle(
          overlayColor: WidgetStatePropertyAll(Colors.transparent),
          foregroundColor: WidgetStatePropertyAll(Colors.transparent),
          shape: WidgetStatePropertyAll(BeveledRectangleBorder(
              side: BorderSide(color: Colors.black, width: 0))),
          backgroundColor: WidgetStatePropertyAll(Colors.transparent),
          elevation: WidgetStatePropertyAll(0),
        ),
        onPressed: () async {
          onpressed();
        },
        child: const Text(
          "Reject",
          style: TextStyle(
              color: Colors.black,
              fontSize: 16,
              letterSpacing: 3,
              fontWeight: FontWeight.normal),
        ));
  }
}
