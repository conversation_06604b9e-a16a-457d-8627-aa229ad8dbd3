import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:foodcorp_admin/views/Notifications/notifications_page.dart';
import 'package:foodcorp_admin/views/Notifications/poll_page.dart';
import 'package:go_router/go_router.dart';

class TabPage extends StatefulWidget {
  final int initialTab;
  const TabPage({super.key, this.initialTab = 0});

  @override
  State<TabPage> createState() => _TabPageState();
}

class _TabPageState extends State<TabPage> {
  late int _selectedTab;

  @override
  void initState() {
    super.initState();
    _selectedTab = widget.initialTab;
  }

  @override
  Widget build(BuildContext context) {
    final Map<int, Widget> segments = const {
      0: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Text('Notifications'),
      ),
      1: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Text('Polls'),
      ),
    };

    Widget content;
    switch (_selectedTab) {
      case 0:
        content = const NotificationPage();
        break;
      case 1:
        content = const PollPage();
        break;
      default:
        content = const NotificationPage();
    }

    return Scaffold(
      body: Column(
        children: [
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: Center(
                  child: CupertinoSlidingSegmentedControl<int>(
                    thumbColor: Colors.green.shade300,
                    groupValue: _selectedTab,
                    children: segments,
                    onValueChanged: (int? val) {
                      if (val != null) {
                        setState(() {
                          _selectedTab = val;
                        });
                      }
                    },
                  ),
                ),
              ),
              if (_selectedTab == 1)
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: IconButton(
                    iconSize: 27,
                    onPressed: () {
                      context.push(Routes.pollshistory);
                    },
                    icon: const Icon(Icons.history_outlined),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 20),
          Expanded(child: content),
        ],
      ),
    );
  }
}
