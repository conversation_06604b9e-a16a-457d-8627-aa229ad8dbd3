import 'package:flutter/material.dart';
import 'package:foodcorp_admin/models/poll_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';

class PollPage extends StatefulWidget {
  const PollPage({super.key});

  @override
  State<PollPage> createState() => _PollPageState();
}

class _PollPageState extends State<PollPage> {
  final _questionController = TextEditingController();
  final List<TextEditingController> _optionControllers = [];
  bool _isLoading = false;
  bool _allowsMultipleAnswers = false;
  bool _isActive = true;

  @override
  void initState() {
    super.initState();
    _addOption(); // Start with two options
    _addOption();
  }

  void _addOption() {
    setState(() {
      _optionControllers.add(TextEditingController());
    });
  }

  void _removeOption(int index) {
    setState(() {
      _optionControllers[index].dispose();
      _optionControllers.removeAt(index);
    });
  }

  void _submitPoll() async {
    final question = _questionController.text.trim();
    final options = _optionControllers
        .map((c) => c.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();

    if (question.isEmpty) {
      showCtcAppSnackBar(context, "Question cannot be empty");
      return;
    }
    if (options.length < 2) {
      showCtcAppSnackBar(context, "Please provide at least two options");
      return;
    }

    // Ensure "Others" is included
    if (!options.contains("Others")) {
      options.add("Others");
    }

    setState(() {
      _isLoading = true;
    });

    // Create poll object with updated options list
    final poll = PollModel(
      id: '', // This will be auto-generated by Firestore
      question: question,
      options: options,
      votes: {for (var opt in options) opt: 0},
      userVotes: {},
      isActive: _isActive,
      allowsMultipleAnswers: _allowsMultipleAnswers,
      expiryDate: null,
      topic: "global",
    );

    await FBFireStore.polls.add(poll.toSnap());

    // Reset UI and notify user as before
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isLoading = false;
        _questionController.clear();
        for (var c in _optionControllers) {
          c.clear();
        }
        _allowsMultipleAnswers = false;
        _isActive = true;
      });
      showCtcAppSnackBar(context, "Poll created successfully");
    });
  }

  // void _submitPoll() async {
  //   final question = _questionController.text.trim();
  //   final options = _optionControllers
  //       .map((c) => c.text.trim())
  //       .where((text) => text.isNotEmpty)
  //       .toList();

  //   if (question.isEmpty) {
  //     showCtcAppSnackBar(context, "Question cannot be empty");
  //     return;
  //   }
  //   if (options.length < 2) {
  //     showCtcAppSnackBar(context, "Please provide at least two options");
  //     return;
  //   }

  //   setState(() {
  //     _isLoading = true;
  //   });

  //   // Create PollModel instance with default empty votes and userVotes maps
  //   final poll = PollModel(
  //     id: '', // Assign appropriate ID (empty or generated by backend)
  //     question: question,
  //     options: options,
  //     votes: {for (var option in options) option: 0},
  //     userVotes: {},
  //     isActive: _isActive,
  //     allowsMultipleAnswers: _allowsMultipleAnswers,
  //     expiryDate: null, // Could be extended to add expiry date field
  //     topic: 'global',
  //   );

  //   await FBFireStore.polls.add(poll.toSnap());

  //   Future.delayed(const Duration(seconds: 1), () {
  //     setState(() {
  //       _isLoading = false;
  //       _questionController.clear();
  //       for (var c in _optionControllers) {
  //         c.clear();
  //       }
  //       _allowsMultipleAnswers = false; // Reset toggle
  //       _isActive = true; // Reset toggle
  //     });
  //     showCtcAppSnackBar(context, "Poll created successfully");
  //   });
  // }

  @override
  void dispose() {
    _questionController.dispose();
    for (var c in _optionControllers) {
      c.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            controller: _questionController,
            decoration: const InputDecoration(
              labelText: 'Poll Question',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),

          // Toggle for allowing multiple answers
          SwitchListTile(
            title: const Text('Allow multiple answers'),
            value: _allowsMultipleAnswers,
            onChanged: (val) {
              setState(() {
                _allowsMultipleAnswers = val;
              });
            },
          ),

          // Toggle for poll active state
          SwitchListTile(
            title: const Text('Poll is Active'),
            value: _isActive,
            onChanged: (val) {
              setState(() {
                _isActive = val;
              });
            },
          ),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _optionControllers.length,
            itemBuilder: (context, index) {
              return Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _optionControllers[index],
                      decoration: InputDecoration(
                        labelText: 'Option ${index + 1}',
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.remove_circle, color: Colors.red),
                    onPressed: _optionControllers.length > 2
                        ? () => _removeOption(index)
                        : null,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 10),
          Align(
            alignment: Alignment.centerLeft,
            child: TextButton.icon(
              onPressed: _addOption,
              icon: const Icon(Icons.add),
              label: const Text('Add Option'),
            ),
          ),
          const SizedBox(height: 20),
          _isLoading
              ? const CircularProgressIndicator()
              : ElevatedButton(
                  onPressed: _submitPoll,
                  child: const Text('Create Poll'),
                ),
        ],
      ),
    );
  }
}
