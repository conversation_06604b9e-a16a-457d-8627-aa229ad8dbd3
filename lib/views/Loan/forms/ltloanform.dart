// ignore_for_file: use_build_context_synchronously

import 'dart:typed_data';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:signature/signature.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../buttons/LT/lt_loan_edit_button.dart';

class ApprovedLtloanform extends StatefulWidget {
  const ApprovedLtloanform(
      {super.key, required this.index, required this.loan});

  final int index;
  final Loan loan;

  @override
  State<ApprovedLtloanform> createState() => _ApprovedLtloanformState();
}

class _ApprovedLtloanformState extends State<ApprovedLtloanform> {
  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController desigctrl = TextEditingController();
  TextEditingController bacnoctrl = TextEditingController();
  TextEditingController addressctrl = TextEditingController();
  TextEditingController appliedamtctrl = TextEditingController();
  TextEditingController appliedamtwordsctrl = TextEditingController();
  TextEditingController loanreasonctrl = TextEditingController();
  TextEditingController surityname1ctrl = TextEditingController();
  TextEditingController surityname2ctrl = TextEditingController();
  TextEditingController acno1ctrl = TextEditingController();
  TextEditingController acno2ctrl = TextEditingController();
  TextEditingController appLtinstallctrl = TextEditingController();
  TextEditingController approvedonctrl = TextEditingController();

  Uint8List? borrowersignatureImage;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool onSubmitLoad = false;
  // Future<String?> uploadSignimages(Uint8List file) async {
  //   try {
  //     final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
  //     final imageRef = FBStorage.fbstore.ref().child(path);

  //     final task = await imageRef.putData(file);
  //     var downloadurl = await task.ref.getDownloadURL();

  //     return await task.ref.getDownloadURL();
  //   } catch (e) {}
  // }

  bool enabled = false;

  @override
  void initState() {
    super.initState();
    appnoctrl.text = widget.loan.applicationNo.toString();
    fullnamectrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.name ??
        "";
    desigctrl.text = widget.loan.designation.toString();
    bacnoctrl.text = widget.loan.bAcNo.toString();
    appliedamtctrl.text = widget.loan.appliedLoanAmt.toString();
    appliedamtwordsctrl.text = widget.loan.appliedLoanAmtinWords.toString();
    loanreasonctrl.text = widget.loan.loanReason.toString();
    surityname1ctrl.text = widget.loan.surityName1.toString();
    surityname2ctrl.text = widget.loan.surityName2.toString();
    acno1ctrl.text = widget.loan.sAcNo1.toString();
    acno2ctrl.text = widget.loan.sAcNo2.toString();
    approvedonctrl.text =
        DateFormat('dd-mm-yyyy , HH:mm').format(widget.loan.approvedOn!);
    addressctrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.permanentAddress ??
        "";
    appLtinstallctrl.text = widget.loan.monthlyInstallmentAmt.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 15, bottom: 30),
        child: Column(
          children: [
            ltFormHeader(),
            const SizedBox(height: 15),
            CustomTextfield(
                enabled: enabled,
                text: 'Approved On',
                controller: approvedonctrl),
            CustomTextfield(
              text: 'Full Name',
              controller: fullnamectrl,
              enabled: false,
            ),

            CustomTextfield(
                enabled: false, text: 'Designation', controller: desigctrl),
            CustomTextfield(
                keyboardType: TextInputType.number,
                enabled: enabled,
                text: 'Account No.',
                controller: bacnoctrl),
            CustomTextfield(
                enabled: false, text: 'Full Address', controller: addressctrl),
            CustomTextfield(
                keyboardType: TextInputType.number,
                enabled: enabled,
                text: 'Loan Applied in Rs.',
                controller: appliedamtctrl),
            CustomTextfield(
                enabled: enabled,
                text: 'Loan Amount in Words',
                controller: appliedamtwordsctrl),
            CustomTextfield(
                enabled: enabled,
                text: 'Reason for Loan',
                controller: loanreasonctrl),
            // borrowerSignRow(context),
            const SizedBox(height: 15),
            const Divider(
              color: Colors.black,
              thickness: 2,
            ),
            // const SizedBox(height: 15),
            const SizedBox(height: 15),
            CustomTextfield(
                enabled: enabled,
                text: 'Shri/Smt. (BORROWER)',
                controller: fullnamectrl),
            const SizedBox(height: 15),
            //borrrowers sign
            Align(
              alignment: Alignment.centerRight,
              child: SizedBox(
                  height: 80,
                  width: 80,
                  child: Image.network(widget.loan.bSign ?? "")),
            ),
            const SizedBox(height: 15),
            const Text(" ( 1. ) "),
            const SizedBox(height: 15),
            CustomTextfield(
                enabled: enabled,
                text: "Name of the first surity person",
                controller: surityname1ctrl),
            CustomTextfield(
                keyboardType: TextInputType.number,
                enabled: enabled,
                text: "Account No. of the first surity person",
                controller: acno1ctrl),
            const SizedBox(height: 15),
            //surity 1 sign
            // Align(
            //   alignment: Alignment.centerRight,
            //   child: SizedBox(
            //       height: 80,
            //       width: 80,
            //       child: Image.network(widget.loan.suritySign1)),
            // ),
            // const SizedBox(height: 15),
            const Text(" ( 2. ) "),
            const SizedBox(height: 15),
            CustomTextfield(
                enabled: enabled,
                text: "Name of the second surity person",
                controller: surityname2ctrl),
            CustomTextfield(
                enabled: enabled,
                keyboardType: TextInputType.number,
                text: "Account No. of the second surity person",
                controller: acno2ctrl),

            // const SizedBox(height: 15),
            //surity 2 sign

            // Align(
            //   alignment: Alignment.centerRight,
            //   child: SizedBox(
            //       height: 80,
            //       width: 80,
            //       child: Image.network(widget.loan.suritySign2)),
            // ),
            // const SizedBox(height: 60),
            CustomTextfield(
                text: "Installment Amount",
                controller: appLtinstallctrl,
                enabled: enabled),
            const SizedBox(height: 60),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //Reject Button
                // onSubmitLoad
                //     ? const CircularProgressIndicator()
                //     : LtRejectButton(
                //         reasonctrl: reasonctrl,
                //         widget: widget,
                //         onPressed: () async {
                //           await FBFireStore.loan.doc(widget.loan.docId).update({
                //             'isNew': false,
                //             'rejectionDate': Timestamp.now(),
                //             'rejectionReason': reasonctrl.text,
                //           });
                //         },
                //       ),
                //SAVE Button
                enabled == true
                    ? onSubmitLoad
                        ? const CircularProgressIndicator()
                        : ElevatedButton(
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(
                                  EdgeInsetsDirectional.symmetric(
                                      horizontal: 100, vertical: 15)),
                              shape: WidgetStatePropertyAll(
                                  ContinuousRectangleBorder()),
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.green),
                              elevation: WidgetStatePropertyAll(0),
                            ),
                            onPressed: () {
                              context.pop();
                              onLtLoanSave(context);
                            },
                            child: Text(
                              "SAVE",
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  letterSpacing: 3,
                                  fontWeight: FontWeight.bold),
                            ),
                          )
                    : SizedBox(),

                //Edit Button
                onSubmitLoad
                    ? const CircularProgressIndicator()
                    : LtLoanEditButton(
                        onPressed: () {
                          setState(() {
                            enabled = true;
                          });
                        },
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> onLtLoanSave(BuildContext context) {
    final share = ((num.tryParse(appliedamtctrl.text) ?? 0) * 10) / 100;
    // print("share value : $share");
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text("Save Application?"),
        // content: const Text("Are you sure you want to SA Application?"),
        actions: [
          TextButton(
              onPressed: () async {
                await FBFireStore.loan.doc(widget.loan.docId).update({
                  'applicationNo': num.tryParse(appnoctrl.text),
                  'designation': desigctrl.text,
                  'bAcNo': num.tryParse(bacnoctrl.text),
                  'appliedLoanAmt': num.tryParse(appliedamtctrl.text),
                  'appliedLoanAmtinWords': appliedamtwordsctrl.text,
                  'loanReason': loanreasonctrl.text,
                  'surityName1': surityname1ctrl.text,
                  'surityName2': surityname2ctrl.text,
                  'sAcNo1': num.tryParse(acno1ctrl.text),
                  'sAcNo2': num.tryParse(acno2ctrl.text),
                  'totalLoanAmt': widget.loan.appliedLoanAmt,
                  'totalLoanPaid': 0,
                  'totalLoanDue': widget.loan.appliedLoanAmt,
                  'isSettled': false,
                  'isNew': false,
                  'processedOn': null, // Nullable
                  'share': share,
                  'totalInterestPaid': 0,
                  'balance': null,
                  'monthlyInstallmentAmt': num.tryParse(appLtinstallctrl.text)
                });
                context.pop();
              },
              child: const Text("Yes")),
          TextButton(
              onPressed: () async {
                if (context.mounted) {
                  context.pop();
                }
              },
              child: const Text("No")),
        ],
      ),
    );
  }

  Row ltFormHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // const Image(height: 50, image: AssetImage('assets/foodcorpimage2.png')),
        Row(
          children: [
            const Text("Application No."),
            const SizedBox(width: 3),
            SizedBox(
              width: 85,
              height: 28,
              child: TextFormField(
                enabled: enabled,
                keyboardType: TextInputType.number,
                controller: appnoctrl,
                decoration: const InputDecoration(border: OutlineInputBorder()),
              ),
            ),
          ],
        )
      ],
    );
  }

  Row surity2SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s2sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s2sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity2signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }
                          // print("Surity2 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity2signatureImage = null;
                          s2sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity2signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity2signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row surity1SignRow(BuildContext context) {
    return Row(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: ElevatedButton(
            style: const ButtonStyle(
              shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
              backgroundColor: WidgetStatePropertyAll(Colors.black),
              elevation: WidgetStatePropertyAll(0),
            ),
            onPressed: () => showDialog(
              context: context,
              builder: (context) => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Signature(
                    height: 400,
                    width: 300,
                    dynamicPressureSupported: true,
                    backgroundColor: Colors.white,
                    controller: s1sctrl,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          final image = await s1sctrl.toImage();
                          final byteData = await image?.toByteData(
                              format: ImageByteFormat.png);

                          if (byteData != null) {
                            setState(() {
                              surity1signatureImage =
                                  byteData.buffer.asUint8List();
                            });
                          }

                          // print("Surity1 Signature captured!");
                          Navigator.pop(context);
                        },
                        child: const Text("Submit"),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          surity1signatureImage = null;
                          s1sctrl.clear();
                        },
                        child: const Text("Clear Signature"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            child: const Text(
              "Add Signature",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),
        const SizedBox(width: 25),
        surity1signatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(surity1signatureImage!),
              )
            : const SizedBox()
      ],
    );
  }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        // print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text("Submit"),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text("Clear Signature"),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}
