// ignore_for_file: use_build_context_synchronously

import 'dart:typed_data';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:signature/signature.dart';
import '../../../models/loan_model.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../New_Loan_Application/buttons/ST/st_edit_button.dart';

class ApprovedStloanform extends StatefulWidget {
  const ApprovedStloanform(
      {super.key, required this.index, required this.loan});

  final int index;
  final Loan loan;

  @override
  State<ApprovedStloanform> createState() => _ApprovedStloanformState();
}

class _ApprovedStloanformState extends State<ApprovedStloanform> {
  TextEditingController appnoctrl = TextEditingController();
  TextEditingController fullnamectrl = TextEditingController();
  TextEditingController acnoctrl = TextEditingController();
  TextEditingController numamtctrl = TextEditingController();
  TextEditingController amtctrl = TextEditingController();
  TextEditingController surity1ctrl = TextEditingController();
  TextEditingController surity2ctrl = TextEditingController();
  TextEditingController balctrl = TextEditingController();
  TextEditingController appStinstallctrl = TextEditingController();
  TextEditingController approvedonctrl = TextEditingController();
  TextEditingController lrctrl = TextEditingController();

  Uint8List? borrowersignatureImage;

  SignatureController bsignctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity1signatureImage;

  SignatureController s1sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  Uint8List? surity2signatureImage;

  SignatureController s2sctrl = SignatureController(
    penStrokeWidth: 3,
    penColor: Colors.blue,
  );

  bool onSubmitLoad = false;

  // Future<String?> uploadSignimages(Uint8List file) async {
  //   try {
  //     final path = "Images/${DateTime.now().millisecondsSinceEpoch}.png";
  //     final imageRef = FBStorage.fbstore.ref().child(path);

  //     final task = await imageRef.putData(file);
  //     var downloadurl = await task.ref.getDownloadURL();

  //     return await task.ref.getDownloadURL();
  //   } catch (e) {}
  // }

  @override
  void initState() {
    super.initState();
    // print(widget.loan.sAcNo1);
    appnoctrl.text = widget.loan.applicationNo.toString();
    fullnamectrl.text = Get.find<HomeCtrl>()
            .users
            .firstWhereOrNull((element) => element.docId == widget.loan.uid)
            ?.name ??
        "";
    acnoctrl.text = widget.loan.bAcNo.toString();
    numamtctrl.text = widget.loan.appliedLoanAmt.toString();
    amtctrl.text = widget.loan.appliedLoanAmtinWords.toString();
    surity1ctrl.text = widget.loan.surityName1.toString();
    surity2ctrl.text = widget.loan.surityName2.toString();
    // balctrl.text = widget.loan.balance.toString();
    appStinstallctrl.text = widget.loan.monthlyInstallmentAmt.toString();
    approvedonctrl.text =
        DateFormat('dd-mm-yyyy , HH:mm').format(widget.loan.approvedOn!);
    lrctrl.text = widget.loan.loanReason.toString();
  }

  bool enabled = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(top: 15, bottom: 30),
        child: Column(
          children: [
            StFormHeader(
              appnoctrl: appnoctrl,
              enabled: enabled,
            ),
            CustomTextfield(
                enabled: enabled,
                text: 'Approved On',
                controller: approvedonctrl),
            CustomTextfield(
                keyboardType: TextInputType.number,
                enabled: enabled,
                text: 'Account No.',
                controller: acnoctrl),
            // CustomTextfield(
            //     keyboardType: TextInputType.number,
            //     enabled: enabled,
            //     text: 'Balance',
            //     controller: balctrl),
            CustomTextfield(
                enabled: enabled, text: 'Full Name', controller: fullnamectrl),
            CustomTextfield(
                keyboardType: TextInputType.number,
                enabled: enabled,
                text: 'Loan Applied in Rs.',
                controller: numamtctrl),
            CustomTextfield(
                enabled: enabled,
                text: 'Loan Amount in Words',
                controller: amtctrl),
            const SizedBox(height: 15),

            Align(
              alignment: Alignment.centerRight,
              child: SizedBox(
                  height: 80,
                  width: 80,
                  child: Image.network(widget.loan.bSign ?? "")),
            ),

            // borrowerSignRow(context),
            const SizedBox(height: 15),
            // const Text(" ( 1. ) "),
            CustomTextfield(
                enabled: enabled,
                text: "Name of the first surity person",
                controller: surity1ctrl),
            // surity1SignRow(context),
            // const SizedBox(height: 15),

            // Align(
            //   alignment: Alignment.centerRight,
            //   child: SizedBox(
            //       height: 80,
            //       width: 80,
            //       child: Image.network(widget.loan.suritySign1)),
            // ),
            const SizedBox(height: 15),
            // const Text(" ( 2. ) "),
            CustomTextfield(
                enabled: enabled,
                text: "Name of the second surity person",
                controller: surity2ctrl),
            // surity2SignRow(context),
            // const SizedBox(height: 15),

            // Align(
            //   alignment: Alignment.centerRight,
            //   child: SizedBox(
            //       height: 80,
            //       width: 80,
            //       child: Image.network(widget.loan.suritySign2)),
            // ),
            // const SizedBox(height: 60),
            CustomTextfield(
                text: "Installment Amount",
                controller: appStinstallctrl,
                enabled: enabled),
            CustomTextfield(
                text: "Loan Reason", controller: lrctrl, enabled: enabled),
            const SizedBox(height: 60),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                //Reject Button
                // onSubmitLoad
                //     ? const CircularProgressIndicator()
                //     : StRejectButton(
                //         reasonctrl: reasonctrl,
                //         widget: widget,
                //         onPressed: () async {
                //           await FBFireStore.loan.doc(widget.loan.docId).update({
                //             'isNew': false,
                //             'rejectionDate': Timestamp.now(),
                //             'rejectionReason': reasonctrl.text,
                //           });
                //         },
                //       ),
                //Apply Button
                enabled == true
                    ? onSubmitLoad
                        ? const CircularProgressIndicator()
                        : ElevatedButton(
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(
                                  EdgeInsetsDirectional.symmetric(
                                      horizontal: 100, vertical: 15)),
                              shape: WidgetStatePropertyAll(
                                  ContinuousRectangleBorder()),
                              backgroundColor:
                                  WidgetStatePropertyAll(Colors.green),
                              elevation: WidgetStatePropertyAll(0),
                            ),
                            onPressed: () {
                              context.pop();
                              onStLoanSave(context);
                            },
                            child: Text(
                              "SAVE",
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  letterSpacing: 3,
                                  fontWeight: FontWeight.bold),
                            ),
                          )
                    : SizedBox(),

                // await FBFireStore.loan.doc(widget.loan.docId).update({
                //   'totalLoanAmt': widget.loan.appliedLoanAmt,
                //   'totalLoanPaid': 0,
                //   'totalLoanDue': widget.loan.appliedLoanAmt,
                //   'isNew': false,
                //   'approvedOn': Timestamp.now(), // Nullable
                //   'processedOn': null, // Nullable
                //   'share': 0,
                //   'totalInterestPaid': 0,
                //   'monthlyInstallmentAmt': 4000
                // });

                //Edit Button
                onSubmitLoad
                    ? const CircularProgressIndicator()
                    : StEditButton(
                        onPressed: () {
                          setState(() {
                            enabled = true;
                          });
                        },
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> onStLoanSave(BuildContext context) {
    final share = ((num.tryParse(numamtctrl.text) ?? 0) * 10) / 100;
    // print("share value : $share");
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text("Save Application?"),
        // content: const Text("Are you sure you want to SA Application?"),
        actions: [
          TextButton(
              onPressed: () async {
                await FBFireStore.loan.doc(widget.loan.docId).update({
                  'applicationNo': num.tryParse(appnoctrl.text),
                  'bAcNo': num.tryParse(acnoctrl.text),
                  'appliedLoanAmt': num.tryParse(numamtctrl.text),
                  'appliedLoanAmtinWords': amtctrl.text,
                  'surityName1': surity1ctrl.text,
                  'surityName2': surity2ctrl.text,
                  // 'balance': num.tryParse(balctrl.text),
                  'totalLoanAmt': widget.loan.appliedLoanAmt,
                  'totalLoanPaid': 0,
                  'totalLoanDue': widget.loan.appliedLoanAmt,
                  // 'isSettled': false,
                  'isNew': false,
                  //   // 'approvedOn': Timestamp.now(), // Nullable
                  'processedOn': null, // Nullable
                  'share': share,
                  'totalInterestPaid': 0,
                  'monthlyInstallmentAmt': num.tryParse(appStinstallctrl.text),
                  'loanReason': lrctrl.text,

                  // widget.sets.defaultStinstallAmt,
                });
                context.pop();
              },
              child: const Text("Yes")),
          TextButton(
              onPressed: () async {
                if (context.mounted) {
                  context.pop();
                }
              },
              child: const Text("No")),
        ],
      ),
    );
  }

  // Row surity2SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s2sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       onPressed: () async {
  //                         final image = await s2sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity2signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }
  //                         print("Surity2 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text("Submit"),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       onPressed: () {
  //                         surity2signatureImage = null;
  //                         s2sctrl.clear();
  //                       },
  //                       child: const Text("Clear Signature"),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity2signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity2signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  // Row surity1SignRow(BuildContext context) {
  //   return Row(
  //     children: [
  //       Align(
  //         alignment: Alignment.centerLeft,
  //         child: ElevatedButton(
  //           style: const ButtonStyle(
  //             shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
  //             backgroundColor: WidgetStatePropertyAll(Colors.black),
  //             elevation: WidgetStatePropertyAll(0),
  //           ),
  //           onPressed: () => showDialog(
  //             context: context,
  //             builder: (context) => Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 Signature(
  //                   height: 400,
  //                   width: 300,
  //                   dynamicPressureSupported: true,
  //                   backgroundColor: Colors.white,
  //                   controller: s1sctrl,
  //                 ),
  //                 const SizedBox(height: 10),
  //                 Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     ElevatedButton(
  //                       onPressed: () async {
  //                         final image = await s1sctrl.toImage();
  //                         final byteData = await image?.toByteData(
  //                             format: ImageByteFormat.png);

  //                         if (byteData != null) {
  //                           setState(() {
  //                             surity1signatureImage =
  //                                 byteData.buffer.asUint8List();
  //                           });
  //                         }

  //                         print("Surity1 Signature captured!");
  //                         Navigator.pop(context);
  //                       },
  //                       child: const Text("Submit"),
  //                     ),
  //                     const SizedBox(width: 10),
  //                     ElevatedButton(
  //                       onPressed: () {
  //                         surity1signatureImage = null;
  //                         s1sctrl.clear();
  //                       },
  //                       child: const Text("Clear Signature"),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //           child: const Text(
  //             "Add Signature",
  //             style: TextStyle(color: Colors.white),
  //           ),
  //         ),
  //       ),
  //       const SizedBox(width: 25),
  //       surity1signatureImage != null
  //           ? SizedBox(
  //               height: 50,
  //               child: Image.memory(surity1signatureImage!),
  //             )
  //           : const SizedBox()
  //     ],
  //   );
  // }

  Row borrowerSignRow(BuildContext context) {
    return Row(
      children: [
        ElevatedButton(
          style: const ButtonStyle(
            shape: WidgetStatePropertyAll(ContinuousRectangleBorder()),
            backgroundColor: WidgetStatePropertyAll(Colors.black),
            elevation: WidgetStatePropertyAll(0),
          ),
          onPressed: () => showDialog(
            context: context,
            builder: (context) => Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Signature(
                  height: 400,
                  width: 300,
                  dynamicPressureSupported: true,
                  backgroundColor: Colors.white,
                  controller: bsignctrl,
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        final image = await bsignctrl.toImage();
                        final byteData = await image?.toByteData(
                            format: ImageByteFormat.png);

                        if (byteData != null) {
                          setState(() {
                            borrowersignatureImage =
                                byteData.buffer.asUint8List();
                          });
                        }

                        print("Signature captured!");
                        Navigator.pop(context);
                      },
                      child: const Text("Submit"),
                    ),
                    const SizedBox(width: 10),
                    ElevatedButton(
                      onPressed: () {
                        borrowersignatureImage = null;
                        bsignctrl.clear();
                      },
                      child: const Text("Clear Signature"),
                    ),
                  ],
                ),
              ],
            ),
          ),
          child: const Text(
            "Add Borrower's Signature",
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 25),
        borrowersignatureImage != null
            ? SizedBox(
                height: 50,
                child: Image.memory(borrowersignatureImage!),
              )
            : const SizedBox()
      ],
    );
  }
}

class StFormHeader extends StatelessWidget {
  const StFormHeader({
    super.key,
    required this.appnoctrl,
    required this.enabled,
  });
  final TextEditingController appnoctrl;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // const Image(height: 50, image: AssetImage('assets/foodcorpimage2.png')),
        Row(
          children: [
            const Text("Application No."),
            const SizedBox(width: 3),
            SizedBox(
              width: 85,
              height: 28,
              child: TextFormField(
                enabled: enabled,
                keyboardType: TextInputType.number,
                cursorHeight: 10,
                controller: appnoctrl,
                decoration: const InputDecoration(border: OutlineInputBorder()),
              ),
            ),
          ],
        )
      ],
    );
  }
}
