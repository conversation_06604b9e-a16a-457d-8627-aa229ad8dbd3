import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controller/homectrl.dart';

class LoanHdetailsPage extends StatefulWidget {
  const LoanHdetailsPage({super.key, this.loanId});
  final String? loanId;

  @override
  State<LoanHdetailsPage> createState() => _LoanHdetailsPageState();
}

class _LoanHdetailsPageState extends State<LoanHdetailsPage> {
  Loan? loanModelw;
  bool loanhfullLoader = true;
  bool hloading = false;
  bool loanhclosingloader = false;

  @override
  void initState() {
    super.initState();
    getData();
  }

  final ctrl = Get.find<HomeCtrl>();
  getData() async {
    if (widget.loanId != null) {
      final loanData = await FBFireStore.loan.doc(widget.loanId).get();
      loanModelw = Loan.fromSnap(loanData);
    }
    setState(() => loanhfullLoader = false);
  }

  loadData() async {
    loanModelw?.applicationNo.toString();
    loanModelw?.uid;
    loanModelw?.totalLoanAmt.toString();
    loanModelw?.appliedLoanAmt.toString();
    loanModelw?.totalLoanPaid.toString();
    loanModelw?.totalLoanDue.toString();
    loanModelw?.loanType;
    loanModelw?.isSettled;
    loanModelw?.settledOn;
    loanModelw?.settledOn.toString();
    loanModelw?.isNew;
    loanModelw?.appliedOn.toString();
    loanModelw?.applicationNo.toString();
    loanModelw?.approvedOn.toString();
    loanModelw?.processedOn.toString();
    loanModelw?.share.toString();
    loanModelw?.totalInterestPaid.toString();
    loanModelw?.designation;
    loanModelw?.bAcNo;
    loanModelw?.appliedLoanAmtinWords;
    loanModelw?.loanReason;
    loanModelw?.bSign;
    loanModelw?.surityName1;
    loanModelw?.surityName2;
    loanModelw?.sAcNo1.toString();
    loanModelw?.sAcNo2.toString();
    // loanModelw?.balance.toString();
    loanModelw?.rejectionDate;
    loanModelw?.rejectionReason;
    loanModelw?.monthlyInstallmentAmt;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          automaticallyImplyLeading: false,
          title: Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  loanModelw?.applicationNo.toString() ?? "",
                  style: TextStyle(color: Colors.black),
                ),
              ],
            ),
          )),
      body: loanhfullLoader
          ? Center(
              child: SizedBox(
                  height: 30, width: 30, child: CircularProgressIndicator()))
          : Padding(
              padding: const EdgeInsets.all(25),
              child: StaggeredGrid.extent(
                maxCrossAxisExtent: 377,
                mainAxisSpacing: 3,
                crossAxisSpacing: 10,
                children: [
                  UserCustomTextField(
                      initialvalue: loanModelw?.applicationNo.toString() ?? "-",
                      enabled: false,
                      labeltext: 'Application No.'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.approvedOn != null
                          ? DateFormat('dd-MM-yyyy')
                              .format(loanModelw!.approvedOn!)
                          : '',
                      enabled: false,
                      labeltext: 'Approved On'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.settledOn != null
                          ? DateFormat('dd-MM-yyyy')
                              .format(loanModelw!.settledOn!)
                          : '',
                      enabled: false,
                      labeltext: 'Settled On.'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.designation ?? "",
                      enabled: false,
                      labeltext: 'Designation'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.loanReason ?? "",
                      enabled: false,
                      labeltext: 'Loan Reason'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.appliedOn.toString() ?? "",
                      enabled: false,
                      labeltext: 'Applied On.'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.loanType ?? "",
                      enabled: false,
                      labeltext: 'Loan Type'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.surityName1 ?? "",
                      enabled: false,
                      labeltext: 'Surity Name of the First Person'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.surityName2 ?? "",
                      enabled: false,
                      labeltext: 'Surity Name of the Second Person'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.appliedLoanAmt.toString() ?? "",
                      enabled: false,
                      labeltext: 'Applied Loan Amount'),
                  UserCustomTextField(
                      initialvalue:
                          loanModelw?.appliedLoanAmtinWords.toString() ?? "",
                      enabled: false,
                      labeltext: 'Applied Loan Amount in words'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.bAcNo.toString() ?? "",
                      enabled: false,
                      labeltext: 'Bank Account No.'),
                  UserCustomTextField(
                      initialvalue:
                          loanModelw?.monthlyInstallmentAmt.toString() ?? "",
                      enabled: false,
                      labeltext: 'Monthly Installment Amount'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.share.toString() ?? "",
                      enabled: false,
                      labeltext: 'Shares'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.totalLoanAmt.toString() ?? "",
                      enabled: false,
                      labeltext: 'Total Loan Amount'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.totalLoanDue.toString() ?? "",
                      enabled: false,
                      labeltext: 'Total Loan Due'),
                  UserCustomTextField(
                      initialvalue: loanModelw?.totalLoanPaid.toString() ?? "",
                      enabled: false,
                      labeltext: 'Total Loan Paid'),
                  UserCustomTextField(
                      initialvalue:
                          loanModelw?.totalInterestPaid.toString() ?? "",
                      enabled: false,
                      labeltext: 'Total Interest Paid'),
                ],
              ),
            ),
    );
  }
}

 // Spacer(),
            // ElevatedButton.icon(
            //   style: ElevatedButton.styleFrom(
            //     backgroundColor: Colors.black87,
            //     elevation: 0,
            //     foregroundColor: Colors.white,
            //     shape: RoundedRectangleBorder(
            //         borderRadius: BorderRadius.circular(4)),
            //   ),
            //   onPressed: () => showDialog(
            //     context: context,
            //     builder: (context) => AlertDialog(
            //       backgroundColor: Colors.white,
            //       title: const Text("Loan Pre-Closure"),
            //       content:
            //           const Text("Are you sure you want to close the loan?"),
            //       actions: loanhclosingloader
            //           ? [
            //               Center(
            //                 child: const CircularProgressIndicator(),
            //               )
            //             ]
            //           : [
            //               TextButton(
            //                 onPressed: () async {
            //                   setState(() => loanhclosingloader = true);

            //                   final sfDocRef =
            //                       FBFireStore.loan.doc(widget.loanId);
            //                   final ufDocRef =
            //                       FBFireStore.users.doc(loanModelw?.uid);

            //                   print("sfDocRef : $sfDocRef");
            //                   print("ufDocRef : $ufDocRef");

            //                   try {
            //                     await FBFireStore.fb
            //                         .runTransaction((transaction) async {
            //                       final loanSnapshot =
            //                           await transaction.get(sfDocRef);
            //                       final userSnapshot =
            //                           await transaction.get(ufDocRef);

            //                       if (!loanSnapshot.exists ||
            //                           !userSnapshot.exists) {
            //                         throw Exception(
            //                             "Loan or User document not found");
            //                       }

            //                       final loanData = loanSnapshot.data()!;
            //                       final userData = userSnapshot.data()!;

            //                       final double totalloanDue =
            //                           (loanData["totalLoanDue"] ?? 0)
            //                               .toDouble();
            //                       final double totalloanPaid =
            //                           (loanData["totalLoanPaid"] ?? 0)
            //                               .toDouble();
            //                       final double totalinterestPaid =
            //                           (loanData["totalInterestPaid"] ?? 0)
            //                               .toDouble();

            //                       final bool isLongTerm =
            //                           loanData["loanType"] == "Long Term Loan";

            //                       transaction.update(sfDocRef, {
            //                         "isSettled": true,
            //                         "settledOn": DateTime.now(),
            //                         "totalLoanDue": 0,
            //                         "balance": 0,
            //                       });

            //                       if (isLongTerm) {
            //                         print("ltloan");
            //                         transaction.update(ufDocRef, {
            //                           "ltLoansDue":
            //                               ((userData["ltLoansDue"] ?? 0) -
            //                                       totalloanDue)
            //                                   .clamp(0, double.infinity),
            //                           "totalLtLoans":
            //                               ((userData["totalLtLoans"] ?? 0) -
            //                                       totalloanDue)
            //                                   .clamp(0, double.infinity),
            //                           "totalLtIntPaid":
            //                               ((userData["totalLtIntPaid"] ?? 0) -
            //                                       totalinterestPaid)
            //                                   .clamp(0, double.infinity),
            //                         });
            //                         print("ltloan");
            //                         print("ltloan");
            //                         print("ltloan");
            //                       } else {
            //                         print("stloan");
            //                         transaction.update(ufDocRef, {
            //                           "stLoansDue":
            //                               ((userData["stLoansDue"] ?? 0) -
            //                                       totalloanDue)
            //                                   .clamp(0, double.infinity),
            //                           "totalStLoans":
            //                               ((userData["totalStLoans"] ?? 0) -
            //                                       totalloanPaid)
            //                                   .clamp(0, double.infinity),
            //                           "totalStIntPaid":
            //                               ((userData["totalStIntPaid"] ?? 0) -
            //                                       totalinterestPaid)
            //                                   .clamp(0, double.infinity),
            //                         });
            //                       }
            //                     });

            //                     if (context.mounted) {
            //                       context.pop();
            //                       ScaffoldMessenger.of(context).showSnackBar(
            //                         const SnackBar(
            //                             content:
            //                                 Text("Loan successfully closed.")),
            //                       );
            //                     }
            //                   } catch (e) {
            //                     ScaffoldMessenger.of(context).showSnackBar(
            //                       SnackBar(
            //                           content: Text(
            //                               "Something went wrong. Please try again.")),
            //                     );
            //                     print("Error during loan pre-closure: $e");
            //                   } finally {
            //                     setState(() => loanhclosingloader = false);
            //                   }
            //                 },
            //                 child: const Text("Yes"),
            //               ),
            //               TextButton(
            //                 onPressed: () async {
            //                   if (context.mounted) {
            //                     context.pop();
            //                   }
            //                 },
            //                 child: const Text("No"),
            //               ),
            //             ],
            //     ),
            //   ),
            //   label: Padding(
            //     padding: const EdgeInsets.symmetric(vertical: 8.0),
            //     child: Text("Loan Pre-Closure",
            //         style: const TextStyle(fontSize: 14)),
            //   ),
            // )
