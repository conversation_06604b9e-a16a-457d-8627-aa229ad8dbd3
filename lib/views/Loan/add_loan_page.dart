// ignore_for_file: use_build_context_synchronously

import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class AddLoanPage extends StatefulWidget {
  const AddLoanPage({super.key});

  @override
  State<AddLoanPage> createState() => _AddLoanPageState();
}

class _AddLoanPageState extends State<AddLoanPage> {
  final TextEditingController appliedLoanAmtCtrl = TextEditingController();
  final TextEditingController totalLoanAmtCtrl = TextEditingController();
  final TextEditingController totalLoanPaidAmtCtrl = TextEditingController();
  final TextEditingController totalLoanDueAmtCtrl = TextEditingController();
  final TextEditingController appliedLoanAmtInWordsCtrl =
      TextEditingController();
  final TextEditingController designationCtrl = TextEditingController();
  final TextEditingController bankAccountCtrl = TextEditingController();
  final TextEditingController loanReasonCtrl = TextEditingController();
  final TextEditingController surityName1Ctrl = TextEditingController();
  final TextEditingController surityName2Ctrl = TextEditingController();
  final TextEditingController surityAcNo1Ctrl = TextEditingController();
  final TextEditingController surityAcNo2Ctrl = TextEditingController();
  final TextEditingController monthlyInstallmentCtrl = TextEditingController();
  final TextEditingController shareCtrl = TextEditingController();
  final TextEditingController applicationNoCtrl = TextEditingController();
  final TextEditingController totalIntPaidCtrl = TextEditingController();

  String _loanType = LoanTypes.longTerm;
  final List<String> _loanTypes = [LoanTypes.longTerm, LoanTypes.emergencyLoan];

  String? _selectedUserId;
  List<UserModel> _users = [];

  DateTime? appliedOnDate;
  DateTime? approvedOnDate;

  bool isLoading = false;

  String? userSignUploadedUrl;
  bool isUploadingBSign = false;

  String? userDocUrl;
  bool userDocUploading = false;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    applicationNoCtrl.text =
        (Get.find<HomeCtrl>().settings!.applicationNo + 1).toString();
  }

  @override
  void dispose() {
    appliedLoanAmtCtrl.dispose();
    appliedLoanAmtInWordsCtrl.dispose();
    designationCtrl.dispose();
    bankAccountCtrl.dispose();
    loanReasonCtrl.dispose();
    surityName1Ctrl.dispose();
    surityName2Ctrl.dispose();
    surityAcNo1Ctrl.dispose();
    surityAcNo2Ctrl.dispose();
    monthlyInstallmentCtrl.dispose();
    shareCtrl.dispose();
    // applicationNoCtrl.dispose();
    totalIntPaidCtrl.dispose();
    totalLoanAmtCtrl.dispose();
    totalLoanPaidAmtCtrl.dispose();
    totalLoanDueAmtCtrl.dispose();
    super.dispose();
  }

  void _loadUsers() async {
    try {
      FBFireStore.users
          .where("approved", isEqualTo: true)
          .snapshots()
          .listen((event) {
        setState(() {
          _users = event.docs.map((e) => UserModel.fromSnap(e)).toList();
        });
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Uint8List? pdfBytes;
  String? pdfFileName;
  UploadTask? uploadTask;

  Future<void> pickFiles({required bool isSignature}) async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowMultiple: false,
        allowedExtensions: ['pdf'],
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Check if file exists and is within size limit (3MB)
        if (file.bytes != null && file.size <= 3 * 1024 * 1024) {
          setState(() {
            pdfBytes = file.bytes;
            pdfFileName =
                '${DateTime.now().millisecondsSinceEpoch}_${file.name}';
          });

          // Upload file immediately after picking
          if (pdfBytes != null) {
            final url = await uploadFile(isSignature: isSignature);
            if (url.isNotEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('File uploaded successfully')),
              );
            }
          }
        } else {
          throw Exception('File is too large. Maximum size is 3MB.');
        }
      }
    } catch (e) {
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text(e.toString()),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  Future<String> uploadFile({required bool isSignature}) async {
    String docUrl = "";

    try {
      if (pdfBytes == null || pdfFileName == null) {
        throw Exception('No file selected');
      }

      setState(() {
        if (isSignature) {
          isUploadingBSign = true;
        } else {
          userDocUploading = true;
        }
      });

      final ref =
          FirebaseStorage.instance.ref().child('documents/$pdfFileName');
      uploadTask = ref.putData(
        pdfBytes!,
        SettableMetadata(contentType: 'application/pdf'),
      );

      // Listen to upload progress
      uploadTask!.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        debugPrint('Upload progress: ${(progress * 100).toStringAsFixed(2)}%');
      });

      final snapshot = await uploadTask!.whenComplete(() {});
      final urlDownload = await snapshot.ref.getDownloadURL();

      setState(() {
        if (isSignature) {
          userSignUploadedUrl = urlDownload;
          isUploadingBSign = false;
        } else {
          userDocUrl = urlDownload;
          userDocUploading = false;
        }
        pdfBytes = null;
        pdfFileName = null;
        uploadTask = null;
      });

      docUrl = urlDownload;
    } catch (e) {
      setState(() {
        if (isSignature) {
          isUploadingBSign = false;
        } else {
          userDocUploading = false;
        }
      });

      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Upload Error'),
          content: Text(e.toString()),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }

    return docUrl;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      // print("selectedUserId : $_selectedUserId");
      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                isLoading
                    ? const CircularProgressIndicator()
                    : CustomHeaderButton(
                        onPressed: () => loanOnPressed(ctrl),
                        buttonName: 'Submit Loan'),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              "LOAN DETAILS",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                // Loan Type Dropdown
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Loan Type',
                    border: OutlineInputBorder(),
                  ),
                  value: _loanType,
                  items: _loanTypes.map((type) {
                    return DropdownMenuItem(value: type, child: Text(type));
                  }).toList(),
                  onChanged: (value) => setState(() => _loanType = value!),
                  validator: (value) => value == null ? 'Required' : null,
                ),
                DropdownButtonFormField<String>(
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Select User',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedUserId,
                  items: _users.map((user) {
                    return DropdownMenuItem(
                      value: user.docId,
                      child: Text(user.name),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedUserId = value),
                  validator: (value) => value == null ? 'Required' : null,
                ),
              ],
            ),
            const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text("Applied On Date"),
                    InkWell(
                      onTap: () async {
                        appliedOnDate = await showDatePicker(
                                context: context,
                                initialDate: appliedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1)) ??
                            DateTime.now();
                        setState(() {});
                      },
                      child: TextFormField(
                        style: TextStyle(color: Colors.black),
                        enabled: false,
                        controller: TextEditingController(
                            text: appliedOnDate
                                    ?.toLocal()
                                    .toString()
                                    .split(' ')[0] ??
                                'Select Date'),
                        decoration: InputDecoration(
                          filled: true,
                          prefixIcon: const Icon(
                            Icons.date_range,
                            size: 20,
                            color: Colors.black,
                          ),
                          border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadius.circular(6)),
                        ),
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text("Approved On Date"),
                    InkWell(
                      onTap: () async {
                        approvedOnDate = await showDatePicker(
                                context: context,
                                initialDate: approvedOnDate ?? DateTime.now(),
                                firstDate: DateTime(DateTime.now().year - 1),
                                lastDate: DateTime(DateTime.now().year + 1)) ??
                            DateTime.now();
                        setState(() {});
                      },
                      child: TextFormField(
                        style: TextStyle(color: Colors.black),
                        enabled: false,
                        controller: TextEditingController(
                            text: approvedOnDate
                                    ?.toLocal()
                                    .toString()
                                    .split(' ')[0] ??
                                'Select Date'),
                        decoration: InputDecoration(
                          filled: true,
                          prefixIcon: const Icon(
                            Icons.date_range,
                            size: 20,
                            color: Colors.black,
                          ),
                          border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadius.circular(6)),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                _loanType == LoanTypes.longTerm
                    ? CustomTextfield(
                        controller: designationCtrl,
                        text: 'Designation',
                        enabled: true,
                      )
                    : SizedBox.shrink(),
                CustomTextfield(
                  controller: bankAccountCtrl,
                  text: 'Bank Account Number',
                  enabled: true,
                ),
              ],
            ),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                CustomTextfield(
                  controller: applicationNoCtrl,
                  text: 'Application Number',
                  enabled: false,
                ),
                CustomTextfield(
                  controller: appliedLoanAmtCtrl,
                  text: 'Applied Loan Amount',
                  enabled: true,
                  keyboardType: TextInputType.number,
                ),
                CustomTextfield(
                  controller: totalLoanAmtCtrl,
                  text: 'Total Loan Amount',
                  enabled: true,
                  keyboardType: TextInputType.number,
                ),
                CustomTextfield(
                  controller: totalLoanPaidAmtCtrl,
                  text: 'Total Loan Paid Amount',
                  enabled: true,
                  keyboardType: TextInputType.number,
                ),
                CustomTextfield(
                  controller: totalLoanDueAmtCtrl,
                  text: 'Total Loan Due Amount',
                  enabled: true,
                  keyboardType: TextInputType.number,
                ),
                CustomTextfield(
                  controller: totalIntPaidCtrl,
                  text: 'Total Int Paid Amount',
                  enabled: true,
                  keyboardType: TextInputType.number,
                ),
                CustomTextfield(
                  controller: appliedLoanAmtInWordsCtrl,
                  text: 'Loan Amount in Words',
                  enabled: true,
                ),
                CustomTextfield(
                  controller: monthlyInstallmentCtrl,
                  text: 'Monthly Installment Amount',
                  enabled: true,
                ),
                CustomTextfield(
                  controller: loanReasonCtrl,
                  text: 'Loan Reason',
                  enabled: true,
                ),
                CustomTextfield(
                  controller: shareCtrl,
                  text: 'Share Amount',
                  enabled: true,
                ),
              ],
            ),
            // StaggeredGrid.count(
            //   crossAxisCount: 3,
            //   crossAxisSpacing: 50,
            //   children: [],
            // ),
            // const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                userSignUploadedUrl == null
                    ? isUploadingBSign
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.all(10),
                                  fixedSize: Size(
                                      MediaQuery.sizeOf(context).width, 40),
                                  // fixedSize: Size.fromWidth(
                                  //     MediaQuery.sizeOf(context).width,),
                                  backgroundColor: Colors.green.shade300,
                                  elevation: 0,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      // side: const BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.circular(4))),
                              onPressed: () async {
                                await pickFiles(isSignature: true);
                                if (pdfBytes != null) {
                                  final url =
                                      await uploadFile(isSignature: true);
                                  if (url.isNotEmpty) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text(
                                              'File uploaded successfully')),
                                    );
                                  }
                                }
                              },
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD BORROWER'S SIGNATURE PDF",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 15,
                                ),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'Sign Uploaded: $userSignUploadedUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            onPressed: () async {
                              if (userSignUploadedUrl != null) {
                                final uri = Uri.parse(userSignUploadedUrl!);
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri,
                                      mode: LaunchMode.externalApplication);
                                } else {}
                              }
                            },
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                userSignUploadedUrl = null;
                                pdfBytes = null;
                                pdfFileName = null;
                                uploadTask = null;
                              });
                            },
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
                userDocUrl == null
                    ? userDocUploading
                        ? const Padding(
                            padding: EdgeInsets.only(top: 12),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(top: 12),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.all(10),
                                  fixedSize: Size(
                                      MediaQuery.sizeOf(context).width, 40),
                                  // fixedSize: Size.fromWidth(
                                  //     MediaQuery.sizeOf(context).width,),
                                  backgroundColor: Colors.green.shade300,
                                  elevation: 0,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      // side: const BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.circular(4))),
                              onPressed: () async {
                                await pickFiles(isSignature: false);
                                if (pdfBytes != null) {
                                  final url =
                                      await uploadFile(isSignature: false);
                                  if (url.isNotEmpty) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text(
                                              'File uploaded successfully')),
                                    );
                                  }
                                }
                              },
                              child: const Text(
                                textAlign: TextAlign.center,
                                "UPLOAD DOCUMENTS PDF",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 15,
                                ),
                              ),
                            ),
                          )
                    : Row(
                        children: [
                          Expanded(
                            child: Text(
                              maxLines: 3,
                              'Doc Uploaded: $userDocUrl',
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(
                                  color: Colors.green, fontSize: 15),
                            ),
                          ),
                          IconButton(
                            icon:
                                const Icon(Icons.download, color: Colors.blue),
                            onPressed: () async {
                              if (userDocUrl != null) {
                                final uri = Uri.parse(userDocUrl!);
                                if (await canLaunchUrl(uri)) {
                                  await launchUrl(uri,
                                      mode: LaunchMode.externalApplication);
                                } else {}
                              }
                            },
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                userDocUrl = null;
                                pdfBytes = null;
                                pdfFileName = null;
                                uploadTask = null;
                              });
                            },
                            icon: const Icon(Icons.close, color: Colors.red),
                            tooltip: 'Remove document',
                          ),
                        ],
                      ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              "SURETY DETAILS",
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                CustomTextfield(
                  controller: surityName1Ctrl,
                  text: 'First Surety Name',
                  enabled: true,
                ),
                _loanType == LoanTypes.longTerm
                    ? CustomTextfield(
                        controller: surityAcNo1Ctrl,
                        text: 'First Surety Account Number',
                        enabled: true,
                        keyboardType: TextInputType.number,
                      )
                    : SizedBox.shrink()
              ],
            ),
            // const SizedBox(height: 20),
            StaggeredGrid.count(
              crossAxisCount: 3,
              crossAxisSpacing: 50,
              children: [
                CustomTextfield(
                  controller: surityName2Ctrl,
                  text: 'Second Surety Name',
                  enabled: true,
                ),
                _loanType == LoanTypes.longTerm
                    ? CustomTextfield(
                        controller: surityAcNo2Ctrl,
                        text: 'Second Surety Account Number',
                        keyboardType: TextInputType.number,
                        enabled: true,
                      )
                    : SizedBox.shrink()
              ],
            ),
          ],
        ),
      );
    });
  }

  Future<void> loanOnPressed(HomeCtrl ctrl) async {
    if (_loanType == LoanTypes.longTerm) {
      if (designationCtrl.text.isEmpty ||
          bankAccountCtrl.text.isEmpty ||
          loanReasonCtrl.text.isEmpty ||
          monthlyInstallmentCtrl.text.isEmpty ||
          shareCtrl.text.isEmpty ||
          // applicationNoCtrl.text.isEmpty ||
          totalLoanAmtCtrl.text.isEmpty ||
          totalLoanPaidAmtCtrl.text.isEmpty ||
          totalIntPaidCtrl.text.isEmpty ||
          totalLoanDueAmtCtrl.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter all details')),
        );
        setState(() => isLoading = false);
        return;
      }
    } else {
      if (
          // designationCtrl.text.isEmpty ||
          bankAccountCtrl.text.isEmpty ||
              loanReasonCtrl.text.isEmpty ||
              monthlyInstallmentCtrl.text.isEmpty ||
              shareCtrl.text.isEmpty ||
              // applicationNoCtrl.text.isEmpty ||
              totalLoanAmtCtrl.text.isEmpty ||
              totalLoanPaidAmtCtrl.text.isEmpty ||
              totalIntPaidCtrl.text.isEmpty ||
              totalLoanDueAmtCtrl.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter all details')),
        );
        setState(() => isLoading = false);
        return;
      }
    }

    if (userSignUploadedUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please upload borrower\'s signature PDF')),
      );
      setState(() => isLoading = false);
      return;
    }
    if (userDocUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please upload documents PDF')),
      );
      setState(() => isLoading = false);
      return;
    }

    final appliedAmt = num.tryParse(appliedLoanAmtCtrl.text.trim()) ?? 0;

    if (_loanType == LoanTypes.longTerm) {
      final maxAmt =
          num.tryParse(ctrl.settings?.maxLtLoanAmt.toString() ?? "0") ?? 0;
      if (appliedAmt > maxAmt) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Applied amount exceeds maximum long term loan limit'),
            duration: Duration(milliseconds: 2000),
          ),
        );
        setState(() => isLoading = false);
        return;
      }
    } else {
      final maxAmt =
          num.tryParse(ctrl.settings?.maxStLoanAmt.toString() ?? "0") ?? 0;
      if (appliedAmt > maxAmt) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Applied amount exceeds maximum emergency loan limit'),
            duration: Duration(milliseconds: 2000),
          ),
        );
        setState(() => isLoading = false);
        return;
      }
    }

    if (_selectedUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a user')),
      );
      setState(() => isLoading = false);
      return;
    }

    final existingLoanQuery = await FBFireStore.loan
        .where('uid', isEqualTo: _selectedUserId)
        .where('loanType', isEqualTo: _loanType)
        .where('isSettled', isEqualTo: false)
        .get();

    if (existingLoanQuery.docs.isNotEmpty) {
      showCtcAppSnackBar(context,
          'User already has an ongoing ${_loanType == LoanTypes.longTerm ? "long term" : "emergency"} loan');
      setState(() => isLoading = false);
      return;
    }

    if (appliedLoanAmtCtrl.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter applied loan amount')),
      );
      setState(() => isLoading = false);
      return;
    }
    if (appliedOnDate == null || approvedOnDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select applied and approved dates')),
      );
      setState(() => isLoading = false);
      return;
    }

    if (_loanType == LoanTypes.longTerm) {
      if (surityName1Ctrl.text.isEmpty ||
          surityName2Ctrl.text.isEmpty ||
          surityAcNo1Ctrl.text.isEmpty ||
          surityAcNo2Ctrl.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter all surety details')),
        );
        setState(() => isLoading = false);
        return;
      }
    } else {
      if (surityName1Ctrl.text.isEmpty || surityName2Ctrl.text.isEmpty
          // surityAcNo1Ctrl.text.isEmpty ||
          // surityAcNo2Ctrl.text.isEmpty
          ) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter all surety details')),
        );
        setState(() => isLoading = false);
        return;
      }
    }

    try {
      setState(() {
        isLoading = true;
      });

      final userDataQuery = await FBFireStore.users.doc(_selectedUserId).get();

      final batch = FBFireStore.fb.batch();

      final loanDocRef = FBFireStore.loan.doc();

      if (_loanType == LoanTypes.longTerm) {
        final loanId = batch.set(loanDocRef, {
          'uid': _selectedUserId,
          'createdAt': appliedOnDate,
          'totalLoanAmt': num.tryParse(totalLoanAmtCtrl.text) ?? 0,
          'appliedLoanAmt': num.tryParse(appliedLoanAmtCtrl.text) ?? 0,
          'totalLoanPaid': num.tryParse(totalLoanPaidAmtCtrl.text) ?? 0,
          'totalLoanDue': num.tryParse(totalLoanDueAmtCtrl.text) ?? 0,
          'loanType': _loanType.toString(),
          'isSettled': false,
          'settledOn': null, // Nullable
          'isNew': false,
          'appliedOn': appliedOnDate,
          'applicationNo': Get.find<HomeCtrl>().settings!.applicationNo + 1,
          'approvedOn': approvedOnDate, // Nullable
          'processedOn': null, // Nullable
          'share': num.tryParse(shareCtrl.text) ?? 0,
          'totalInterestPaid': num.tryParse(totalIntPaidCtrl.text) ?? 0,
          'designation': designationCtrl.text.toString(),
          'bAcNo': num.tryParse(bankAccountCtrl.text) ?? 0,
          'appliedLoanAmtinWords': appliedLoanAmtInWordsCtrl.text.toString(),
          'loanReason': loanReasonCtrl.text.toString(),
          'bSign': userSignUploadedUrl,
          'surityName1': surityName1Ctrl.text.toString(),
          'surityName2': surityName2Ctrl.text.toString(),
          'sAcNo1': num.tryParse(surityAcNo1Ctrl.text) ?? 0,
          'sAcNo2': num.tryParse(surityAcNo2Ctrl.text) ?? 0,
          // 'balance': null,
          'rejectionDate': null,
          'rejectionReason': null,
          'monthlyInstallmentAmt':
              num.tryParse(monthlyInstallmentCtrl.text) ?? 0,
          'loanPreClosureReason': null,
          'pdfString': userDocUrl,
        });

        await FBFireStore.settings
            .update({'applicationNo': FieldValue.increment(1)});

        batch.update(FBFireStore.users.doc(_selectedUserId), {
          'ltLoansDue': userDataQuery.data()?['ltLoansDue'] +
              (num.tryParse(totalLoanDueAmtCtrl.text) ?? 0),
          'totalLtLoans': userDataQuery.data()?['totalLtLoans'] +
              (num.tryParse(totalLoanAmtCtrl.text) ?? 0),
          'totalLtIntPaid': userDataQuery.data()?['totalLtIntPaid'] +
              (num.tryParse(totalIntPaidCtrl.text) ?? 0),
          'totalShares': userDataQuery.data()?['totalShares'] +
              (num.tryParse(shareCtrl.text) ?? 0),
        });

        batch.set(FBFireStore.transactions.doc(), {
          "uId": _selectedUserId,
          "createdAt": Timestamp.fromDate(DateTime.now()),
          "title": "Loan Approved",
          "amount": num.tryParse(appliedLoanAmtCtrl.text) ?? 0,
          "inn": true,
          "userMonthlyId": null,
          "recoveryId": null,
          "loanId": loanDocRef.id,
        });

        batch.set(FBFireStore.transactions.doc(), {
          "uId": _selectedUserId,
          "createdAt":
              Timestamp.fromDate(DateTime.now().add(Duration(seconds: 10))),
          "title": "Share Deduction 10%",
          "amount": num.tryParse(shareCtrl.text) ?? 0,
          "inn": false,
          "userMonthlyId": null,
          "recoveryId": null,
          "loanId": loanDocRef.id,
        });

        final userDoc = await FBFireStore.users.doc(_selectedUserId).get();

        batch.set(FBFireStore.notifications.doc(), {
          'uId': _selectedUserId,
          'title': "Loan Approved",
          'desc':
              "Loan approved of ${num.tryParse(appliedLoanAmtCtrl.text) ?? 0}",
          'type': "loanAccepted",
          'districtOffice': userDoc.data()?['districtoffice'],
          'createdAt': Timestamp.fromDate(DateTime.now()),
        });

        batch.set(FBFireStore.notifications.doc(), {
          'uId': _selectedUserId,
          'title': "Share Deduction 10%",
          'desc': "Share Deduction 10% : ${num.tryParse(shareCtrl.text) ?? 0}",
          'type': "shareDeduction",
          'districtOffice': userDoc.data()?['districtoffice'],
          'createdAt':
              Timestamp.fromDate(DateTime.now().add(Duration(seconds: 10))),
        });
      } else {
        batch.set(loanDocRef, {
          'uid': _selectedUserId,
          'createdAt': appliedOnDate,
          'totalLoanAmt': num.tryParse(totalLoanAmtCtrl.text) ?? 0,
          'appliedLoanAmt': num.tryParse(appliedLoanAmtCtrl.text) ?? 0,
          'totalLoanPaid': num.tryParse(totalLoanPaidAmtCtrl.text) ?? 0,
          'totalLoanDue': num.tryParse(totalLoanDueAmtCtrl.text) ?? 0,
          'loanType': _loanType.toString(),
          'isSettled': false,
          'settledOn': null, // Nullable
          'isNew': false,
          'appliedOn': appliedOnDate,
          'applicationNo': Get.find<HomeCtrl>().settings!.applicationNo + 1,
          'approvedOn': approvedOnDate, // Nullable
          'processedOn': null, // Nullable
          'share': num.tryParse(shareCtrl.text) ?? 0,
          'totalInterestPaid': num.tryParse(totalIntPaidCtrl.text) ?? 0,
          'designation': null,
          'bAcNo': num.tryParse(bankAccountCtrl.text) ?? 0,
          'appliedLoanAmtinWords': appliedLoanAmtInWordsCtrl.text.toString(),
          'loanReason': loanReasonCtrl.text.toString(),
          'bSign': userSignUploadedUrl,
          'surityName1': surityName1Ctrl.text,
          'surityName2': surityName2Ctrl.text,
          'sAcNo1': null,
          'sAcNo2': null,
          'rejectionDate': null,
          'rejectionReason': null,
          'monthlyInstallmentAmt':
              num.tryParse(monthlyInstallmentCtrl.text) ?? 0,
          'loanPreClosureReason': null,
          'pdfString': userDocUrl,
        });

        await FBFireStore.settings
            .update({'applicationNo': FieldValue.increment(1)});

        batch.update(FBFireStore.users.doc(_selectedUserId), {
          'stLoansDue': userDataQuery.data()?['stLoansDue'] +
              (num.tryParse(totalLoanDueAmtCtrl.text) ?? 0),
          'totalStLoans': userDataQuery.data()?['totalStLoans'] +
              (num.tryParse(totalLoanAmtCtrl.text) ?? 0),
          'totalStIntPaid': userDataQuery.data()?['totalStIntPaid'] +
              (num.tryParse(totalIntPaidCtrl.text) ?? 0),
          'totalShares': userDataQuery.data()?['totalShares'] +
              (num.tryParse(shareCtrl.text) ?? 0),
        });

        batch.set(FBFireStore.transactions.doc(), {
          "uId": _selectedUserId,
          "createdAt": Timestamp.fromDate(DateTime.now()),
          "title": "Loan Approved",
          "amount": num.tryParse(appliedLoanAmtCtrl.text) ?? 0,
          "inn": true,
          "userMonthlyId": null,
          "recoveryId": null,
          "loanId": loanDocRef.id,
        });

        batch.set(FBFireStore.transactions.doc(), {
          "uId": _selectedUserId,
          "createdAt":
              Timestamp.fromDate(DateTime.now().add(Duration(seconds: 10))),
          "title": "Share Deduction 10%",
          "amount": num.tryParse(shareCtrl.text) ?? 0,
          "inn": false,
          "userMonthlyId": null,
          "recoveryId": null,
          "loanId": loanDocRef.id,
        });

        final userDoc = await FBFireStore.users.doc(_selectedUserId).get();

        batch.set(FBFireStore.notifications.doc(), {
          'uId': _selectedUserId,
          'title': "Loan Approved",
          'desc':
              "Loan approved of ${num.tryParse(appliedLoanAmtCtrl.text) ?? 0}",
          'type': "loanAccepted",
          'districtOffice': userDoc.data()?['districtoffice'],
          'createdAt': Timestamp.fromDate(DateTime.now()),
        });

        batch.set(FBFireStore.notifications.doc(), {
          'uId': _selectedUserId,
          'title': "Share Deduction 10%",
          'desc': "Share Deduction 10% : ${num.tryParse(shareCtrl.text) ?? 0}",
          'type': "shareDeduction",
          'districtOffice': userDoc.data()?['districtoffice'],
          'createdAt':
              Timestamp.fromDate(DateTime.now().add(Duration(seconds: 10))),
        });
      }

      await batch.commit();

      setState(() => isLoading = false);

      if (context.mounted) {
        showCtcAppSnackBar(context, 'Loan created successfully');
        Navigator.pop(context);
      }

      context.go(Routes.loan);
    } catch (e) {
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Error creating loan');
      setState(() => isLoading = false);
    }
  }
}
