import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class LoanHistoryPage extends StatefulWidget {
  const LoanHistoryPage({super.key});

  @override
  State<LoanHistoryPage> createState() => _LoanHistoryPageState();
}

class _LoanHistoryPageState extends State<LoanHistoryPage> {
  String? selectedoffice;
  DateTime? selectedDate;
  DateTimeRange? selectedDateRange;

  @override
  void initState() {
    super.initState();
    selectedoffice = 'all';
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<Loan> loanHistoryFil = ctrl.loan.where((element) {
        if (!element.isSettled) return false;

        final user = ctrl.users.firstWhereOrNull((u) => u.docId == element.uid);

        if (selectedoffice != 'all' && user?.districtoffice != selectedoffice) {
          return false;
        }

        if (selectedDateRange != null) {
          final date = DateTime(element.appliedOn.year, element.appliedOn.month,
              element.appliedOn.day);
          return date.isAtSameMomentAs(selectedDateRange!.start) ||
              date.isAtSameMomentAs(selectedDateRange!.end) ||
              (date.isAfter(selectedDateRange!.start) &&
                  date.isBefore(selectedDateRange!.end));
        }

        return true;
      }).toList();

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(width: 10),
            Row(
              children: [
                DropdownButtonHideUnderline(
                    child: DropdownButtonFormField(
                  decoration: InputDecoration(
                      constraints: const BoxConstraints(maxWidth: 450),
                      border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10))),
                  value: selectedoffice,
                  items: [
                    const DropdownMenuItem(value: 'all', child: Text("All")),
                    ...List.generate(
                      ctrl.districtoffice.length,
                      (index) {
                        return DropdownMenuItem(
                            value: ctrl.districtoffice[index].docId,
                            child: Text(ctrl.districtoffice[index].name));
                      },
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      selectedoffice = value;
                    });
                  },
                )),
                if (selectedoffice != 'all') SizedBox(width: 5),
                if (selectedoffice != 'all')
                  IconButton(
                      style: OutlinedButton.styleFrom(),
                      onPressed: () {
                        setState(() {
                          selectedoffice = 'all';
                        });
                      },
                      icon: Icon(
                        Icons.clear,
                        size: 30,
                      )),
                SizedBox(width: 10),
                Row(
                  children: [
                    SizedBox(
                      width: 300,
                      child: InkWell(
                        onTap: () async {
                          final picked = await showDateRangePicker(
                            context: context,
                            firstDate: DateTime(DateTime.now().year - 1),
                            lastDate: DateTime(DateTime.now().year + 1),
                            initialDateRange: selectedDateRange,
                          );
                          if (picked != null) {
                            setState(() {
                              selectedDateRange = picked;
                            });
                          }
                        },
                        child: TextFormField(
                          enabled: false,
                          controller: TextEditingController(
                            text: selectedDateRange == null
                                ? 'Select Date Range'
                                : '${DateFormat('dd-MM-yyyy').format(selectedDateRange!.start)} - ${DateFormat('dd-MM-yyyy').format(selectedDateRange!.end)}',
                          ),
                          decoration: InputDecoration(
                            filled: true,
                            prefixIcon: const Icon(Icons.date_range,
                                size: 20, color: Colors.black),
                            border: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            hintText: 'Select Date Range',
                          ),
                        ),
                      ),
                    ),
                    if (selectedDateRange != null)
                      IconButton(
                        onPressed: () {
                          setState(() {
                            selectedDateRange = null;
                          });
                        },
                        icon: const Icon(Icons.clear, size: 20),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 40),
            const Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Applied On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Appl No.'),
                HeaderTxt(txt: 'Loan Type'),
              ],
            ),
            const SizedBox(height: 30),
            ...List.generate(
              loanHistoryFil.length,
              (index) {
                final user = ctrl.users.firstWhereOrNull(
                    (element) => element.docId == loanHistoryFil[index].uid);
                final doOffice = ctrl.districtoffice.firstWhereOrNull(
                    (element) => element.docId == user?.districtoffice);
                return InkWell(
                  onTap: () => context.push(
                      '${Routes.loanhistorydetails}/${loanHistoryFil[index].docId}',
                      extra: loanHistoryFil[index]),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(child: Text("${index + 1}")),
                          Expanded(
                            child: Text(DateFormat('dd-MM-yyyy')
                                .format(loanHistoryFil[index].appliedOn)),
                          ),
                          Expanded(child: Text(user?.name ?? "")),
                          Expanded(child: Text(doOffice?.name ?? "-")),
                          Expanded(
                              child: Text(loanHistoryFil[index]
                                  .applicationNo
                                  .toString())),
                          Expanded(child: Text(loanHistoryFil[index].loanType)),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }
}
