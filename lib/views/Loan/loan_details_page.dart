// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/const.dart';
import 'package:foodcorp_admin/shared/firebase.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class LoanDetailsPage extends StatefulWidget {
  const LoanDetailsPage({super.key, this.loanId});
  final String? loanId;
  // final Loan? loan;

  @override
  State<LoanDetailsPage> createState() => _LoanDetailsPageState();
}

class _LoanDetailsPageState extends State<LoanDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController tabCtrl;
  Loan? loanModelw;
  UserModel? userModelw;
  bool loanfullLoader = true;
  bool loanclosingloader = false;

  bool isLoading = false;
  bool addingTransLoading = false;

  bool isExpense = false; //isExpense == !inn

  bool showInCashbook = true;

  TextEditingController loanPreClosureReasonCtrl = TextEditingController();
  TextEditingController transAmtCtrl = TextEditingController();
  TextEditingController titleCtrl = TextEditingController();

  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(length: 2, vsync: this);
    getData();
  }

  @override
  void dispose() {
    tabCtrl.dispose();
    super.dispose();
  }

  final ctrl = Get.find<HomeCtrl>();

  Future<void> getData() async {
    if (widget.loanId != null) {
      final loanData = await FBFireStore.loan.doc(widget.loanId).get();
      loanModelw = Loan.fromSnap(loanData);
      if (loanModelw != null) {
        final userData = await FBFireStore.users.doc(loanModelw?.uid).get();
        userModelw = UserModel.fromSnap(userData);
      }
      // loadData();
    }
    setState(() => loanfullLoader = false);
  }

  Future<void> loadData() async {
    loanModelw?.applicationNo.toString();
    loanModelw?.uid;
    loanModelw?.totalLoanAmt.toString();
    loanModelw?.appliedLoanAmt.toString();
    loanModelw?.totalLoanPaid.toString();
    loanModelw?.totalLoanDue.toString();
    loanModelw?.loanType;
    loanModelw?.isSettled;
    loanModelw?.settledOn.toString();
    loanModelw?.isNew;
    loanModelw?.appliedOn.toString();
    loanModelw?.applicationNo.toString();
    loanModelw?.approvedOn.toString();
    loanModelw?.processedOn.toString();
    loanModelw?.share.toString();
    loanModelw?.totalInterestPaid.toString();
    loanModelw?.designation;
    loanModelw?.bAcNo;
    loanModelw?.appliedLoanAmtinWords;
    loanModelw?.loanReason;
    loanModelw?.bSign;
    loanModelw?.surityName1;
    loanModelw?.surityName2;
    loanModelw?.sAcNo1.toString();
    loanModelw?.sAcNo2.toString();
    // loanModelw?.balance.toString();
    loanModelw?.rejectionDate;
    loanModelw?.rejectionReason;
    loanModelw?.monthlyInstallmentAmt;

    // setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    double h = size.height;

    final transactions = ctrl.transactions
        .where((element) => element.uId == userModelw?.docId)
        .where((element) => element.loanId == widget.loanId)
        .toList();

    // print("trans length : ${transactions.length}");

    ctrl.transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final doOffice = ctrl.districtoffice.firstWhereOrNull(
        (element) => element.docId == userModelw?.districtoffice);

    return Scaffold(
        appBar: AppBar(
            title: Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        loanModelw?.applicationNo.toString() ?? "",
                        style: TextStyle(color: Colors.black),
                      ),
                      Spacer(),
                      Row(children: [
                        CustomHeaderButton(
                            onPressed: () => showDialog(
                                barrierDismissible: false,
                                context: context,
                                builder: (context) => StatefulBuilder(
                                        builder: (context, setState2) {
                                      // print("loanid : ${widget.loanId}");
                                      // print("userdocId : ${userModelw?.docId}");
                                      // print(
                                      //     "loanuserdocId : ${loanModelw?.uid}");

                                      return WillPopScope(
                                          onWillPop: () async => !isLoading,
                                          child: AlertDialog(
                                              title: Text("Add Transaction"),
                                              content: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  CustomTextfield(
                                                    text: 'Title',
                                                    enabled: true,
                                                    keyboardType:
                                                        TextInputType.text,
                                                    onFieldSubmitted: (p0) {},
                                                    controller: titleCtrl,
                                                  ),
                                                  CustomTextfield(
                                                    text: 'Amount',
                                                    enabled: true,
                                                    keyboardType:
                                                        TextInputType.number,
                                                    onFieldSubmitted: (p0) {},
                                                    controller: transAmtCtrl,
                                                  ),
                                                  Row(
                                                    children: [
                                                      Checkbox(
                                                        value: isExpense,
                                                        onChanged: (val) {
                                                          setState2(() {
                                                            isExpense =
                                                                val ?? false;
                                                          });
                                                        },
                                                      ),
                                                      const Text('Is Expense'),
                                                    ],
                                                  ),
                                                  Row(
                                                    children: [
                                                      Checkbox(
                                                        value: showInCashbook,
                                                        onChanged: (val) {
                                                          setState2(() {
                                                            showInCashbook =
                                                                val ?? true;
                                                          });
                                                        },
                                                      ),
                                                      const Text(
                                                          'Show in Cashbook'),
                                                    ],
                                                  ),
                                                  // SizedBox(height: 20),
                                                  // if (selectedType !=
                                                  //         null &&
                                                  //     !_pinVerified)
                                                  //   TextFormField(
                                                  //     decoration:
                                                  //         const InputDecoration(
                                                  //       filled: true,
                                                  //       border:
                                                  //           OutlineInputBorder(),
                                                  //       labelText:
                                                  //           'Enter PIN',
                                                  //     ),
                                                  //     controller:
                                                  //         userPinCtrl,
                                                  //     obscureText: true,
                                                  //     onChanged: (text) =>
                                                  //         setState(() =>
                                                  //             _enteredPin =
                                                  //                 text),
                                                  //     onFieldSubmitted:
                                                  //         (_) => _verifyPin(
                                                  //             setState2),
                                                  //   ),
                                                ],
                                              ),
                                              backgroundColor: Colors.white,
                                              actions: [
                                                TextButton(
                                                  onPressed: isLoading
                                                      ? null
                                                      : () async =>
                                                          context.pop(),
                                                  child: Text("Cancel"),
                                                ),
                                                TextButton(
                                                  onPressed: () async =>
                                                      addingTransLoading
                                                          ? CircularProgressIndicator()
                                                          : await handleAddTransactionOnPressed(
                                                              disOff: doOffice
                                                                      ?.docId ??
                                                                  "",
                                                              setState2:
                                                                  setState2,
                                                              context: context,
                                                              userdocId:
                                                                  loanModelw
                                                                          ?.uid ??
                                                                      "",
                                                              isExpense:
                                                                  isExpense,
                                                            )
                                                  // (_pinVerified &&
                                                  //         selectedType !=
                                                  //             null &&
                                                  //         !isLoading)
                                                  //     ? () async => handlePayoutConfirmation(
                                                  //         setState2:
                                                  //             setState2,
                                                  //         context:
                                                  //             context,
                                                  //         selectedType:
                                                  //             selectedType,
                                                  //         pinVerified:
                                                  //             _pinVerified,
                                                  //         usermodelw:
                                                  //             usermodelw,
                                                  //         userdocId:
                                                  //             usermodelw
                                                  //                     ?.docId ??
                                                  //                 "")
                                                  //     : null
                                                  ,
                                                  child: isLoading
                                                      ? CircularProgressIndicator(
                                                          strokeWidth: 2)
                                                      : Text("Confirm"),
                                                ),
                                              ]));
                                    })),
                            buttonName: 'Add Transactions'),
                        SizedBox(width: 10),
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black87,
                            elevation: 0,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                          ),
                          onPressed: () => context.push(
                              '${Routes.userDetails}/${userModelw?.docId}'),
                          label: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text("Go to user",
                                style: const TextStyle(fontSize: 14)),
                          ),
                        ),
                        SizedBox(width: 10),
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black87,
                            elevation: 0,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4)),
                          ),
                          onPressed: () => showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              backgroundColor: Colors.white,
                              title: const Text("Loan Pre-Closure"),
                              content: Container(
                                  constraints: BoxConstraints(maxWidth: 300),
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          "Closing Amount : ₹${loanModelw?.totalLoanDue}",
                                          style: TextStyle(fontSize: 16),
                                        ),
                                        SizedBox(height: 10),
                                        const Text(
                                            "Are you sure you want to close the loan?"),
                                        SizedBox(height: 10),
                                        TextField(
                                            controller:
                                                loanPreClosureReasonCtrl,
                                            minLines: 1,
                                            maxLines: 3,
                                            decoration: InputDecoration(
                                                filled: true,
                                                border: OutlineInputBorder(
                                                    borderSide: BorderSide.none,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6)),
                                                hintText: 'Reason'))
                                      ])),
                              actions: [
                                TextButton(
                                  onPressed: () async {
                                    if (context.mounted) {
                                      context.pop();
                                    }
                                  },
                                  child: const Text("No"),
                                ),
                                loanclosingloader
                                    ? Center(
                                        child:
                                            const CircularProgressIndicator())
                                    : TextButton(
                                        onPressed: () async {
                                          setState(
                                              () => loanclosingloader = true);

                                          final sfDocRef = FBFireStore.loan
                                              .doc(widget.loanId);
                                          final ufDocRef = FBFireStore.users
                                              .doc(loanModelw?.uid);

                                          // print("sfDocRef : $sfDocRef");
                                          // print("ufDocRef : $ufDocRef");

                                          if (loanPreClosureReasonCtrl
                                              .text.isEmpty) {
                                            showCtcAppSnackBar(context,
                                                "Please enter loan closure reason");
                                            setState(() {
                                              loanclosingloader = false;
                                            });
                                            return;
                                          }

                                          try {
                                            await FBFireStore.fb.runTransaction(
                                                (transaction) async {
                                              final loanSnapshot =
                                                  await transaction
                                                      .get(sfDocRef);
                                              final userSnapshot =
                                                  await transaction
                                                      .get(ufDocRef);

                                              if (!loanSnapshot.exists ||
                                                  !userSnapshot.exists) {
                                                throw Exception(
                                                    "Loan or User document not found");
                                              }

                                              final loanData =
                                                  loanSnapshot.data()!;
                                              final userData =
                                                  userSnapshot.data()!;

                                              // final totalloanDue =
                                              //     (loanData["totalLoanDue"] ?? 0);
                                              // final totalloanPaid =
                                              //     (loanData["totalLoanPaid"] ?? 0);
                                              // final totalinterestPaid =
                                              //     (loanData["totalInterestPaid"] ??
                                              //         0);

                                              final bool isLongTerm =
                                                  loanData["loanType"] ==
                                                      "Long Term Loan";

                                              if (isLongTerm) {
                                                transaction.update(ufDocRef, {
                                                  "ltLoansDue":
                                                      ((userData["ltLoansDue"] ??
                                                                  0) -
                                                              loanModelw
                                                                  ?.totalLoanDue)
                                                          .clamp(0,
                                                              double.infinity),
                                                  "totalLtLoans":
                                                      ((userData["totalLtLoans"] ??
                                                                  0) -
                                                              loanModelw
                                                                  ?.totalLoanDue)
                                                          .clamp(0,
                                                              double.infinity),
                                                  "totalLtIntPaid": ((userData[
                                                                  "totalLtIntPaid"] ??
                                                              0) -
                                                          loanModelw
                                                              ?.totalInterestPaid)
                                                      .clamp(
                                                          0, double.infinity),
                                                });
                                              } else {
                                                transaction.update(ufDocRef, {
                                                  "stLoansDue":
                                                      ((userData["stLoansDue"] ??
                                                                  0) -
                                                              loanModelw
                                                                  ?.totalLoanDue)
                                                          .clamp(0,
                                                              double.infinity),
                                                  "totalStLoans":
                                                      ((userData["totalStLoans"] ??
                                                                  0) -
                                                              loanModelw
                                                                  ?.totalLoanDue)
                                                          .clamp(0,
                                                              double.infinity),
                                                  "totalStIntPaid": ((userData[
                                                                  "totalStIntPaid"] ??
                                                              0) -
                                                          loanModelw
                                                              ?.totalInterestPaid)
                                                      .clamp(
                                                          0, double.infinity),
                                                });
                                              }

                                              transaction.update(sfDocRef, {
                                                "isSettled": true,
                                                "settledOn": DateTime.now(),
                                                "totalLoanDue": 0,
                                                // "balance": 0,
                                                "loanPreClosureReason":
                                                    loanPreClosureReasonCtrl
                                                        .text
                                                        .trim(),
                                              });

                                              transaction.set(
                                                  FBFireStore.transactions
                                                      .doc(),
                                                  {
                                                    "uId": userModelw?.docId,
                                                    "createdAt":
                                                        Timestamp.now(),
                                                    "title": "Loan Settled",
                                                    "amount": loanModelw
                                                        ?.totalLoanDue,
                                                    "inn": false,
                                                    "userMonthlyId": null,
                                                    "recoveryId": null,
                                                  });

                                              transaction.set(
                                                  FBFireStore.notifications
                                                      .doc(),
                                                  {
                                                    'uId': userModelw?.docId,
                                                    'title': "Loan Settled",
                                                    'desc':
                                                        "₹${loanModelw?.totalLoanDue}\n Reason : ${loanPreClosureReasonCtrl.text.trim()}",
                                                    'type': "loanClosure",
                                                    'districtOffice': userModelw
                                                        ?.districtoffice,
                                                    'createdAt':
                                                        Timestamp.now(),
                                                  });
                                            });

                                            if (context.mounted) {
                                              context.pop();
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                const SnackBar(
                                                    content: Text(
                                                        "Loan successfully closed.")),
                                              );
                                              context.go(Routes.loan);
                                            }
                                          } catch (e) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                  content: Text(
                                                      "Something went wrong. Please try again.")),
                                            );
                                            debugPrint(
                                                "Error during loan pre-closure: ${e.toString()}");
                                          } finally {
                                            setState(() =>
                                                loanclosingloader = false);
                                          }
                                        },
                                        child: const Text("Yes"),
                                      ),
                              ],
                            ),
                          ),
                          label: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text("Loan Pre-Closure",
                                style: const TextStyle(fontSize: 14)),
                          ),
                        ),
                      ])
                    ]))),
        body: loanfullLoader
            ? Center(
                child: SizedBox(
                    height: 30, width: 30, child: CircularProgressIndicator()))
            : Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: SizedBox(
                        height: 40,
                        width: 500,
                        child: TabBar(
                          labelColor: Colors.black,
                          dividerColor: Colors.black,
                          indicatorSize: TabBarIndicatorSize.tab,
                          controller: tabCtrl,
                          indicatorColor: Colors.green.shade300,
                          tabs: const <Widget>[
                            Tab(
                              child: Text(
                                "Details",
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                            Tab(
                              child: Text(
                                "Transactions",
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                        height: h * 0.7,
                        child: TabBarView(
                            physics: const NeverScrollableScrollPhysics(),
                            controller: tabCtrl,
                            children: [
                              SingleChildScrollView(
                                child: Padding(
                                  padding: const EdgeInsets.all(25),
                                  child: StaggeredGrid.extent(
                                    maxCrossAxisExtent: 377,
                                    mainAxisSpacing: 3,
                                    crossAxisSpacing: 10,
                                    children: [
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.applicationNo
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Application No.'),
                                      UserCustomTextField(
                                          initialvalue: DateFormat(
                                                  'dd - MM - yyyy')
                                              .format(loanModelw!.appliedOn),
                                          enabled: false,
                                          labeltext: 'Applied On.'),
                                      UserCustomTextField(
                                          initialvalue: DateFormat(
                                                  'dd - MM - yyyy')
                                              .format(loanModelw!.approvedOn!),
                                          enabled: false,
                                          labeltext: 'Approved On.'),
                                      UserCustomTextField(
                                          initialvalue: userModelw?.name ?? "",
                                          enabled: false,
                                          labeltext: 'Name'),
                                      UserCustomTextField(
                                          initialvalue:
                                              userModelw?.cpfNo.toString() ??
                                                  "",
                                          enabled: false,
                                          labeltext: 'CPF No.'),
                                      UserCustomTextField(
                                          initialvalue: doOffice?.name ?? "",
                                          enabled: false,
                                          labeltext: 'District Office'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.designation ?? "",
                                          enabled: false,
                                          labeltext: 'Designation'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.loanReason ?? "",
                                          enabled: false,
                                          labeltext: 'Loan Reason'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.loanType ?? "",
                                          enabled: false,
                                          labeltext: 'Loan Type'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.surityName1 ?? "",
                                          enabled: false,
                                          labeltext:
                                              'Surity Name of the First Person'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.surityName2 ?? "",
                                          enabled: false,
                                          labeltext:
                                              'Surity Name of the Second Person'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.appliedLoanAmt
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Applied Loan Amount'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.appliedLoanAmtinWords
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext:
                                              'Applied Loan Amount in words'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw?.approvedOn
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Approved On'),
                                      UserCustomTextField(
                                          initialvalue:
                                              loanModelw?.bAcNo.toString() ??
                                                  "",
                                          enabled: false,
                                          labeltext: 'Bank Account No.'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.monthlyInstallmentAmt
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext:
                                              'Monthly Installment Amount'),
                                      // UserCustomTextField(
                                      //     initialvalue:
                                      //         loanModelw?.share.toString() ??
                                      //             "",
                                      //     enabled: false,
                                      //     labeltext: 'Shares'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw?.totalLoanAmt
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Total Loan Amount'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw?.totalLoanDue
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Total Loan Due'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.totalLoanPaid
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Total Loan Paid'),
                                      UserCustomTextField(
                                          initialvalue: loanModelw
                                                  ?.totalInterestPaid
                                                  .toString() ??
                                              "",
                                          enabled: false,
                                          labeltext: 'Total Interest Paid'),
                                    ],
                                  ),
                                ),
                              ),
                              SingleChildScrollView(
                                  child: Padding(
                                      padding: const EdgeInsets.all(25),
                                      child: Column(children: [
                                        ...List.generate(
                                            transactions.length,
                                            (index) => Column(children: [
                                                  ListTile(
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 10,
                                                            horizontal: 3),
                                                    trailing: Text(
                                                      transactions[index]
                                                          .amount
                                                          .toString(),
                                                      style: TextStyle(
                                                          fontSize: 18,
                                                          fontWeight:
                                                              FontWeight.w500),
                                                    ),
                                                    subtitle: Text(
                                                      DateFormat(
                                                              "dd-MM-yyyy - hh:mm a")
                                                          .format(ctrl
                                                              .transactions[
                                                                  index]
                                                              .createdAt),
                                                      style: const TextStyle(
                                                          fontSize: 12,
                                                          fontWeight:
                                                              FontWeight.w400),
                                                    ),
                                                    title: Text(
                                                      "Paid ${transactions[index].title}",
                                                      style: TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w500),
                                                    ),
                                                    leading: ctrl
                                                                .transactions[
                                                                    index]
                                                                .inn ==
                                                            false
                                                        ? Icon(
                                                            color: const Color
                                                                .fromARGB(255,
                                                                230, 122, 114),
                                                            CupertinoIcons
                                                                .arrow_up_right)
                                                        : Icon(
                                                            color: const Color
                                                                .fromARGB(255,
                                                                164, 230, 114),
                                                            CupertinoIcons
                                                                .arrow_down_right),
                                                    // const Image(
                                                    //     image: AssetImage(
                                                    //         "assets/image.png")),
                                                  ),
                                                  Divider(
                                                      color:
                                                          Colors.grey.shade300,
                                                      thickness: 1)
                                                ]))
                                      ])))
                              // SingleChildScrollView(
                              //   child: Padding(
                              //     padding: const EdgeInsets.all(25),
                              //     child: Column(
                              //       mainAxisAlignment: MainAxisAlignment.start,
                              //       crossAxisAlignment: CrossAxisAlignment.start,
                              //       children: [
                              //         StaggeredGrid.extent(
                              //           maxCrossAxisExtent: 400,
                              //           mainAxisSpacing: 3,
                              //           crossAxisSpacing: 10,
                              //           children: [
                              //             UserCustomTextField(
                              //                 initialvalue:
                              //                     usermodelw?.ltLoansDue.toString() ??
                              //                         "",
                              //                 enabled: false,
                              //                 labeltext: 'Long Term Loans Due'),
                              //             UserCustomTextField(
                              //                 initialvalue:
                              //                     usermodelw?.stLoansDue.toString() ??
                              //                         "",
                              //                 enabled: false,
                              //                 labeltext: 'Short Term Loans Due'),
                              //             UserCustomTextField(
                              //                 initialvalue: usermodelw?.totalLtLoans
                              //                         .toString() ??
                              //                     "",
                              //                 enabled: false,
                              //                 labeltext: 'Total Long term Loans'),
                              //             UserCustomTextField(
                              //                 initialvalue: usermodelw?.totalStLoans
                              //                         .toString() ??
                              //                     "",
                              //                 enabled: false,
                              //                 labeltext: 'Total Short term Loans'),
                              //             UserCustomTextField(
                              //                 initialvalue: usermodelw?.totalLtIntPaid
                              //                         .toString() ??
                              //                     "",
                              //                 enabled: false,
                              //                 labeltext:
                              //                     'Total Long term Interest Paid'),
                              //             UserCustomTextField(
                              //                 initialvalue: usermodelw?.totalStIntPaid
                              //                         .toString() ??
                              //                     "",
                              //                 enabled: false,
                              //                 labeltext:
                              //                     'Total Short term Interest Paid'),
                              //           ],
                              //         ),
                              //         SizedBox(height: 15),
                              //         // ...List.generate(
                              //         //   currentUserLoan.length,
                              //         //   (index) => InkWell(
                              //         //     splashColor: Colors.transparent,
                              //         //     hoverColor: Colors.transparent,
                              //         //     focusColor: Colors.transparent,
                              //         //     onTap: () =>
                              //         //         context.push(Routes.userloandetails),
                              //         //     child: UserCustomCard(
                              //         //         extraTexxt: DateFormat("dd/MM/yyyy")
                              //         //             .format(ctrl.loan.first.createdAt),
                              //         //         dataTexxt:
                              //         //             "APPL. NO. ${ctrl.loan.first.applicationNo}"
                              //         //                 .toString()),
                              //         //   ),
                              //         // ),
                              //       ],
                              //     ),
                              //   ),
                              // ),
                            ]))
                  ]));
  }

  Future<void> handleAddTransactionOnPressed(
      {required BuildContext context,
      required String userdocId,
      required void Function(void Function()) setState2,
      required bool isExpense,
      required String disOff}) async {
    if (titleCtrl.text.isEmpty || transAmtCtrl.text.isEmpty) {
      showCtcAppSnackBar(context, 'Please fill all the fields');
      setState2(() => addingTransLoading = false);
      return;
    }

    setState2(() => addingTransLoading = true);

    final batch = FBFireStore.fb.batch();

    try {
      final currentFinancialYear = TheFinancialYear.getCurrentYearForDatabase();
      final socYearlySnap = await FBFireStore.societyYearly
          .where('selectedyear', isEqualTo: currentFinancialYear)
          .limit(1)
          .get();

      // transaction add

      batch.set(FBFireStore.transactions.doc(), {
        'uId': userdocId,
        'createdAt': DateTime.now(),
        'title': '${titleCtrl.text.toString()} Transaction',
        'amount': num.tryParse(transAmtCtrl.text.toString()) ?? 0,
        'inn': !isExpense,
        'userMonthlyId': null,
        'recoveryId': null,
        'loanId': widget.loanId,
        'showInCB': showInCashbook,
      });

      //loan update

      batch.update(FBFireStore.loan.doc(widget.loanId), {
        'totalLoanDue': (loanModelw?.totalLoanDue ?? 0) -
            (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
        'totalLoanPaid': (loanModelw?.totalLoanPaid ?? 0) +
            (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
      });

      //user update

      if (loanModelw?.loanType == LoanTypes.longTerm) {
        batch.update(FBFireStore.users.doc(loanModelw?.uid), {
          'ltLoansDue': (userModelw?.ltLoansDue ?? 0) -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'totalLtLoans': (userModelw?.totalLtLoans ?? 0) -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
        });

        batch.update(
            FBFireStore.societyYearly.doc(socYearlySnap.docs.first.id), {
          'ltLoanReceived': socYearlySnap.docs.first.data()['ltLoanReceived'] +
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'ltPendingLoan': socYearlySnap.docs.first.data()['ltPendingLoan'] -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'totalLoanReceived':
              socYearlySnap.docs.first.data()['totalLoanReceived'] +
                  (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          // '' : ,
        });
      } else {
        batch.update(FBFireStore.users.doc(loanModelw?.uid), {
          'stLoansDue': (userModelw?.stLoansDue ?? 0) -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'totalStLoans': (userModelw?.totalStLoans ?? 0) -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
        });

        batch.update(
            FBFireStore.societyYearly.doc(socYearlySnap.docs.first.id), {
          'ltLoanReceived': socYearlySnap.docs.first.data()['ltLoanReceived'] +
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'ltPendingLoan': socYearlySnap.docs.first.data()['ltPendingLoan'] -
              (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          'totalLoanReceived':
              socYearlySnap.docs.first.data()['totalLoanReceived'] +
                  (num.tryParse(transAmtCtrl.text.toString()) ?? 0),
          // '' : ,
        });
      }

      //notifications

      batch.set(FBFireStore.notifications.doc(), {
        'uId': userdocId,
        'title': titleCtrl.text.toString(),
        'desc': transAmtCtrl.text.toString(),
        'type': 'Transaction',
        'districtOffice': userModelw?.districtoffice ?? '',
        'createdAt': DateTime.now(),
      });

      await batch.commit();

      context.pop();
      setState2(() => addingTransLoading = false);
      setState2(() => isExpense = false);
    } catch (e) {
      debugPrint('add transaction dialog error : ${e.toString()}');
      setState2(() => isExpense = false);
      setState2(() => addingTransLoading = false);
    }
  }
}


  // onPressed: () async {
  //                             setState(() => loading = true);
  //                             final sfDocRef =
  //                                 FBFireStore.loan.doc(widget.loanId);
  //                             final ufDocRef =
  //                                 FBFireStore.users.doc(loanModelw?.uid);
  //                             try {
  //                               await FBFireStore.fb
  //                                   .runTransaction((transaction) async {
  //                                 final loanSnapshot =
  //                                     await transaction.get(sfDocRef);
  //                                 final userSnapshot =
  //                                     await transaction.get(ufDocRef);
  //                                 if (!loanSnapshot.exists ||
  //                                     !userSnapshot.exists) {
  //                                   throw Exception(
  //                                       "Loan or User document not found");
  //                                 }
  //                                 final loanData = loanSnapshot.data()!;
  //                                 final userData = userSnapshot.data()!;
  //                                 final double loanDue =
  //                                     loanData["totalLoanDue"] ?? 0;
  //                                 final double interestPaid =
  //                                     loanData["totalInterestPaid"] ?? 0;
  //                                 final bool isLongTerm =
  //                                     loanData["loanType"] == "longTerm";
  //                                 transaction.update(sfDocRef, {
  //                                   "isSettled": true,
  //                                   "settledOn": DateTime.now(),
  //                                   // "totalLoanPaid": 0,
  //                                   "totalLoanDue": 0,
  //                                   // "totalInterestPaid": 0,
  //                                 });
  //                                 transaction.update(ufDocRef, {
  //                                   if (isLongTerm)
  //                                     "ltLoansDue":
  //                                         (userData["ltLoansDue"] ?? 0) -
  //                                             loanDue,
  //                                   if (!isLongTerm)
  //                                     "stLoansDue":
  //                                         (userData["stLoansDue"] ?? 0) -
  //                                             loanDue,
  //                                   if (isLongTerm)
  //                                     "totalLtIntPaid":
  //                                         (userData["totalLtIntPaid"] ?? 0) -
  //                                             interestPaid,
  //                                   if (!isLongTerm)
  //                                     "totalStIntPaid":
  //                                         (userData["totalStIntPaid"] ?? 0) -
  //                                             interestPaid,
  //                                 });
  //                               });
  //                               if (context.mounted) {
  //                                 context.pop();
  //                               }
  //                             } catch (e) {
  //                               ScaffoldMessenger.of(context).showSnackBar(SnackBar(
  //                                   content: Text(
  //                                       "Something went wrong. Please try again.")));
  //                               print("Error during loan pre-closure: $e");
  //                             } finally {
  //                               setState(() => loading = false);
  //                             }
  //                           },