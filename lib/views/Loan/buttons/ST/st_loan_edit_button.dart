import 'package:flutter/material.dart';

class StLoanEditButton extends StatefulWidget {
  const StLoanEditButton({super.key, this.onPressed});
  final Function()? onPressed;
  @override
  State<StLoanEditButton> createState() => _StLoanEditButtonState();
}

class _StLoanEditButtonState extends State<StLoanEditButton> {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: const ButtonStyle(
        overlayColor: WidgetStatePropertyAll(Colors.transparent),
        foregroundColor: WidgetStatePropertyAll(Colors.transparent),
        padding: WidgetStatePropertyAll(
            EdgeInsetsDirectional.symmetric(horizontal: 40, vertical: 15)),
        shape: WidgetStatePropertyAll(BeveledRectangleBorder(
            side: BorderSide(color: Colors.black, width: 0))),
        backgroundColor: WidgetStatePropertyAll(Colors.transparent),
        elevation: WidgetStatePropertyAll(0),
      ),
      onPressed: widget.onPressed,
      child: const Text(
        "EDIT",
        style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            letterSpacing: 3,
            fontWeight: FontWeight.bold),
      ),
    );
  }
}
