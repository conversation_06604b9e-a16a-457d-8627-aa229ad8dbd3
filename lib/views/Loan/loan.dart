import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/common/page_header.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:foodcorp_admin/models/user_model.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/shared/router.dart';
// import 'package:foodcorp_admin/views/Loan/forms/ltloanform.dart';
// import 'package:foodcorp_admin/views/Loan/forms/stloanform.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'dart:html' as html;

enum SelectedLoan { longtermloan, shorttermloan }

class LoanPage extends StatefulWidget {
  const LoanPage({super.key});

  @override
  State<LoanPage> createState() => _LoanPageState();
}

SearchController sctrl = SearchController();

class _LoanPageState extends State<LoanPage> {
  UserModel? selectedUser;
  SelectedLoan selectedLoan = SelectedLoan.longtermloan;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(builder: (ctrl) {
      List<Loan> filtered = ctrl.loan
          .where((element) => element.rejectionDate == null)
          .where((element) => element.isSettled == false)
          .where((element) => element.settledOn == null)
          .where((element) => element.isSettled == false)
          .where((element) => selectedLoan == SelectedLoan.longtermloan
              ? element.loanType == 'Long Term Loan'
              : element.loanType == 'Emergency Loan')
          .where((loan) {
        final user = ctrl.users.firstWhereOrNull((u) => u.docId == loan.uid);
        final search = sctrl.text.toLowerCase();
        return loan.applicationNo.toString().contains(search) ||
            (user?.name.toLowerCase().contains(search) ?? false);
      }).toList();

      filtered.sort((a, b) => b.approvedOn!.compareTo(a.approvedOn!));

      if (selectedUser != null) {
        filtered = filtered
            .where((element) => element.uid == selectedUser?.docId)
            .toList();
      }

      return SingleChildScrollView(
        padding: const EdgeInsets.all(40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Row(
                    children: [
                      CustomSearchBarWidget(
                        searchController: sctrl,
                        searchOnChanged: (p0) {
                          setState(() {});
                        },
                      ),
                      // SizedBox(width: 10),
                      // DropdownSearch<UserModel>(
                      //   selectedItem: selectedUser,
                      //   onChanged: (value) {
                      //     selectedUser = value;
                      //     setState(() {});
                      //   },
                      //   decoratorProps: DropDownDecoratorProps(
                      //     decoration: InputDecoration(
                      //       hintText: "Search User",
                      //       constraints: const BoxConstraints(maxWidth: 450),
                      //       border: OutlineInputBorder(
                      //         borderRadius: BorderRadius.circular(10),
                      //       ),
                      //     ),
                      //   ),
                      //   popupProps: PopupProps.menu(
                      //     showSearchBox: true,
                      //     searchFieldProps: TextFieldProps(
                      //       decoration: InputDecoration(
                      //         hintText: ' Search... ',
                      //         border: UnderlineInputBorder(),
                      //       ),
                      //     ),
                      //   ),
                      //   itemAsString: (item) => item.name,
                      //   items: (filter, loadProps) => ctrl.users,
                      //   compareFn: (item1, item2) =>
                      //       item1.name.toLowerCase() == item2.name.toLowerCase(),
                      // ),
                      // if (selectedUser != null) const SizedBox(width: 5),
                      // if (selectedUser != null)
                      //   IconButton(
                      //     onPressed: () {
                      //       setState(() {
                      //         selectedUser = null;
                      //       });
                      //     },
                      //     icon: const Icon(Icons.clear, size: 30),
                      //   ),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomHeaderButton(
                      onPressed: () => context.push(Routes.addloanpage),
                      buttonName: "Add Loan",
                    ),
                    SizedBox(width: 5),
                    CustomHeaderButton(
                      onPressed: () => context.push(Routes.loanhistory),
                      buttonName: "Loan History",
                    ),
                  ],
                )
              ],
            ),
            const SizedBox(height: 30),
            CupertinoSlidingSegmentedControl<SelectedLoan>(
              thumbColor: Colors.green.shade300,
              groupValue: selectedLoan,
              children: {
                SelectedLoan.longtermloan: Text(
                  "Long Term Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.longtermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
                SelectedLoan.shorttermloan: Text(
                  "Emergency Loan",
                  style: TextStyle(
                    color: selectedLoan == SelectedLoan.shorttermloan
                        ? Colors.white
                        : Colors.black,
                  ),
                ),
              },
              onValueChanged: (SelectedLoan? value) {
                if (value != null) {
                  setState(() {
                    selectedLoan = value;
                  });
                }
              },
            ),
            const SizedBox(height: 30),
            Row(
              children: [
                HeaderTxt(txt: 'Sr.no'),
                HeaderTxt(txt: "Approved On"),
                HeaderTxt(txt: "Name"),
                HeaderTxt(txt: "DO Office"),
                HeaderTxt(txt: 'Appl No.'),
                HeaderTxt(txt: 'Loan Amount'),
                if (selectedLoan == SelectedLoan.longtermloan)
                  SizedBox(width: 40, child: Text(''))
                else
                  SizedBox(width: 40),
              ],
            ),
            const SizedBox(height: 30),
            ...List.generate(
              filtered.length,
              (index) {
                final loan = filtered[index];
                final user = ctrl.users
                    .firstWhereOrNull((element) => element.docId == loan.uid);
                final doOffice = ctrl.districtoffice.firstWhereOrNull(
                    (element) => element.docId == user?.districtoffice);

                return InkWell(
                  onTap: () => context.push(
                    '${Routes.loanDetails}/${loan.docId}',
                  ),
                  child: Container(
                    height: 40,
                    color: index % 2 == 0 ? Colors.white : null,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Row(
                        children: [
                          Expanded(flex: 1, child: Text("${index + 1}")),
                          Expanded(
                            flex: 1,
                            child: Text(
                              DateFormat('dd-MM-yyyy').format(loan.approvedOn!),
                            ),
                          ),
                          Expanded(
                              flex: 1,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(user?.name ?? ""))),
                          Expanded(flex: 1, child: Text(doOffice?.name ?? "-")),
                          Expanded(
                              flex: 1,
                              child: Text(loan.applicationNo.toString())),
                          Expanded(
                              flex: 1,
                              child: Text(loan.appliedLoanAmt.toString())),
                          // Expanded(child: Text(loan.loanType)),
                          if (selectedLoan == SelectedLoan.longtermloan)
                            SizedBox(
                              width: 40,
                              child: IconButton(
                                icon: Icon(Icons.download),
                                tooltip: 'Download Form',
                                onPressed: () async {
                                  await createAndDownloadLoanPdfWeb(
                                      filtered[index], user, doOffice?.name);
                                },
                              ),
                            )
                          else
                            SizedBox(width: 40),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }

  Future<void> createAndDownloadLoanPdfWeb(
      Loan loan, UserModel? user, String? doOffice) async {
    final pdf = pw.Document();

    final textNormal = pw.TextStyle(fontSize: 12);
    // final textBold = pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold);
    final smallText = pw.TextStyle(fontSize: 10);

    final signatureBytes = await fetchSignatureBytes(loan.bSign.toString());

    // Helper for labels with underline values
    pw.Widget buildLabelWithUnderline(String label, String value,
        {double labelWidth = 120, double valueWidth = 250}) {
      return pw.Padding(
        padding: const pw.EdgeInsets.symmetric(vertical: 6),
        child: pw.Row(children: [
          pw.Container(
            width: labelWidth,
            child: pw.Text(label, style: pw.TextStyle(fontSize: 12)),
          ),
          pw.SizedBox(width: 12),
          pw.Container(
            width: valueWidth,
            padding: const pw.EdgeInsets.only(left: 4, bottom: 2),
            decoration: pw.BoxDecoration(
              border: pw.Border(
                bottom: pw.BorderSide(width: 1, color: PdfColors.black),
              ),
            ),
            child: pw.Text(value, style: pw.TextStyle(fontSize: 12)),
          ),
        ]),
      );
    }

    // Page 1: APPLICATION FOR LONG TERM LOAN
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.symmetric(vertical: 25, horizontal: 40),
        build: (context) {
          return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with price and application no.
                pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text("Price : Rs. 10/-", style: smallText),
                      pw.Text("Application No.: ${loan.applicationNo}",
                          style: smallText),
                    ]),
                pw.SizedBox(height: 15),
                // Main title centered bold
                pw.Center(
                    child: pw.Text(
                        textAlign: pw.TextAlign.center,
                        'FOOD CORPORATION OF INDIA EMPLOYEES\' CO-OPERATIVE CREDIT SOCIETY LTD. BARODA.',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, fontSize: 14))),
                pw.SizedBox(height: 10),
                pw.Center(
                    child: pw.Text('Alembic Road, Baroda - 3.',
                        style: textNormal)),
                pw.SizedBox(height: 15),

                pw.Center(
                    child: pw.Text('APPLICATION FOR LONG TERM LOAN',
                        style: pw.TextStyle(
                            fontWeight: pw.FontWeight.bold, fontSize: 14))),

                pw.SizedBox(height: 10),

                buildLabelWithUnderline('Full Name', user?.name ?? '-',
                    labelWidth: 120, valueWidth: 300),
                pw.SizedBox(height: 4),
                pw.Row(children: [
                  buildLabelWithUnderline(
                      'Designation', loan.designation ?? '-',
                      labelWidth: 80, valueWidth: 150),
                  pw.SizedBox(width: 4),
                  buildLabelWithUnderline(
                      'Account No.', user?.bankAcNo.toString() ?? '-',
                      labelWidth: 80, valueWidth: 150),
                ]),
                pw.SizedBox(height: 4),
                buildLabelWithUnderline(
                    'Full Address', user?.currentAddress ?? '-',
                    labelWidth: 120, valueWidth: 300),
                // pw.SizedBox(height: 8),
                // buildLabelWithUnderline(
                //     'Permanent/Temporary', user?.employmentType ?? '',
                //     width: 150),
                pw.SizedBox(height: 4),
                pw.Row(
                  children: [
                    buildLabelWithUnderline('Loan Applied for Rs.',
                        loan.totalLoanAmt.toString() ?? "-",
                        labelWidth: 120, valueWidth: 60),
                    pw.SizedBox(width: 10),
                    buildLabelWithUnderline(
                        'In words Rs', loan.appliedLoanAmtinWords ?? '-',
                        labelWidth: 80, valueWidth: 180),
                  ],
                ),
                pw.SizedBox(height: 4),
                buildLabelWithUnderline(
                    'Reason for Loan', loan.loanReason ?? '-',
                    labelWidth: 120, valueWidth: 300),
                pw.SizedBox(height: 4),
                pw.Row(
                  children: [
                    pw.Text("Applicant's Signature: "),
                    pw.SizedBox(width: 10),
                    buildSignatureWidget(signatureBytes),
                  ],
                ),
                // pw.SizedBox(height: 25),
                pw.Divider(),
                pw.SizedBox(height: 10),

                pw.Text(
                    'We the undersigned hereby agreed and undertake to stand sureties to:',
                    style: textNormal),
                pw.SizedBox(height: 4),
                pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      buildLabelWithUnderline('Shri/Smt.', user?.name ?? '-',
                          labelWidth: 60, valueWidth: 300),
                    ]),
                pw.SizedBox(height: 7),
                // pw.Row(
                //   mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                //   children: [
                pw.Row(
                  children: [
                    buildLabelWithUnderline('1) Name', loan.surityName1 ?? "-",
                        labelWidth: 60, valueWidth: 150),
                    pw.SizedBox(width: 8),
                    buildLabelWithUnderline(
                        'Account No.', '${loan.sAcNo1 ?? "-"}',
                        labelWidth: 100, valueWidth: 150),
                  ],
                ),
                pw.Row(children: [
                  buildLabelWithUnderline('2) Name', loan.surityName2 ?? "-",
                      labelWidth: 60, valueWidth: 150),
                  pw.SizedBox(width: 8),
                  buildLabelWithUnderline(
                      'Account No.', '${loan.sAcNo2 ?? "-"}',
                      labelWidth: 100, valueWidth: 150),
                ]),

                // pw.SizedBox(height: 12),
                pw.Divider(),
                // pw.SizedBox(height: 15),
                pw.Center(
                  child: pw.Text('RESOLUTION',
                      style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold, fontSize: 16)),
                ),
                pw.SizedBox(height: 10),
                pw.Text(
                  'Managing Committee of the Food Corporation of India Employee\'s Co Operative Credit Society Ltd. Baroda resolved that an amount of Rs.${loan.appliedLoanAmt ?? "_____________"} in words (Rs.${loan.appliedLoanAmtinWords ?? "_____________"}) has been granted as the Long Term Loan to the applicant today on ${loan.approvedOn != null ? DateFormat('dd-MM-yyyy').format(loan.approvedOn!) : "______________"} .',
                  style: textNormal,
                ),
                pw.SizedBox(height: 15),
                pw.Align(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Text('CHAIRMAN',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ),
                pw.Divider(),
                pw.Center(
                  child: pw.Text('RECEIPT',
                      style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold, fontSize: 16)),
                ),
                pw.SizedBox(height: 10),
                pw.Text(
                  'Received Rs.${loan.appliedLoanAmt} in words (Rs.${loan.appliedLoanAmtinWords ?? ""} only) from Food Corporation of India Employee\'s Co-operative Credit Society Limited Baroda towards the Long Term Loan today On  ${loan.approvedOn != null ? DateFormat('dd-MM-yyyy').format(loan.approvedOn!) : "______________"} ',
                  // ${DateFormat('dd-MM-yyyy').format(DateTime.now())}.',
                  style: textNormal,
                ),
                pw.SizedBox(height: 10),
                pw.Row(children: [
                  buildLabelWithUnderline('Cash/Cheque No.', '',
                      labelWidth: 100, valueWidth: 150),
                  pw.SizedBox(width: 8),
                  buildLabelWithUnderline('Date', '',
                      labelWidth: 100, valueWidth: 150),
                ]),
                pw.Row(
                  children: [
                    pw.Text(
                      "Receiver's signature",
                    ),
                    pw.SizedBox(width: 10),
                    buildSignatureWidget(signatureBytes),
                  ],
                ),
                // buildLabelWithUnderline(
                //     'Receiver\'s signature', loan.bSign ?? '-',
                //     labelWidth: 120, valueWidth: 300),
                pw.SizedBox(height: 15),
                pw.Row(children: [
                  buildLabelWithUnderline(
                      'A/c No.', user?.bankAcNo.toString() ?? '',
                      labelWidth: 100, valueWidth: 150),
                  pw.SizedBox(width: 8),
                  buildLabelWithUnderline('Unit', doOffice ?? '',
                      labelWidth: 100, valueWidth: 150),
                ]),
              ]);
        },
      ),
    );

    // Page 2: RECEIPT and LOAN BOND/AGREEMENT
    // Page 2: LOAN BOND / AGREEMENT
// PAGE 2: Loan Bond / Agreement
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.symmetric(horizontal: 40, vertical: 30),
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Title
              pw.Center(
                child: pw.Text(
                  "LOAN BOND / AGREEMENT",
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),

              // First paragraph with blanks
              pw.Text(
                "Shri/Smt. __________________________ hereby acknowledge the receipt of "
                "loan Rs. ____________________ In words (Rs. ____________________ only) "
                "from the said society repayable by monthly instalment of Rs. ___________ "
                "within ___________ instalments with interest of 8.5% P.A. as per terms "
                "and conditions mentioned in the application for loan as per provisions "
                "of the bye laws of the Society and authorise Pay Controlling Authority "
                "of Food Corporation of India to deduct on behalf of the Society from "
                "his/her salary or other sums payable to him/her nominee or heirs the "
                "above mentioned loan amount with interest due in one or more instalments "
                "according to the determination of the Managing Committee of the said "
                "society from time to time.",
                textAlign: pw.TextAlign.justify,
                style: pw.TextStyle(fontSize: 12),
              ),
              pw.SizedBox(height: 25),

              // Signatures block
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(children: [
                    pw.SizedBox(height: 40),
                    pw.Text("Witness Signature",
                        style: pw.TextStyle(fontSize: 11)),
                  ]),
                  pw.Column(children: [
                    pw.SizedBox(height: 40),
                    pw.Text("Borrower's Signature",
                        style: pw.TextStyle(fontSize: 11)),
                    pw.SizedBox(height: 5),
                    pw.Text("Date:", style: pw.TextStyle(fontSize: 11)),
                  ]),
                  pw.Column(children: [
                    pw.SizedBox(height: 40),
                    pw.Text("Account No. ________",
                        style: pw.TextStyle(fontSize: 11)),
                    pw.SizedBox(height: 5),
                    pw.Text("Unit ______________",
                        style: pw.TextStyle(fontSize: 11)),
                  ]),
                ],
              ),
              pw.SizedBox(height: 30),

              // Second paragraph
              pw.Text(
                "We the undersigned hereby agreed and undertake to stand sureties and "
                "bind ourselves jointly and severally liable to the society for the "
                "repayment of loan with interest in accordance with the conditions and "
                "rules and bye laws of the society and in the event of default or "
                "non-payment on the part of Shri/Smt. ____________________ borrower, "
                "we authorise the society to recover the loan due with interest from "
                "amount due to us or to their nominee or heirs by their employers Pay "
                "Controlling Authority of Food Corporation of India. Further, we certify "
                "that we have not stood sureties more than two members, including the "
                "Applicant and in whose cases the loan amount is under recovery.",
                textAlign: pw.TextAlign.justify,
                style: pw.TextStyle(fontSize: 12),
              ),
              pw.SizedBox(height: 25),

              // Sureties section
              pw.SizedBox(height: 30),

              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 120,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("1. Witness Signature",
                          style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 150,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("First Surety's Signature",
                          style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 120,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("Account No.", style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 40),

              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 120,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("2. Witness Signature",
                          style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 150,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("Second Surety's Signature",
                          style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                  pw.Column(
                    children: [
                      pw.Container(
                        width: 120,
                        decoration: pw.BoxDecoration(
                          border: pw.Border(bottom: pw.BorderSide(width: 1)),
                        ),
                        child: pw.SizedBox(height: 20),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text("Account No.", style: pw.TextStyle(fontSize: 11)),
                    ],
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );

    // Save and trigger download
    Uint8List pdfBytes = await pdf.save();

    final blob = html.Blob([pdfBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', 'loan_application_${loan.applicationNo}.pdf')
      ..click();
    html.Url.revokeObjectUrl(url);
  }

  // Future<dynamic> applDetailsDialog(
  //   BuildContext context,
  //   HomeCtrl ctrl,
  //   int index,
  // ) {
  //   return showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       backgroundColor: Colors.white,
  //       scrollable: true,
  //       content: SingleChildScrollView(
  //         child: ConstrainedBox(
  //           constraints: const BoxConstraints(maxHeight: 400),
  //           child: SizedBox(
  //             width: 800,
  //             child: (ctrl.loan[index].loanType == 'Long Term Loan')
  //                 ? ApprovedLtloanform(
  //                     loan: ctrl.loan[index],
  //                     index: index,
  //                   )
  //                 : ApprovedStloanform(
  //                     loan: ctrl.loan[index],
  //                     index: index,
  //                   ),
  //           ),
  //         ),
  //       ),
  //       title: const Text("Application Details"),
  //     ),
  //   );
  // }
}

Future<Uint8List?> fetchSignatureBytes(String imageUrl) async {
  try {
    if (imageUrl.isEmpty) return null;

    final response = await http.get(Uri.parse(imageUrl));
    if (response.statusCode == 200) {
      // Validate mime-type
      final contentType = response.headers['content-type'] ?? '';
      if (contentType.contains('image/png') ||
          contentType.contains('image/jpeg') ||
          contentType.contains('image/jpg')) {
        return response.bodyBytes;
      } else {
        print("Invalid content-type: $contentType");
      }
    } else {
      print("HTTP error: ${response.statusCode}");
    }
  } catch (e) {
    print('Error fetching image bytes: $e');
  }
  return null;
}

pw.Widget buildSignatureWidget(Uint8List? imageBytes,
    {double width = 150, double height = 50}) {
  if (imageBytes == null) {
    return pw.Container(
      width: width,
      height: height,
      decoration: pw.BoxDecoration(border: pw.Border.all()),
      child: pw.Center(child: pw.Text('')),
    );
  }
  final image = pw.MemoryImage(imageBytes);
  return pw.Container(
    width: width,
    height: height,
    // decoration: pw.BoxDecoration(border: pw.Border.all()),
    child: pw.Image(image, fit: pw.BoxFit.contain),
  );
}
