import 'package:flutter/material.dart';
import 'package:foodcorp_admin/shared/methods.dart';
import 'package:foodcorp_admin/views/drawer/dashboard_drawer.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: snackbar2Key,
      backgroundColor: Colors.grey.shade100,
      body: Row(
        children: [
          const DashboardDrawer(),
          // const SizedBox(width: 20),
          Expanded(child: child),
        ],
      ),
    );
  }
}
