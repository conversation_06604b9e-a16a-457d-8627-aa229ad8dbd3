import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import '../../shared/firebase.dart';
import 'db_drawer_tile.dart';

class DashboardDrawer extends StatefulWidget {
  const DashboardDrawer({
    super.key,
    this.isTablet = false,
    this.scafKey,
  });

  final bool isTablet;
  final GlobalKey<ScaffoldState>? scafKey;

  @override
  State<DashboardDrawer> createState() => _DashboardDrawerState();
}

bool loading = false;

class _DashboardDrawerState extends State<DashboardDrawer> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: GetBuilder<HomeCtrl>(builder: (ctrl) {
        return SizedBox(
          width: 260,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // L O G O
              const SizedBox(height: 30),
              DashHeader(isTablet: widget.isTablet),
              const SizedBox(height: 20),

              // I T E M S
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      DashboardTile(
                        icon: Icons.dashboard_outlined,
                        textName: 'Dashboard',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.dashboard,
                        isSelected: true,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        number: ctrl.newLoan.length.toString(),
                        showNumbers: true,
                        icon: Icons.local_atm_rounded,
                        textName: 'Loan Request',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.loanapplication,
                        isSelected: true,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        showNumbers: true,
                        number: ctrl.newusersapplication.length.toString(),
                        icon: Icons.location_history_outlined,
                        textName: 'User Request',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.userapplication,
                        isSelected: true,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        showNumbers: true,
                        number: ctrl.updateRequests
                            .where((element) => element.accepted == null)
                            .length
                            .toString(),
                        icon: Icons.update_outlined,
                        textName: 'Update Request',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.updaterequest,
                        isSelected: true,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.house_outlined,
                        textName: 'District Office',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.dooffice,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.people_alt_outlined,
                        textName: 'Users',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.users,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: CupertinoIcons.percent,
                        textName: 'Active Loans',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.loan,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: CupertinoIcons.doc_text,
                        textName: 'Reports',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.reports,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: CupertinoIcons.layers_alt,
                        textName: 'Recovery',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.recovery,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: CupertinoIcons.money_dollar,
                        textName: 'Expenses',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.expense,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.playlist_add_check_outlined,
                        textName: 'Subs Interest Payout',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.subsintpayout,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.trending_up_outlined,
                        textName: 'Dividend Payout',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.dividendpayout,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: CupertinoIcons.chat_bubble_2,
                        textName: 'Notifications',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.notifications,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.settings_outlined,
                        textName: 'Settings',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.pinpage,
                      ),
                      const SizedBox(height: 5),
                      DashboardTile(
                        icon: Icons.feedback_outlined,
                        textName: 'Feedback',
                        tab: widget.isTablet,
                        scafKey: widget.scafKey,
                        route: Routes.feedback,
                      ),
                      const SizedBox(height: 5),
                    ],
                  ),
                ),
              ),
              loading
                  ? CircularProgressIndicator()
                  : ElevatedButton(
                      style: const ButtonStyle(
                          elevation: WidgetStatePropertyAll(0),
                          overlayColor: WidgetStatePropertyAll(
                            Colors.transparent,
                          ),
                          backgroundColor:
                              WidgetStatePropertyAll(Colors.transparent)),
                      onPressed: () => showDialog(
                          context: context,
                          builder: (BuildContext context) => AlertDialog(
                                title: const Text('Alert'),
                                content: const Text(
                                    'Are you sure you want to logout?'),
                                actions: [
                                  TextButton(
                                      onPressed: () async {
                                        setState(() {
                                          loading = true;
                                        });
                                        await FBAuth.auth.signOut();
                                        if (context.mounted) {
                                          context.go(Routes.signin);
                                        }
                                        setState(() {
                                          loading = false;
                                        });
                                      },
                                      child: const Text('Yes')),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                    },
                                    child: const Text('No'),
                                  )
                                  // => context.pop(), child: const Text('No')),
                                ],
                              )),
                      child: const Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(CupertinoIcons.power,
                              size: 22, color: Colors.black),
                          Padding(
                            padding: EdgeInsets.only(top: 5, bottom: 20),
                            child: Text(
                              'Logout',
                              style: TextStyle(color: Colors.black),
                            ),
                          ),
                        ],
                      ),
                    )
            ],
          ),
        );
      }),
    );
  }
}

class DashHeader extends StatelessWidget {
  const DashHeader({
    super.key,
    this.isTablet = false,
  });
  final bool isTablet;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      height: 120,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (!isTablet)
            SizedBox(
              child: Image.asset(
                'assets/foodcorpimage.png',
                fit: BoxFit.contain,
              ),
            ),
        ],
      ),
    );
  }
}
