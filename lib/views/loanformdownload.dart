import 'package:flutter/material.dart';
import 'package:foodcorp_admin/controller/homectrl.dart';
import 'package:foodcorp_admin/models/loan_model.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class LoanFormDownload extends StatelessWidget {
  final HomeCtrl ctrl;
  final Loan loan;

  const LoanFormDownload({
    super.key,
    required this.ctrl,
    required this.loan,
  });

  @override
  Widget build(BuildContext context) {
    final user = ctrl.users.firstWhereOrNull((u) => u.docId == loan.uid);
    final doOffice = ctrl.districtoffice
        .firstWhereOrNull((d) => d.docId == user?.districtoffice);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Page 1: Application Form
            _buildPageContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                      child: Text(
                          'FOOD CORPORATION OF INDIA EMPLOYEES\' CO-OPERATIVE CREDIT SOCIETY LTD. BARODA.',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16))),
                  const SizedBox(height: 8),
                  Center(
                      child: Text('APPLICATION FOR LONG TERM LOAN',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 14))),
                  const SizedBox(height: 20),
                  _buildRow('Full Name', user?.name ?? ''),
                  _buildRow('Designation', loan.designation ?? ''),
                  _buildRow('Account No.', user?.bankAcNo.toString() ?? ''),
                  _buildRow('Full Address', user?.currentAddress ?? ''),
                  // _buildRow('Permanent/Temporary', user?.employmentType ?? ''),
                  _buildRow(
                      'Loan Applied for Rs.', loan.appliedLoanAmt.toString()),
                  _buildRow(
                      'In words Rs', loan.appliedLoanAmtinWords.toString()),
                  _buildRow('Reason for Loan', loan.loanReason ?? ''),
                  const SizedBox(height: 20),
                  Text('Applicant\'s Signature: ________________________'),
                  const SizedBox(height: 10),
                  Text(
                      'We the undersigned hereby agreed and undertake to stand sureties to:'),
                  const SizedBox(height: 10),
                  _buildRow('Borrower Name', user?.name ?? ''),
                  const SizedBox(height: 10),
                  _buildRow('Surety 1 Name', '____________________'),
                  _buildRow('Surety 1 Account No.', '____________________'),
                  _buildRow('Surety 1 Signature', '____________________'),
                  _buildRow('Surety 2 Name', '____________________'),
                  _buildRow('Surety 2 Account No.', '____________________'),
                  _buildRow('Surety 2 Signature', '____________________'),
                  const SizedBox(height: 20),
                  Text('RESOLUTION:',
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  Text(
                      'Managing Committee of the Food Corporation of India Employees\' Co-operative Credit Society Ltd. Baroda resolved that an amount of Rs.${loan.appliedLoanAmt} in words (Rs.${(loan.appliedLoanAmt)}) has been granted as the Long Term Loan to the applicant today on ${DateFormat('dd-MM-yyyy').format(DateTime.now())}.'),
                  const SizedBox(height: 10),
                  Align(
                      alignment: Alignment.centerRight,
                      child: Text('CHAIRMAN',
                          style: const TextStyle(fontWeight: FontWeight.bold))),
                ],
              ),
            ),

            const SizedBox(height: 40),

            // Page 2: Receipt and Loan Bond / Agreement
            _buildPageContainer(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                      child: Text('RECEIPT',
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16))),
                  const SizedBox(height: 10),
                  Text(
                      'Received Rs.${loan.appliedLoanAmt} in words (Rs.${(loan.appliedLoanAmt)} only) from Food Corporation of India Employees\' Co-operative Credit Society Limited Baroda towards the Long Term Loan today On ${DateFormat('dd-MM-yyyy').format(DateTime.now())}.'),
                  const SizedBox(height: 10),
                  _buildRow('Cash/Cheque No.', '______________________'),
                  _buildRow('Date', '______________________'),
                  _buildRow('Receiver\'s signature', '______________________'),
                  const SizedBox(height: 20),
                  Text('A/c No.: ${user?.bankAcName ?? ""}'),
                  Text('Unit: ${doOffice?.name ?? "-"}'),
                  const SizedBox(height: 20),
                  Text('LOAN BOND / AGREEMENT',
                      style: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16)),
                  const SizedBox(height: 10),
                  Text(
                      'I, Shri/Smt. ${user?.name ?? ""} hereby acknowledge the receipt of loan Rs.${loan.appliedLoanAmt} In words (Rs.${(loan.appliedLoanAmt)} only) from the said society repayable by monthly instalment of Rs.____________ within ______ instalments with interest of 8.5% P.A. as per terms and conditions mentioned in the application and by-laws of the society.'),
                  const SizedBox(height: 10),
                  Text(
                      'Authorise Pay Controlling Authority of Food Corporation of India to deduct on behalf of the Society from my salary or other sums payable to me or nominee or heirs the above mentioned loan amount with interest due.'),
                  const SizedBox(height: 20),
                  _buildRow('Account No.', user?.bankAcNo.toString() ?? ''),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(children: [
                        Text('Witness Signature'),
                        const SizedBox(height: 50),
                        Text('First Surety\'s Signature'),
                        Text('Account No.'),
                      ]),
                      Column(children: [
                        Text('Witness Signature'),
                        const SizedBox(height: 50),
                        Text('Second Surety\'s Signature'),
                        Text('Account No.'),
                      ]),
                      Column(children: [
                        Text('Borrower\'s Signature'),
                        const SizedBox(height: 50),
                        Text('Unit'),
                        Text(
                            'Date: ${DateFormat('dd-MM-yyyy').format(DateTime.now())}'),
                      ]),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
              flex: 3,
              child: Text(label,
                  style: const TextStyle(fontWeight: FontWeight.w600))),
          Expanded(flex: 5, child: Text(value)),
        ],
      ),
    );
  }
}

Widget _buildRow(String label, String value) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4),
    child: Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(label,
              style:
                  const TextStyle(fontWeight: FontWeight.w600, fontSize: 12)),
        ),
        Expanded(
          flex: 5,
          child: Text(value, style: const TextStyle(fontSize: 12)),
        ),
      ],
    ),
  );
}

Widget _buildPageContainer({required Widget child}) {
  return Container(
    width: double.infinity,
    padding: const EdgeInsets.all(24),
    decoration: BoxDecoration(
      color: Colors.white,
      border: Border.all(color: Colors.black, width: 1),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.3),
          blurRadius: 4,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: child,
  );
}
