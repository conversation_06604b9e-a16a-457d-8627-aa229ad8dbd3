import 'dart:ui';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/firebase_options.dart';
import 'package:foodcorp_admin/shared/router.dart';
import 'shared/methods.dart';
import 'shared/theme.dart';

// import 'dart:html' as html;
// void disableBrowserNavigationGestures() {
//   html.window.history.pushState(null, '', html.window.location.href);
//   html.window.onPopState.listen((event) {
//     html.window.history.pushState(null, '', html.window.location.href);
//   });
// }

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  // disableBrowserNavigationGestures();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      scaffoldMessengerKey: snackbarKey,
      theme: themeData,
      debugShowCheckedModeBanner: false,
      routerConfig: appRouter,
      title: "FCI Baroda Credit Soc.",
      scrollBehavior: MyCustomScrollBehavior(),
    );
  }
}

class MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
        PointerDeviceKind.stylus,
        PointerDeviceKind.unknown,
        PointerDeviceKind.trackpad,
      };
}
