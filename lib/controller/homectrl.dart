import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:foodcorp_admin/models/cash_model.dart';
import 'package:foodcorp_admin/models/districtoffice_model.dart';
import 'package:foodcorp_admin/models/expenses_model.dart';
import 'package:foodcorp_admin/models/feedback_model.dart';
import 'package:foodcorp_admin/models/receipt_model.dart';
import 'package:foodcorp_admin/models/recovery_model.dart';
import 'package:foodcorp_admin/models/settings_model.dart';
import 'package:foodcorp_admin/models/subsidiary_ledger_model.dart';
import 'package:foodcorp_admin/models/update_request_model.dart';
import 'package:foodcorp_admin/models/user_monthlyrecord_model.dart';
import 'package:get/get.dart';
import '../models/loan_model.dart';
import '../models/new_user_application_model.dart';
import '../models/recovery_monthly_model.dart';
import '../models/society_yearlyrecord_model.dart';
import '../models/transaction_model.dart';
import '../models/user_model.dart';
import '../shared/firebase.dart';

class HomeCtrl extends GetxController {
  SettingsModel? settings;
  List<CashModel> cashModel = <CashModel>[];
  List<DistrictOfficeModel> districtoffice = <DistrictOfficeModel>[];
  List<Expense> expenses = <Expense>[];
  List<UserModel> users = <UserModel>[];
  List<InputUserModel> inputUsers = <InputUserModel>[];
  List<NewUserApplicationModel> newusersapplication =
      <NewUserApplicationModel>[];
  List<NewUserApplicationModel> usersapplicationrejected =
      <NewUserApplicationModel>[];
  List<Loan> newLoan = <Loan>[];
  List<Loan> loan = <Loan>[];
  List<RecoveryModel> recovery = <RecoveryModel>[];
  List<RecoveryMonthlyModel> recoverymonthly = <RecoveryMonthlyModel>[];
  List<UserMonthlyRecordModel> usermonthly = <UserMonthlyRecordModel>[];
  List<ReceiptModel> receipt = <ReceiptModel>[];
  List<SocietyYearlyRecordModel> societyyearly = <SocietyYearlyRecordModel>[];

  List<TransactionModel> transactions = <TransactionModel>[];
  List<SubsidiaryLedgerModel> subsidiaryLedger = <SubsidiaryLedgerModel>[];

  List<UpdateRequestModel> updateRequests = <UpdateRequestModel>[];

  List<FeedbackModel> feedbacks = <FeedbackModel>[];

  bool cashdataloaded = false;
  num cashbalance = 0;
  num cashdeposit = 0;

  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? setsStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? cashStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? expenseStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      districtofficeStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? usersStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? newusersStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? recoverysStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      recoverymonthlyStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? usermonthlyStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? receiptStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
      societyyearlyStream;

  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? transactionsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? updateRequestsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? feedbacksStream;

  @override
  void onInit() {
    super.onInit();
    getdistrictofficedata();
    getsettingsdata();
    getexpensedata();
    getusersdata();
    getloandata();
    getnewuserapplication();
    getrecoverydata();
    getrecoverymonthlydata();
    getusermonthlydata();
    getreceiptdata();
    getsocietyyearlydata();
    getcashdata();
    gettransactionsdata();
    getsubsidiaryledgerdata();
    getrequestsdata();
    getfeedbacksdata();
  }

  @override
  void onClose() {
    setsStream?.cancel();
    cashStream?.cancel();
    expenseStream?.cancel();
    districtofficeStream?.cancel();
    usersStream?.cancel();
    newusersStream?.cancel();
    recoverysStream?.cancel();
    recoverymonthlyStream?.cancel();
    usermonthlyStream?.cancel();
    receiptStream?.cancel();
    societyyearlyStream?.cancel();
    transactionsStream?.cancel();
    updateRequestsStream?.cancel();
    feedbacksStream?.cancel();

    super.onClose();
  }

  void getfeedbacksdata() {
    try {
      FBFireStore.feedback.snapshots().listen(
        (event) {
          feedbacks = event.docs.map(
            (e) {
              return FeedbackModel.fromSnap(e);
            },
          ).toList();
          feedbacks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          // print("No. of subsidiary ledger in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getsubsidiaryledgerdata() {
    try {
      FBFireStore.subsidiaryledger.snapshots().listen(
        (event) {
          subsidiaryLedger = event.docs.map(
            (e) {
              return SubsidiaryLedgerModel.fromSnap(e);
            },
          ).toList();
          // print("No. of subsidiary ledger in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getcashdata() {
    try {
      cashdataloaded = false;
      update();
      FBFireStore.cash.snapshots().listen(
        (event) {
          cashModel = event.docs.map(
            (e) {
              return CashModel.fromSnap(e);
            },
          ).toList();
          cashbalance = 0;
          for (var element in societyyearly) {
            cashbalance += element.cashBalance;
            // cashdeposit += element.amount;
          }
          cashdataloaded = true;
          update();
          // print("No. of cash docs in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      cashdataloaded = false;
      update();
      debugPrint(e.toString());
    }
  }

  void getsocietyyearlydata() {
    try {
      FBFireStore.societyYearly.snapshots().listen(
        (event) {
          societyyearly = event.docs.map(
            (e) {
              return SocietyYearlyRecordModel.fromSnap(e);
            },
          ).toList();
          // print("No. of society yearly in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getreceiptdata() {
    try {
      FBFireStore.receipt.snapshots().listen(
        (event) {
          receipt = event.docs.map(
            (e) {
              return ReceiptModel.fromSnap(e);
            },
          ).toList();
          // print("No. of receipts in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getusermonthlydata() {
    try {
      FBFireStore.usermonthly.snapshots().listen(
        (event) {
          usermonthly = event.docs.map(
            (e) {
              return UserMonthlyRecordModel.fromSnap(e);
            },
          ).toList();
          // print("No. of usermonthly in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getrecoverymonthlydata() {
    try {
      FBFireStore.recoverymonthly.snapshots().listen(
        (event) {
          recoverymonthly = event.docs.map(
            (e) {
              return RecoveryMonthlyModel.fromSnap(e);
            },
          ).toList();
          // print("No. of recoverymonthly in ctrl : ${event.size}");
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getrecoverydata() {
    try {
      FBFireStore.recovery.snapshots().listen((event) {
        recovery = event.docs.map(
          (e) {
            return RecoveryModel.fromSnap(e);
          },
        ).toList();
        // print("No. of recovery in ctrl : ${event.size}");
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getloandata() {
    try {
      FBFireStore.loan
          .orderBy('appliedOn', descending: false)
          .snapshots()
          .listen((event) {
        // print("No. o f loan : ${event.size}");
        final loandata = event.docs.map((e) {
          return Loan.fromSnap(e);
        }).toList();
        newLoan = loandata.where((element) => element.isNew).toList();
        loan = loandata.where((element) => !(element.isNew)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getnewuserapplication() {
    try {
      FBFireStore.newuserapplication
          // .where("isNew", isEqualTo: true)
          // .where("approved", isEqualTo: false)
          .snapshots()
          .listen(
        (event) {
          // print("No. of new users application : ${event.size}");
          final usersapplication = event.docs.map(
            (e) {
              return NewUserApplicationModel.fromSnap(e);
            },
          ).toList();
          newusersapplication =
              usersapplication.where((element) => element.isNew).toList();
          usersapplicationrejected = usersapplication
              .where((element) => (!element.isNew) && (!element.approved))
              .toList();
          update();
        },
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getusersdata() async {
    try {
      FBFireStore.users
          .where("approved", isEqualTo: true)
          .snapshots()
          .listen((event) {
        // print("No. of users : ${event.size}");
        inputUsers = event.docs.map((e) {
          return InputUserModel.fromSnap(e);
        }).toList();
        users = event.docs.map((e) {
          return UserModel.fromSnap(e);
        }).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getdistrictofficedata() async {
    try {
      FBFireStore.districtoffice
          // .limit()
          .snapshots()
          .listen((event) {
        // print(event.size);
        districtoffice =
            event.docs.map((e) => DistrictOfficeModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getexpensedata() async {
    try {
      FBFireStore.expense.snapshots().listen((event) {
        expenses = event.docs.map((e) => Expense.fromSnap(e)).toList();
        update();
        // print("event.size : ${event.size}");
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getsettingsdata() async {
    try {
      setsStream?.cancel();
      setsStream = FBFireStore.settings.snapshots().listen((event) {
        if (event.exists) {
          settings = SettingsModel.fromSnap(event);
        } else {
          settings = null;
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void gettransactionsdata() {
    try {
      transactionsStream?.cancel();
      transactionsStream = FBFireStore.transactions.snapshots().listen((event) {
        transactions =
            event.docs.map((e) => TransactionModel.fromSnap(e)).toList();
        update();
        // print("T.docs.length : ${event.docs.length}");
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getrequestsdata() {
    try {
      updateRequestsStream?.cancel();
      updateRequestsStream = FBFireStore.requests.snapshots().listen((event) {
        updateRequests =
            event.docs.map((e) => UpdateRequestModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
