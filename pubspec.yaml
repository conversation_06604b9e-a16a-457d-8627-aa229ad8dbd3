name: foodcorp_admin
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.4.3 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  get: ^4.6.6
  cloud_firestore: ^6.0.0
  cloud_functions: ^6.0.0
  firebase_auth: ^6.0.1
  firebase_storage: ^13.0.0
  firebase_core: ^4.0.0
  firebase_ui_auth: ^3.0.0
  get_storage: ^2.1.1
  google_fonts: ^6.2.1
  intl: ^0.19.0
  go_router: ^16.0.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_date_range_picker: ^0.2.0
  signature: ^6.0.0
  flutter_pdfview: ^1.3.4
  file_saver: ^0.3.1
  url_launcher: ^6.3.1
  http: ^1.2.2
  dropdown_search: ^6.0.1
  # pluto_grid: ^8.0.0
  # pluto_grid_plus: ^8.4.14
  pluto_grid: 
      git:
        url: https://github.com/Mohammadwabeel/pluto_grid.git

  excel: ^4.0.0
  pdf: ^3.6.0
  pdf_image_renderer: ^1.0.1
  path_provider: ^2.1.5
  permission_handler: ^12.0.0+1
  file_picker: ^10.1.1
  flutter_image_compress: ^2.4.0
  image_picker: ^1.0.7
  csv: ^6.0.0
  qr_flutter: ^4.0.0
  fluttertoast: ^8.2.4
  flashy_flushbar: ^1.4.0
  # html: ^0.16.0

  

dependency_overrides:
  intl: 0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/foodcorpimage.png
    - assets/image.png
    # - assets/Noto_Sans/NotoSans-Italic-VariableFont_wdth,wght.ttf
    # - assets/fonts/NotoSans-Bold.ttf
    # - assets/images/
    # - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
